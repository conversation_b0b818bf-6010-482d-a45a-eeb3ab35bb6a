/**
 * SEO Analysis Tool for Content Optimization
 * Provides keyword research, SERP analysis, and content optimization recommendations
 */

export class SEOAnalyzer {
  constructor() {
    this.stopWords = new Set([
      'a', 'an', 'and', 'are', 'as', 'at', 'be', 'by', 'for', 'from',
      'has', 'he', 'in', 'is', 'it', 'its', 'of', 'on', 'that', 'the',
      'to', 'was', 'will', 'with', 'how', 'what', 'when', 'where', 'why'
    ]);
  }

  /**
   * Analyze content topic for SEO opportunities
   * @param {string} topic - Main content topic
   * @param {Object} researchData - Research data from Tavily search
   * @returns {Object} SEO analysis and recommendations
   */
  analyzeTopic(topic, researchData) {
    try {
      // Extract keywords from research data
      const extractedKeywords = this.extractKeywords(researchData);
      
      // Generate semantic keywords
      const semanticKeywords = this.generateSemanticKeywords(topic);
      
      // Analyze competitor keywords
      const competitorKeywords = this.analyzeCompetitorKeywords(researchData);
      
      // Generate content structure recommendations
      const contentStructure = this.recommendContentStructure(topic, extractedKeywords);
      
      // Calculate keyword difficulty estimates
      const keywordAnalysis = this.analyzeKeywordDifficulty(extractedKeywords);

      return {
        primaryTopic: topic,
        keywords: {
          primary: this.identifyPrimaryKeywords(topic, extractedKeywords),
          secondary: this.identifySecondaryKeywords(extractedKeywords),
          longTail: this.identifyLongTailKeywords(extractedKeywords),
          semantic: semanticKeywords,
          competitor: competitorKeywords
        },
        contentStructure,
        seoRecommendations: this.generateSEORecommendations(topic, extractedKeywords),
        keywordAnalysis,
        optimizationScore: this.calculateOptimizationPotential(keywordAnalysis),
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error('SEO Analysis Error:', error);
      throw new Error(`SEO analysis failed: ${error.message}`);
    }
  }

  /**
   * Extract keywords from research data
   * @param {Object} researchData - Research data from search results
   * @returns {Array} Extracted keywords with frequency
   */
  extractKeywords(researchData) {
    const keywords = new Map();
    
    try {
      // Process main results
      if (researchData.mainResults) {
        researchData.mainResults.forEach(result => {
          if (result.title) this.processText(result.title, keywords);
          if (result.content) this.processText(result.content, keywords);
          if (result.snippet) this.processText(result.snippet, keywords);
        });
      }

      // Process related results
      if (researchData.relatedResults) {
        researchData.relatedResults.forEach(related => {
          if (related.results) {
            related.results.forEach(result => {
              if (result.title) this.processText(result.title, keywords);
              if (result.content) this.processText(result.content, keywords);
              if (result.snippet) this.processText(result.snippet, keywords);
            });
          }
        });
      }

      // Convert to array and sort by frequency
      return Array.from(keywords.entries())
        .map(([keyword, count]) => ({ keyword, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 50); // Top 50 keywords

    } catch (error) {
      console.error('Keyword extraction error:', error);
      return [];
    }
  }

  /**
   * Process text for keyword extraction
   * @param {string} text - Text to process
   * @param {Map} keywords - Keywords map to update
   */
  processText(text, keywords) {
    if (!text || typeof text !== 'string') return;

    // Clean and tokenize text
    const tokens = text
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(token => 
        token.length > 2 && 
        !this.stopWords.has(token) && 
        !/^\d+$/.test(token)
      );

    // Count single words
    tokens.forEach(token => {
      keywords.set(token, (keywords.get(token) || 0) + 1);
    });

    // Count 2-word phrases
    for (let i = 0; i < tokens.length - 1; i++) {
      const phrase = `${tokens[i]} ${tokens[i + 1]}`;
      keywords.set(phrase, (keywords.get(phrase) || 0) + 1);
    }

    // Count 3-word phrases
    for (let i = 0; i < tokens.length - 2; i++) {
      const phrase = `${tokens[i]} ${tokens[i + 1]} ${tokens[i + 2]}`;
      keywords.set(phrase, (keywords.get(phrase) || 0) + 1);
    }
  }

  /**
   * Generate semantic keywords related to the main topic
   * @param {string} topic - Main topic
   * @returns {Array} Semantic keywords
   */
  generateSemanticKeywords(topic) {
    const semanticPatterns = [
      `best ${topic}`,
      `how to ${topic}`,
      `${topic} guide`,
      `${topic} tips`,
      `${topic} benefits`,
      `${topic} vs`,
      `${topic} examples`,
      `${topic} tools`,
      `${topic} strategies`,
      `${topic} trends`,
      `${topic} best practices`,
      `${topic} for beginners`,
      `advanced ${topic}`,
      `${topic} case study`,
      `${topic} review`
    ];

    return semanticPatterns.map(pattern => ({
      keyword: pattern,
      intent: this.determineSearchIntent(pattern),
      difficulty: this.estimateKeywordDifficulty(pattern)
    }));
  }

  /**
   * Analyze competitor keywords
   * @param {Object} researchData - Research data
   * @returns {Array} Competitor keyword analysis
   */
  analyzeCompetitorKeywords(researchData) {
    const competitorKeywords = [];
    
    try {
      if (researchData.mainResults) {
        researchData.mainResults.forEach(result => {
          if (result.url && result.title) {
            const domain = new URL(result.url).hostname;
            const keywords = this.extractKeywordsFromText(result.title + ' ' + (result.snippet || ''));
            
            competitorKeywords.push({
              domain,
              url: result.url,
              title: result.title,
              keywords: keywords.slice(0, 10) // Top 10 keywords per competitor
            });
          }
        });
      }
    } catch (error) {
      console.error('Competitor keyword analysis error:', error);
    }

    return competitorKeywords;
  }

  /**
   * Extract keywords from text
   * @param {string} text - Text to analyze
   * @returns {Array} Extracted keywords
   */
  extractKeywordsFromText(text) {
    const keywords = new Map();
    this.processText(text, keywords);
    
    return Array.from(keywords.entries())
      .map(([keyword, count]) => ({ keyword, count }))
      .sort((a, b) => b.count - a.count);
  }

  /**
   * Recommend content structure based on keywords
   * @param {string} topic - Main topic
   * @param {Array} keywords - Extracted keywords
   * @returns {Object} Content structure recommendations
   */
  recommendContentStructure(topic, keywords) {
    const topKeywords = keywords.slice(0, 15);
    
    return {
      title: `${topic}: Complete Guide and Best Practices`,
      headings: [
        {
          level: 'H1',
          text: `The Ultimate Guide to ${topic}`,
          keywords: [topic]
        },
        {
          level: 'H2', 
          text: `What is ${topic}?`,
          keywords: [topic, 'definition', 'basics']
        },
        {
          level: 'H2',
          text: `Benefits of ${topic}`,
          keywords: [topic, 'benefits', 'advantages']
        },
        {
          level: 'H2',
          text: `How to Get Started with ${topic}`,
          keywords: [topic, 'getting started', 'beginners']
        },
        {
          level: 'H2',
          text: `Best Practices for ${topic}`,
          keywords: [topic, 'best practices', 'tips']
        },
        {
          level: 'H2',
          text: `Common ${topic} Challenges and Solutions`,
          keywords: [topic, 'challenges', 'problems', 'solutions']
        },
        {
          level: 'H2',
          text: `Tools and Resources for ${topic}`,
          keywords: [topic, 'tools', 'resources']
        },
        {
          level: 'H2',
          text: `Future of ${topic}`,
          keywords: [topic, 'future', 'trends']
        }
      ],
      sections: topKeywords.slice(0, 8).map(kw => ({
        heading: `${kw.keyword.charAt(0).toUpperCase() + kw.keyword.slice(1)} in ${topic}`,
        keywords: [kw.keyword, topic],
        estimatedWords: 200
      }))
    };
  }

  /**
   * Identify primary keywords
   * @param {string} topic - Main topic
   * @param {Array} keywords - All extracted keywords
   * @returns {Array} Primary keywords
   */
  identifyPrimaryKeywords(topic, keywords) {
    return keywords
      .filter(kw => kw.keyword.includes(topic.toLowerCase()) || kw.count > 5)
      .slice(0, 5)
      .map(kw => ({
        ...kw,
        intent: this.determineSearchIntent(kw.keyword),
        difficulty: this.estimateKeywordDifficulty(kw.keyword)
      }));
  }

  /**
   * Identify secondary keywords
   * @param {Array} keywords - All extracted keywords
   * @returns {Array} Secondary keywords
   */
  identifySecondaryKeywords(keywords) {
    return keywords
      .filter(kw => kw.count >= 3 && kw.count <= 8)
      .slice(0, 10)
      .map(kw => ({
        ...kw,
        intent: this.determineSearchIntent(kw.keyword),
        difficulty: this.estimateKeywordDifficulty(kw.keyword)
      }));
  }

  /**
   * Identify long-tail keywords
   * @param {Array} keywords - All extracted keywords
   * @returns {Array} Long-tail keywords
   */
  identifyLongTailKeywords(keywords) {
    return keywords
      .filter(kw => kw.keyword.split(' ').length >= 3 && kw.count >= 2)
      .slice(0, 15)
      .map(kw => ({
        ...kw,
        intent: this.determineSearchIntent(kw.keyword),
        difficulty: this.estimateKeywordDifficulty(kw.keyword)
      }));
  }

  /**
   * Determine search intent for keyword
   * @param {string} keyword - Keyword to analyze
   * @returns {string} Search intent category
   */
  determineSearchIntent(keyword) {
    const informationalWords = ['what', 'how', 'why', 'guide', 'tutorial', 'tips', 'learn'];
    const commercialWords = ['best', 'top', 'review', 'compare', 'vs'];
    const transactionalWords = ['buy', 'price', 'cost', 'discount', 'deal'];
    
    const lowerKeyword = keyword.toLowerCase();
    
    if (informationalWords.some(word => lowerKeyword.includes(word))) {
      return 'informational';
    }
    if (commercialWords.some(word => lowerKeyword.includes(word))) {
      return 'commercial';
    }
    if (transactionalWords.some(word => lowerKeyword.includes(word))) {
      return 'transactional';
    }
    
    return 'navigational';
  }

  /**
   * Estimate keyword difficulty
   * @param {string} keyword - Keyword to analyze
   * @returns {string} Difficulty level
   */
  estimateKeywordDifficulty(keyword) {
    const wordCount = keyword.split(' ').length;
    
    if (wordCount >= 4) return 'Low';
    if (wordCount === 3) return 'Medium';
    if (wordCount === 2) return 'Medium-High';
    return 'High';
  }

  /**
   * Analyze keyword difficulty for extracted keywords
   * @param {Array} keywords - Keywords to analyze
   * @returns {Object} Keyword difficulty analysis
   */
  analyzeKeywordDifficulty(keywords) {
    const analysis = {
      low: keywords.filter(kw => this.estimateKeywordDifficulty(kw.keyword) === 'Low').length,
      medium: keywords.filter(kw => this.estimateKeywordDifficulty(kw.keyword) === 'Medium').length,
      high: keywords.filter(kw => this.estimateKeywordDifficulty(kw.keyword) === 'High').length,
      total: keywords.length
    };

    return {
      ...analysis,
      distribution: {
        low: (analysis.low / analysis.total * 100).toFixed(1),
        medium: (analysis.medium / analysis.total * 100).toFixed(1),
        high: (analysis.high / analysis.total * 100).toFixed(1)
      }
    };
  }

  /**
   * Generate SEO recommendations
   * @param {string} topic - Main topic
   * @param {Array} keywords - Extracted keywords
   * @returns {Array} SEO recommendations
   */
  generateSEORecommendations(topic, keywords) {
    return [
      {
        category: 'Content Strategy',
        recommendation: `Focus on long-tail keywords related to "${topic}" for easier ranking opportunities`,
        impact: 'High'
      },
      {
        category: 'Keyword Density',
        recommendation: `Maintain 1-2% keyword density for primary keywords while ensuring natural language flow`,
        impact: 'Medium'
      },
      {
        category: 'Content Structure',
        recommendation: `Use topic clusters to create comprehensive content covering all related subtopics`,
        impact: 'High'
      },
      {
        category: 'User Intent',
        recommendation: `Address multiple search intents (informational, commercial, transactional) within the content`,
        impact: 'High'
      },
      {
        category: 'Semantic SEO',
        recommendation: `Include semantically related terms and concepts to improve topical authority`,
        impact: 'Medium'
      },
      {
        category: 'Content Freshness',
        recommendation: `Include current trends and recent developments to signal content freshness`,
        impact: 'Medium'
      }
    ];
  }

  /**
   * Calculate optimization potential score
   * @param {Object} keywordAnalysis - Keyword difficulty analysis
   * @returns {number} Optimization score (0-100)
   */
  calculateOptimizationPotential(keywordAnalysis) {
    const lowDifficultyWeight = 0.4;
    const mediumDifficultyWeight = 0.3;
    const highDifficultyWeight = 0.1;

    const score = 
      (keywordAnalysis.distribution.low * lowDifficultyWeight) +
      (keywordAnalysis.distribution.medium * mediumDifficultyWeight) +
      (keywordAnalysis.distribution.high * highDifficultyWeight);

    return Math.round(Math.min(100, Math.max(0, score)));
  }
}

// Export singleton instance
export const seoAnalyzer = new SEOAnalyzer();

// KaibanJS tool configuration
export const seoTool = {
  name: 'seo_analyzer',
  description: 'Comprehensive SEO analysis tool for keyword research, content structure optimization, and search intent analysis.',
  
  async call(topic, researchData) {
    return seoAnalyzer.analyzeTopic(topic, researchData);
  },
  
  // KaibanJS tool interface
  func: async (topic, researchData) => {
    const result = seoAnalyzer.analyzeTopic(topic, researchData);
    return JSON.stringify(result, null, 2);
  }
};