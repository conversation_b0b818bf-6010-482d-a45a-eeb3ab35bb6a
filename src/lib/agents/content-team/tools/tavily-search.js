/**
 * Enhanced Tavily Search Tool for Content Research
 * Provides comprehensive web research capabilities for content agents
 */

import { TavilySearchResults } from '@langchain/community/tools/tavily_search';

export class EnhancedTavilySearch {
  constructor(apiKey) {
    this.apiKey = apiKey || process.env.NEXT_PUBLIC_TAVILY_API_KEY;
    this.searchTool = new TavilySearchResults({
      maxResults: 8,
      apiKey: this.apiKey
    });
  }

  /**
   * Perform comprehensive research on a topic
   * @param {string} query - Search query
   * @param {Object} options - Search options
   * @returns {Promise<Object>} Structured research results
   */
  async researchTopic(query, options = {}) {
    try {
      const {
        maxResults = 8,
        includeRawContent = false,
        searchDepth = 'advanced',
        includeImages = false
      } = options;

      // Perform main search
      const mainResults = await this.searchTool.call(query);
      
      // Parse results
      const searchResults = this.parseSearchResults(mainResults);
      
      // Get related queries for comprehensive coverage
      const relatedQueries = this.generateRelatedQueries(query);
      const relatedResults = await this.searchRelatedQueries(relatedQueries.slice(0, 2));

      return {
        query,
        mainResults: searchResults,
        relatedResults,
        summary: this.generateSearchSummary(searchResults, relatedResults),
        totalSources: searchResults.length + relatedResults.reduce((acc, r) => acc + r.results.length, 0),
        searchDepth,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error('Enhanced Tavily Search Error:', error);
      throw new Error(`Research failed: ${error.message}`);
    }
  }

  /**
   * Search for competitor content
   * @param {string} topic - Topic to analyze
   * @param {Array} competitors - List of competitor domains
   * @returns {Promise<Object>} Competitor analysis results
   */
  async analyzeCompetitors(topic, competitors = []) {
    try {
      const competitorResults = {};
      
      for (const competitor of competitors) {
        const query = `site:${competitor} ${topic}`;
        const results = await this.searchTool.call(query);
        competitorResults[competitor] = this.parseSearchResults(results);
      }

      // General competitor research if no specific competitors provided
      if (competitors.length === 0) {
        const genericQuery = `"${topic}" top articles ranking`;
        const results = await this.searchTool.call(genericQuery);
        competitorResults['general'] = this.parseSearchResults(results);
      }

      return {
        topic,
        competitors: competitorResults,
        analysis: this.generateCompetitorAnalysis(competitorResults),
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error('Competitor Analysis Error:', error);
      throw new Error(`Competitor analysis failed: ${error.message}`);
    }
  }

  /**
   * Search for trending topics and current events
   * @param {string} baseQuery - Base topic
   * @returns {Promise<Object>} Trending information
   */
  async findTrending(baseQuery) {
    try {
      const trendingQueries = [
        `${baseQuery} 2024 trends`,
        `${baseQuery} latest news`,
        `${baseQuery} recent developments`,
        `what's new in ${baseQuery}`,
        `${baseQuery} industry updates`
      ];

      const trendingResults = await Promise.all(
        trendingQueries.map(async (query) => {
          const results = await this.searchTool.call(query);
          return {
            query,
            results: this.parseSearchResults(results)
          };
        })
      );

      return {
        baseQuery,
        trendingData: trendingResults,
        summary: this.generateTrendingSummary(trendingResults),
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error('Trending Search Error:', error);
      throw new Error(`Trending search failed: ${error.message}`);
    }
  }

  /**
   * Parse and structure search results
   * @param {string} rawResults - Raw search results from Tavily
   * @returns {Array} Parsed and structured results
   */
  parseSearchResults(rawResults) {
    try {
      if (typeof rawResults === 'string') {
        // Parse the results string if needed
        const results = JSON.parse(rawResults);
        return results;
      }
      
      return Array.isArray(rawResults) ? rawResults : [];
    } catch (error) {
      console.error('Error parsing search results:', error);
      return [];
    }
  }

  /**
   * Generate related search queries
   * @param {string} originalQuery - Original search query
   * @returns {Array} Array of related queries
   */
  generateRelatedQueries(originalQuery) {
    const queryVariations = [
      `${originalQuery} best practices`,
      `${originalQuery} tips techniques`,
      `${originalQuery} guide tutorial`,
      `${originalQuery} benefits advantages`,
      `${originalQuery} challenges problems`,
      `how to ${originalQuery}`,
      `${originalQuery} vs alternatives`,
      `${originalQuery} case study examples`
    ];

    return queryVariations;
  }

  /**
   * Search related queries for comprehensive coverage
   * @param {Array} queries - Array of related queries
   * @returns {Promise<Array>} Results for related queries
   */
  async searchRelatedQueries(queries) {
    try {
      const relatedResults = await Promise.all(
        queries.map(async (query) => {
          const results = await this.searchTool.call(query);
          return {
            query,
            results: this.parseSearchResults(results)
          };
        })
      );

      return relatedResults;
    } catch (error) {
      console.error('Related queries search error:', error);
      return [];
    }
  }

  /**
   * Generate search summary
   * @param {Array} mainResults - Main search results
   * @param {Array} relatedResults - Related search results
   * @returns {string} Search summary
   */
  generateSearchSummary(mainResults, relatedResults) {
    const totalResults = mainResults.length + relatedResults.reduce((acc, r) => acc + r.results.length, 0);
    
    return `Found ${totalResults} sources across ${relatedResults.length + 1} different search queries. ` +
           `Main query returned ${mainResults.length} primary sources. ` +
           `Related searches provided additional context and supporting information.`;
  }

  /**
   * Generate competitor analysis summary
   * @param {Object} competitorResults - Competitor search results
   * @returns {string} Analysis summary
   */
  generateCompetitorAnalysis(competitorResults) {
    const competitors = Object.keys(competitorResults);
    const totalResults = Object.values(competitorResults).reduce((acc, results) => acc + results.length, 0);
    
    return `Analyzed ${competitors.length} competitor sources with ${totalResults} total results. ` +
           `Found content patterns and opportunities for differentiation.`;
  }

  /**
   * Generate trending summary
   * @param {Array} trendingResults - Trending search results
   * @returns {string} Trending summary
   */
  generateTrendingSummary(trendingResults) {
    const totalResults = trendingResults.reduce((acc, tr) => acc + tr.results.length, 0);
    
    return `Identified current trends from ${trendingResults.length} trending searches with ${totalResults} results. ` +
           `Found latest developments and emerging topics in the field.`;
  }
}

// Export singleton instance
export const tavilySearch = new EnhancedTavilySearch();

// KaibanJS tool configuration
export const researchTool = {
  name: 'enhanced_tavily_search',
  description: 'Comprehensive web research tool with competitor analysis, trend identification, and structured data extraction.',
  
  async call(query, options = {}) {
    return await tavilySearch.researchTopic(query, options);
  },
  
  // KaibanJS tool interface
  func: async (query, options = {}) => {
    const result = await tavilySearch.researchTopic(query, options);
    return JSON.stringify(result, null, 2);
  }
};