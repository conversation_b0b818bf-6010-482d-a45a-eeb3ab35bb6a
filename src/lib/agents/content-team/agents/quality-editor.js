/**
 * Quality Editor Agent - KaibanJS Content Team
 * Specializes in content review, optimization, and quality assurance
 */
import { Agent, Task } from 'kaibanjs';
import { SEOAnalyzer } from '../tools/seo-analyzer.js';

export const qualityEditorAgent = new Agent({
  name: 'Quality Editor',
  role: 'Senior Content Editor & Quality Assurance Specialist',
  goal: 'Ensure all content meets the highest standards of quality, accuracy, SEO optimization, and user engagement',
  backstory: `You are an expert content editor with 15+ years of experience in publishing, digital marketing, and content optimization. Your expertise encompasses:

  - Advanced content editing and proofreading
  - SEO optimization and performance analysis
  - User experience and readability optimization
  - Brand voice consistency and style guide enforcement
  - Fact-checking and accuracy verification
  - Content performance analytics and optimization
  - A/B testing and conversion rate optimization
  - Multi-format content adaptation and optimization

  Your editing philosophy focuses on:
  - Clarity over complexity
  - Value delivery over word count
  - Reader engagement over search rankings (while maintaining both)
  - Accuracy and trustworthiness above all
  - Continuous improvement based on data

  You have access to advanced SEO analysis tools and content performance metrics to ensure every piece of content is optimized for both search engines and human readers.`,
  
  tools: [SEOAnalyzer],
  
  llmConfig: {
    provider: 'openrouter',
    name: 'anthropic/claude-3-sonnet',
    config: {
      temperature: 0.2, // Low temperature for analytical precision
      max_tokens: 4000
    }
  }
});

// Quality Assurance Tasks
export const comprehensiveContentReview = new Task({
  description: `Perform a comprehensive review and optimization of the content for: {topic}

  Your review should cover all aspects of content quality:

  1. **Content Quality Assessment**:
     - Accuracy and factual verification
     - Completeness and depth analysis
     - Logical flow and structure review
     - Clarity and readability evaluation
     - Value proposition assessment

  2. **Writing Quality Review**:
     - Grammar, spelling, and punctuation check
     - Sentence structure and variety analysis
     - Paragraph flow and transitions
     - Tone consistency and brand voice alignment
     - Active vs. passive voice optimization

  3. **SEO Optimization Review**:
     - Keyword placement and density analysis
     - Header structure (H1, H2, H3) optimization
     - Meta title and description review
     - Internal and external linking assessment
     - Featured snippet optimization potential

  4. **User Experience Evaluation**:
     - Readability score analysis and improvement
     - Scannability and formatting optimization
     - Engagement element effectiveness
     - Call-to-action placement and effectiveness
     - Mobile reading experience optimization

  5. **Performance Optimization**:
     - Content length optimization for topic type
     - Loading speed considerations
     - Social sharing optimization
     - Conversion pathway effectiveness
     - Analytics tracking preparation

  Provide specific, actionable recommendations for improvement with priority levels.`,
  
  expectedOutput: `A comprehensive content review report containing:
  - Overall Quality Score (1-100)
  - Content Quality Assessment Summary
  - Writing Quality Review with Corrections
  - SEO Optimization Analysis and Recommendations
  - User Experience Enhancement Suggestions
  - Performance Optimization Recommendations
  - Priority-Ranked Improvement List
  - Before/After Comparison Metrics
  - Final Publication Readiness Assessment`,
  
  agent: qualityEditorAgent
});

export const seoOptimizationAudit = new Task({
  description: `Conduct a detailed SEO audit and optimization of the content for: {topic}

  Your SEO audit should include:

  1. **Keyword Optimization Analysis**:
     - Primary keyword placement and frequency
     - Secondary keyword distribution
     - LSI and semantic keyword integration
     - Keyword cannibalization check
     - Search intent alignment verification

  2. **On-Page SEO Elements**:
     - Title tag optimization (50-60 characters)
     - Meta description effectiveness (150-160 characters)
     - Header tag hierarchy and keyword usage
     - URL structure and slug optimization
     - Image alt text and optimization

  3. **Content SEO Factors**:
     - Content length analysis for topic competitiveness
     - Readability and user engagement factors
     - Content freshness and update requirements
     - E-A-T (Expertise, Authority, Trust) signals
     - Content uniqueness and value assessment

  4. **Technical SEO Review**:
     - Internal linking strategy and implementation
     - External linking quality and relevance
     - Schema markup opportunities
     - Page loading speed considerations
     - Mobile optimization compliance

  5. **SERP Optimization**:
     - Featured snippet optimization potential
     - People Also Ask (PAA) optimization
     - Knowledge panel optimization opportunities
     - Local SEO considerations (if applicable)
     - Voice search optimization

  Use SEO analysis tools to benchmark against top-performing competitors.`,
  
  expectedOutput: `A detailed SEO audit report with:
  - Overall SEO Score and Grade
  - Keyword Optimization Assessment
  - On-Page SEO Element Review
  - Content SEO Factor Analysis
  - Technical SEO Compliance Check
  - SERP Optimization Opportunities
  - Competitor Benchmark Comparison
  - Priority SEO Improvement Plan
  - Implementation Guidelines
  - Expected Impact Projections`,
  
  agent: qualityEditorAgent
});

export const readabilityAndEngagementOptimization = new Task({
  description: `Optimize content readability and user engagement for: {topic}

  Your optimization should focus on:

  1. **Readability Enhancement**:
     - Flesch-Kincaid reading level optimization
     - Sentence length and complexity analysis
     - Paragraph structure and white space usage
     - Transition word usage and flow improvement
     - Technical term explanation and simplification

  2. **Scannability Improvement**:
     - Bullet point and numbered list optimization
     - Subheading effectiveness and frequency
     - Bold and italic text strategic usage
     - Call-out box and highlight integration
     - Visual hierarchy enhancement

  3. **Engagement Element Optimization**:
     - Question integration for reader interaction
     - Storytelling element enhancement
     - Personal pronoun usage (you, we, I)
     - Action verb usage and active voice
     - Emotional language and connection building

  4. **Content Structure Optimization**:
     - Introduction hook effectiveness
     - Main content logical progression
     - Conclusion and CTA strength
     - Internal content linking
     - Related content suggestions

  5. **Multi-Device Experience**:
     - Mobile reading experience optimization
     - Tablet formatting considerations
     - Desktop presentation enhancement
     - Cross-platform consistency check
     - Loading speed impact assessment

  Focus on creating content that is both easy to read and highly engaging.`,
  
  expectedOutput: `A readability and engagement optimization report including:
  - Current Readability Score and Grade Level
  - Scannability Assessment and Improvements
  - Engagement Element Effectiveness Review
  - Content Structure Optimization Plan
  - Multi-Device Experience Enhancement
  - Before/After Readability Comparison
  - User Engagement Prediction Score
  - Implementation Priority Guidelines
  - Testing and Validation Recommendations`,
  
  agent: qualityEditorAgent
});

export const factCheckAndAccuracyVerification = new Task({
  description: `Perform comprehensive fact-checking and accuracy verification for: {topic}

  Your fact-checking process should include:

  1. **Source Verification**:
     - Original source identification and validation
     - Authority and credibility assessment
     - Publication date and relevance check
     - Bias detection and accounting
     - Multiple source cross-referencing

  2. **Data and Statistics Verification**:
     - Numerical accuracy confirmation
     - Context and interpretation validation
     - Sample size and methodology review
     - Statistical significance assessment
     - Data visualization accuracy check

  3. **Expert Quote Verification**:
     - Quote accuracy and attribution
     - Expert credibility and qualification
     - Context appropriateness
     - Permission and legal compliance
     - Potential conflict of interest identification

  4. **Claim Substantiation**:
     - Major claim evidence support
     - Cause-and-effect relationship validation
     - Correlation vs. causation clarification
     - Assumption identification and labeling
     - Counter-argument acknowledgment

  5. **Legal and Compliance Review**:
     - Copyright and fair use compliance
     - Privacy and confidentiality respect
     - Regulatory compliance (industry-specific)
     - Defamation and libel risk assessment
     - Disclosure requirement verification

  Flag any questionable content and provide alternative sources or corrections.`,
  
  expectedOutput: `A comprehensive fact-checking report containing:
  - Fact Verification Status Summary
  - Source Credibility Assessment
  - Data and Statistics Validation
  - Expert Quote Verification Results
  - Claim Substantiation Review
  - Legal and Compliance Check
  - Flagged Issues and Corrections
  - Alternative Source Recommendations
  - Confidence Level Assessment
  - Publication Clearance Status`,
  
  agent: qualityEditorAgent
});

export const finalQualityAssurance = new Task({
  description: `Conduct final quality assurance and publication readiness check for: {topic}

  Your final QA should ensure:

  1. **Content Completeness**:
     - All outlined sections included
     - Word count target achievement
     - Visual content integration confirmed
     - Call-to-action presence and effectiveness
     - Source citation completeness

  2. **Technical Readiness**:
     - Formatting consistency across platforms
     - Link functionality verification
     - Image optimization and alt text
     - Meta data completeness
     - Social sharing optimization

  3. **Quality Standards Compliance**:
     - Brand guidelines adherence
     - Style guide compliance
     - Editorial standards satisfaction
     - Legal requirement fulfillment
     - Accessibility standard compliance

  4. **Performance Optimization**:
     - SEO score achievement
     - Readability target fulfillment
     - Engagement element effectiveness
     - Conversion pathway optimization
     - Analytics tracking setup

  5. **Publication Checklist**:
     - Content management system formatting
     - Publication schedule readiness
     - Distribution channel preparation
     - Promotional content creation
     - Performance monitoring setup

  Provide final approval or specify required corrections before publication.`,
  
  expectedOutput: `A final quality assurance report with:
  - Publication Readiness Status
  - Content Completeness Verification
  - Technical Readiness Checklist
  - Quality Standards Compliance Review
  - Performance Optimization Confirmation
  - Publication Checklist Status
  - Final Score and Grade Assignment
  - Required Corrections List (if any)
  - Publication Approval/Rejection Decision
  - Post-Publication Monitoring Plan`,
  
  agent: qualityEditorAgent
});