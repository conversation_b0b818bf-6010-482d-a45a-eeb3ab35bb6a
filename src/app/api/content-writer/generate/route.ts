/**
 * Content Writer API Route - KaibanJS + OpenRouter <PERSON><PERSON>
 * Handles content generation requests with real-time streaming updates
 */
import { NextRequest, NextResponse } from 'next/server';
import { ContentTeamWorkflow } from '@/lib/agents/content-team/contentTeamWorkflow.js';

// Interface for content generation request
interface ContentGenerationRequest {
  topic: string;
  targetAudience?: string;
  contentType: string;
  wordCount: number;
  seoKeywords: string[];
  tone: string;
  additionalRequirements?: string;
}

// Interface for streaming update
interface StreamingUpdate {
  type: 'agent_update' | 'task_progress' | 'final_result' | 'error';
  agentId?: string;
  taskName?: string;
  update?: any;
  result?: any;
  error?: string;
  timestamp: string;
}

export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const body: ContentGenerationRequest = await request.json();
    
    // Validate required fields
    if (!body.topic || !body.contentType || !body.wordCount) {
      return NextResponse.json(
        { error: 'Missing required fields: topic, contentType, and wordCount' },
        { status: 400 }
      );
    }

    // Check if streaming is requested
    const isStreaming = request.headers.get('accept') === 'text/event-stream';
    
    if (isStreaming) {
      return handleStreamingRequest(body);
    } else {
      return handleRegularRequest(body);
    }
    
  } catch (error) {
    console.error('Content generation API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}

async function handleStreamingRequest(body: ContentGenerationRequest) {
  // Create a readable stream for Server-Sent Events
  const encoder = new TextEncoder();
  const readable = new ReadableStream({
    start(controller) {
      // Initialize the workflow
      const workflow = new ContentTeamWorkflow({
        enableRealTimeUpdates: true,
        enableCostTracking: true
      });

      // Set up real-time update callback
      workflow.setUpdateCallback((update: StreamingUpdate) => {
        const data = `data: ${JSON.stringify(update)}\n\n`;
        controller.enqueue(encoder.encode(data));
      });

      // Execute the workflow asynchronously
      executeWorkflowWithStreaming(workflow, body, controller, encoder)
        .catch((error) => {
          const errorUpdate: StreamingUpdate = {
            type: 'error',
            error: error.message,
            timestamp: new Date().toISOString()
          };
          const data = `data: ${JSON.stringify(errorUpdate)}\n\n`;
          controller.enqueue(encoder.encode(data));
          controller.close();
        });
    }
  });

  // Return streaming response
  return new Response(readable, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Accept'
    }
  });
}

async function executeWorkflowWithStreaming(
  workflow: ContentTeamWorkflow,
  body: ContentGenerationRequest,
  controller: ReadableStreamDefaultController,
  encoder: TextEncoder
) {
  try {
    // Start the workflow
    const result = await workflow.executeWorkflow(body);
    
    // Send final result
    const finalUpdate: StreamingUpdate = {
      type: 'final_result',
      result: result.data,
      timestamp: new Date().toISOString()
    };
    
    const data = `data: ${JSON.stringify(finalUpdate)}\n\n`;
    controller.enqueue(encoder.encode(data));
    
    // Close the stream
    controller.close();
    
  } catch (error) {
    console.error('Streaming workflow error:', error);
    const errorUpdate: StreamingUpdate = {
      type: 'error',
      error: error.message,
      timestamp: new Date().toISOString()
    };
    
    const data = `data: ${JSON.stringify(errorUpdate)}\n\n`;
    controller.enqueue(encoder.encode(data));
    controller.close();
  }
}

async function handleRegularRequest(body: ContentGenerationRequest) {
  try {
    // Initialize the workflow
    const workflow = new ContentTeamWorkflow({
      enableRealTimeUpdates: false,
      enableCostTracking: true
    });

    // Execute the workflow
    const result = await workflow.executeWorkflow(body);
    
    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data,
        executionTime: result.executionTime,
        timestamp: new Date().toISOString()
      });
    } else {
      return NextResponse.json(
        { 
          success: false, 
          error: result.error,
          timestamp: new Date().toISOString()
        },
        { status: 500 }
      );
    }
    
  } catch (error) {
    console.error('Regular workflow error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Workflow execution failed', 
        details: error.message,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

// Handle OPTIONS for CORS preflight
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Accept',
    },
  });
}

// Export configuration for the API route
export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

// Helper function to validate content request
function validateContentRequest(body: ContentGenerationRequest): string[] {
  const errors: string[] = [];
  
  if (!body.topic || body.topic.trim().length === 0) {
    errors.push('Topic is required');
  }
  
  if (!body.contentType) {
    errors.push('Content type is required');
  }
  
  if (!body.wordCount || body.wordCount < 100 || body.wordCount > 10000) {
    errors.push('Word count must be between 100 and 10,000');
  }
  
  if (body.seoKeywords && body.seoKeywords.length > 20) {
    errors.push('Maximum 20 SEO keywords allowed');
  }
  
  const validTones = ['professional', 'conversational', 'academic', 'casual', 'persuasive'];
  if (body.tone && !validTones.includes(body.tone)) {
    errors.push(`Tone must be one of: ${validTones.join(', ')}`);
  }
  
  const validContentTypes = ['blog-post', 'article', 'landing-page', 'social-media', 'email-newsletter'];
  if (body.contentType && !validContentTypes.includes(body.contentType)) {
    errors.push(`Content type must be one of: ${validContentTypes.join(', ')}`);
  }
  
  return errors;
}