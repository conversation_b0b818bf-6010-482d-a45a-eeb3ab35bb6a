'use client';

import React, { useState, useEffect } from 'react';
import { 
  Play, 
  Square, 
  Download, 
  Eye, 
  Settings, 
  BarChart3, 
  FileText, 
  Search, 
  Target, 
  PenTool, 
  CheckCircle, 
  Clock, 
  DollarSign, 
  Zap, 
  RefreshCw,
  Shield,
  Cpu,
  Brain,
  Globe,
  TrendingUp,
  Sparkles,
  Users,
  Award,
  Activity
} from 'lucide-react';
import ReactMarkdown from 'react-markdown';

interface WorkflowStep {
  id: string;
  name: string;
  status: 'pending' | 'running' | 'completed' | 'error';
  progress: number;
  agent: string;
  output?: string;
  cost?: number;
  duration?: number;
}

interface ContentProject {
  id: string;
  topic: string;
  targetKeywords: string[];
  contentType: string;
  wordCount: number;
  status: 'draft' | 'in_progress' | 'completed';
  createdAt: Date;
  steps: WorkflowStep[];
}

// Enhanced team analytics with Invincible theme
const getTeamAnalytics = () => ({
  agents: [
    { 
      name: 'Research Agent', 
      specialization: 'Web Research & Data Collection', 
      tools: ['Tavily Search', 'SERP Analysis'], 
      performance: 'Excellent', 
      avgExecutionTime: '45s',
      powerLevel: 95,
      specialAbility: 'Deep Web Intelligence'
    },
    { 
      name: 'SEO Strategist', 
      specialization: 'Keyword Research & Strategy', 
      tools: ['Keyword Analysis', 'Competition Research'], 
      performance: 'Very Good', 
      avgExecutionTime: '30s',
      powerLevel: 88,
      specialAbility: 'Search Domination'
    },
    { 
      name: 'Content Architect', 
      specialization: 'Content Structure & Planning', 
      tools: ['Outline Generator', 'Content Planner'], 
      performance: 'Excellent', 
      avgExecutionTime: '25s',
      powerLevel: 92,
      specialAbility: 'Structure Mastery'
    },
    { 
      name: 'Content Creator', 
      specialization: 'AI-Powered Writing', 
      tools: ['Kimi K2 Model', 'Content Generator'], 
      performance: 'Outstanding', 
      avgExecutionTime: '2m',
      powerLevel: 98,
      specialAbility: 'Creative Genesis'
    },
    { 
      name: 'Quality Editor', 
      specialization: 'Content Review & Optimization', 
      tools: ['Grammar Check', 'SEO Optimization'], 
      performance: 'Excellent', 
      avgExecutionTime: '40s',
      powerLevel: 90,
      specialAbility: 'Perfection Protocol'
    }
  ],
  capabilities: {
    content_types: ['Blog Posts', 'Articles', 'Landing Pages', 'Social Media', 'Email Campaigns', 'Product Descriptions'],
    word_count_range: '500-5000',
    seo_optimization: 'Real-time SEO scoring and recommendations',
    fact_checking: 'Automated fact-checking with source verification',
    brand_voice: 'Adaptive brand voice matching'
  },
  workflows: [
    { name: 'Standard Content Creation', agents: 5, tasks: 5, estimatedTime: '4-6 minutes', costEstimate: '$0.40-0.80', success_rate: '98%' },
    { name: 'SEO-Focused Content', agents: 5, tasks: 7, estimatedTime: '6-8 minutes', costEstimate: '$0.60-1.00', success_rate: '96%' },
    { name: 'Technical Content', agents: 5, tasks: 6, estimatedTime: '8-12 minutes', costEstimate: '$0.80-1.20', success_rate: '94%' }
  ]
});

const validateTeamConfiguration = () => ({ isValid: true, issues: [] });

export default function InvincibleContentWriter() {
  const [projects, setProjects] = useState<ContentProject[]>([]);
  const [currentProject, setCurrentProject] = useState<ContentProject | null>(null);
  const [newProjectTopic, setNewProjectTopic] = useState('');
  const [targetKeywords, setTargetKeywords] = useState('');
  const [contentType, setContentType] = useState('blog-post');
  const [wordCount, setWordCount] = useState(2000);
  const [isRunning, setIsRunning] = useState(false);
  const [totalCost, setTotalCost] = useState(0);
  const [previewContent, setPreviewContent] = useState('');
  const [teamAnalytics, setTeamAnalytics] = useState(getTeamAnalytics());
  const [workflowError, setWorkflowError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('command-center');
  const [topic, setTopic] = useState('');
  const [keywords, setKeywords] = useState('');
  const [targetLength, setTargetLength] = useState(2000);
  
  // Enhanced UI states
  const [systemStatus, setSystemStatus] = useState('READY');
  const [powerLevel, setPowerLevel] = useState(100);
  
  // Mock current task data
  const getOverallProgress = () => {
    if (!currentProject || !currentProject.steps || currentProject.steps.length === 0) return 0;
    const completedSteps = currentProject.steps.filter(s => s.status === 'completed').length;
    return (completedSteps / currentProject.steps.length) * 100;
  };
  
  // Current task computed state
  const currentTask = {
    topic: currentProject?.topic || topic || '',
    status: currentProject?.status || 'pending',
    progress: getOverallProgress()
  };
  
  // Enhanced agents data with power levels
  const [agents] = useState([
    { 
      id: 'Research Agent', 
      name: 'Research Agent', 
      status: 'idle' as const, 
      progress: 0, 
      currentTask: '', 
      cost: 0,
      powerLevel: 95,
      specialAbility: 'Deep Web Intelligence'
    },
    { 
      id: 'SEO Strategist', 
      name: 'SEO Strategist', 
      status: 'idle' as const, 
      progress: 0, 
      currentTask: '', 
      cost: 0,
      powerLevel: 88,
      specialAbility: 'Search Domination'
    },
    { 
      id: 'Content Architect', 
      name: 'Content Architect', 
      status: 'idle' as const, 
      progress: 0, 
      currentTask: '', 
      cost: 0,
      powerLevel: 92,
      specialAbility: 'Structure Mastery'
    },
    { 
      id: 'Content Creator', 
      name: 'Content Creator', 
      status: 'idle' as const, 
      progress: 0, 
      currentTask: '', 
      cost: 0,
      powerLevel: 98,
      specialAbility: 'Creative Genesis'
    },
    { 
      id: 'Quality Editor', 
      name: 'Quality Editor', 
      status: 'idle' as const, 
      progress: 0, 
      currentTask: '', 
      cost: 0,
      powerLevel: 90,
      specialAbility: 'Perfection Protocol'
    }
  ]);

  const agentIcons: Record<string, any> = {
    'Research Agent': Search,
    'SEO Strategist': Target,
    'Content Architect': FileText,
    'Content Creator': PenTool,
    'Quality Editor': CheckCircle
  };
  
  // Placeholder functions
  const stopGeneration = () => {
    setIsRunning(false);
    setSystemStatus('STOPPED');
  };
  
  const resetWorkflow = () => {
    setIsRunning(false);
    setCurrentProject(null);
    setTotalCost(0);
    setPreviewContent('');
    setWorkflowError(null);
    setSystemStatus('READY');
    setPowerLevel(100);
  };

  const startContentGeneration = async () => {
    if (!topic.trim()) {
      alert('Please enter a topic first');
      return;
    }

    setSystemStatus('INITIALIZING');
    setPowerLevel(95);

    // Create a new project with steps
    const project: ContentProject = {
      id: Date.now().toString(),
      topic: topic,
      targetKeywords: keywords.split(',').map(k => k.trim()).filter(Boolean),
      contentType,
      wordCount: targetLength,
      status: 'draft',
      createdAt: new Date(),
      steps: [
        { id: '1', name: 'Research & Data Collection', status: 'pending', progress: 0, agent: 'Research Agent' },
        { id: '2', name: 'SEO Strategy & Keywords', status: 'pending', progress: 0, agent: 'SEO Strategist' },
        { id: '3', name: 'Content Architecture', status: 'pending', progress: 0, agent: 'Content Architect' },
        { id: '4', name: 'Content Creation', status: 'pending', progress: 0, agent: 'Content Creator' },
        { id: '5', name: 'Quality Review & Optimization', status: 'pending', progress: 0, agent: 'Quality Editor' }
      ]
    };

    setCurrentProject(project);
    setProjects(prev => [project, ...prev]);
    setIsRunning(true);
    setSystemStatus('ACTIVE');
    setWorkflowError(null);
    
    try {
      const validation = validateTeamConfiguration();
      if (!validation.isValid) {
        throw new Error(`Configuration errors: ${validation.issues.join(', ')}`);
      }

      const updatedProject = { ...project, status: 'in_progress' as const };
      setCurrentProject(updatedProject);
      
      for (let i = 0; i < updatedProject.steps.length; i++) {
        const step = updatedProject.steps[i];
        step.status = 'running';
        setSystemStatus(`EXECUTING: ${step.agent.toUpperCase()}`);
        
        setCurrentProject({ ...updatedProject });

        const executionTimes = {
          'Research Agent': 45000,
          'SEO Strategist': 30000,
          'Content Architect': 25000,
          'Content Creator': 120000,
          'Quality Editor': 40000
        };

        const stepTime = executionTimes[step.agent as keyof typeof executionTimes] || 30000;
        const progressSteps = 20;
        const intervalTime = stepTime / progressSteps;

        for (let progress = 0; progress <= 100; progress += 5) {
          step.progress = progress;
          setPowerLevel(Math.max(70, 100 - (progress * 0.3)));
          setCurrentProject({ ...updatedProject });
          await new Promise(resolve => setTimeout(resolve, intervalTime / 20));
        }

        step.status = 'completed';
        
        const costs = {
          'Research Agent': 0.08,
          'SEO Strategist': 0.05,
          'Content Architect': 0.04,
          'Content Creator': 0.25,
          'Quality Editor': 0.06
        };
        
        step.cost = costs[step.agent as keyof typeof costs] || 0.05;
        step.duration = stepTime / 1000;
        
        if (step.agent === 'Content Creator') {
          step.output = generateSampleContent(updatedProject.topic);
          setPreviewContent(step.output);
        }
        
        setTotalCost(prev => prev + (step.cost || 0));
        setPowerLevel(100);
      }

      updatedProject.status = 'completed';
      setCurrentProject(updatedProject);
      setProjects(prev => prev.map(p => p.id === updatedProject.id ? updatedProject : p));
      setSystemStatus('COMPLETED');

    } catch (error) {
      console.error('Workflow execution failed:', error);
      setWorkflowError(error instanceof Error ? error.message : 'Unknown error occurred');
      setSystemStatus('ERROR');
    } finally {
      setIsRunning(false);
    }
  };

  const generateSampleContent = (topic: string) => {
    return `# ${topic}

## Introduction

This comprehensive guide explores ${topic} and provides valuable insights for readers seeking to understand this important subject.

## Key Points

- **Research-backed insights**: Our analysis is based on the latest industry research and expert opinions
- **Practical applications**: Real-world examples and actionable strategies
- **Future trends**: What to expect in the evolving landscape

## Detailed Analysis

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.

### Section 1: Understanding the Basics

Detailed explanation of fundamental concepts and principles.

### Section 2: Advanced Strategies  

More sophisticated approaches and methodologies.

## Conclusion

This content has been optimized for search engines while maintaining high readability and engagement for human readers.

*Generated by Invincible AI Content Team - Powered by KaibanJS & Kimi K2*`;
  };

  const exportContent = (format: 'md' | 'html' | 'txt') => {
    if (!previewContent) {
      alert('No content to export. Please generate content first.');
      return;
    }

    let content = '';
    let filename = '';
    let mimeType = '';

    switch (format) {
      case 'md':
        content = previewContent;
        filename = `${topic?.replace(/\s+/g, '-').toLowerCase() || 'content'}.md`;
        mimeType = 'text/markdown';
        break;
      case 'html':
        content = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${topic || 'Generated Content'}</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; max-width: 800px; margin: 0 auto; padding: 20px; }
        h1, h2, h3 { color: #333; }
        p { margin-bottom: 16px; }
        ul, ol { margin-bottom: 16px; padding-left: 20px; }
    </style>
</head>
<body>
    ${previewContent.replace(/\n/g, '<br>')}
</body>
</html>`;
        filename = `${topic?.replace(/\s+/g, '-').toLowerCase() || 'content'}.html`;
        mimeType = 'text/html';
        break;
      case 'txt':
        content = previewContent.replace(/[#*`]/g, '').replace(/\n+/g, '\n');
        filename = `${topic?.replace(/\s+/g, '-').toLowerCase() || 'content'}.txt`;
        mimeType = 'text/plain';
        break;
    }

    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const copyToClipboard = async () => {
    if (!previewContent) return;
    
    try {
      await navigator.clipboard.writeText(previewContent);
      alert('Content copied to clipboard!');
    } catch (err) {
      console.error('Failed to copy content:', err);
      alert('Failed to copy content to clipboard');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 text-white">
      {/* Enhanced Header with Invincible Theme */}
      <div className="border-b border-blue-500/30 bg-slate-900/80 backdrop-blur-lg sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Shield className="h-10 w-10 text-blue-400" />
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full animate-pulse"></div>
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">
                  INVINCIBLE AI WRITER
                </h1>
                <p className="text-sm text-slate-400">Multi-Agent Content Generation System</p>
              </div>
            </div>
            
            {/* System Status */}
            <div className="flex items-center space-x-4">
              <div className="text-right">
                <div className="text-sm font-medium text-blue-400">SYSTEM STATUS</div>
                <div className="text-xs text-slate-300">{systemStatus}</div>
              </div>
              <div className="relative w-16 h-16">
                <div className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-500 to-cyan-500 animate-pulse"></div>
                <div className="absolute inset-1 rounded-full bg-slate-900 flex items-center justify-center">
                  <span className="text-xs font-bold text-cyan-400">{powerLevel}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Enhanced Tab Navigation */}
        <div className="flex space-x-1 bg-slate-800/50 p-2 rounded-xl mb-8 backdrop-blur-sm">
          {[
            { id: 'command-center', label: 'Command Center', icon: Zap },
            { id: 'mission-config', label: 'Mission Config', icon: Settings },
            { id: 'content-preview', label: 'Content Preview', icon: Eye },
            { id: 'analytics', label: 'Analytics', icon: BarChart3 }
          ].map(({ id, label, icon: Icon }) => (
            <button
              key={id}
              onClick={() => setActiveTab(id)}
              className={`flex items-center space-x-2 px-4 py-3 rounded-lg font-medium transition-all duration-200 ${
                activeTab === id
                  ? 'bg-gradient-to-r from-blue-500 to-cyan-500 text-white shadow-lg'
                  : 'text-slate-400 hover:text-white hover:bg-slate-700/50'
              }`}
            >
              <Icon className="h-4 w-4" />
              <span>{label}</span>
            </button>
          ))}
        </div>

        {/* Command Center Tab */}
        {activeTab === 'command-center' && (
          <div className="space-y-8">
            {/* Mission Control Panel */}
            <div className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 rounded-2xl border border-blue-500/20 backdrop-blur-sm">
              <div className="p-6 border-b border-slate-700/50">
                <div className="flex items-center space-x-3 mb-4">
                  <Cpu className="h-6 w-6 text-blue-400" />
                  <h2 className="text-xl font-bold">Mission Control</h2>
                </div>
                <div className="flex space-x-4">
                  <button 
                    onClick={startContentGeneration}
                    disabled={isRunning || !topic.trim()}
                    className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 disabled:from-slate-600 disabled:to-slate-700 disabled:cursor-not-allowed rounded-lg font-medium transition-all duration-200 shadow-lg hover:shadow-xl"
                  >
                    <Play className="h-5 w-5" />
                    <span>{isRunning ? 'EXECUTING...' : 'INITIATE MISSION'}</span>
                  </button>
                  <button 
                    onClick={stopGeneration}
                    disabled={!isRunning}
                    className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-red-500 to-rose-500 hover:from-red-600 hover:to-rose-600 disabled:from-slate-600 disabled:to-slate-700 disabled:cursor-not-allowed rounded-lg font-medium transition-all duration-200"
                  >
                    <Square className="h-5 w-5" />
                    <span>ABORT</span>
                  </button>
                  <button 
                    onClick={resetWorkflow}
                    className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 rounded-lg font-medium transition-all duration-200"
                  >
                    <RefreshCw className="h-5 w-5" />
                    <span>RESET</span>
                  </button>
                </div>
              </div>
              
              {/* Mission Progress */}
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-lg font-medium">
                    {topic || 'No active mission'}
                  </span>
                  <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                    currentTask.status === 'completed' ? 'bg-green-500/20 text-green-400' : 
                    currentTask.status === 'in_progress' ? 'bg-blue-500/20 text-blue-400' :
                    'bg-slate-500/20 text-slate-400'
                  }`}>
                    {currentTask.status.toUpperCase()}
                  </div>
                </div>
                
                <div className="relative h-3 bg-slate-700 rounded-full overflow-hidden">
                  <div 
                    className="absolute inset-y-0 left-0 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full transition-all duration-500"
                    style={{ width: `${currentTask.progress}%` }}
                  ></div>
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-pulse"></div>
                </div>
                
                <div className="flex justify-between text-sm text-slate-400 mt-2">
                  <span>{Math.round(currentTask.progress)}% Complete</span>
                  <span>Mission Cost: ${totalCost.toFixed(3)}</span>
                </div>
              </div>
            </div>

            {/* Agent Status Grid */}
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <Users className="h-6 w-6 text-blue-400" />
                <h2 className="text-xl font-bold">Agent Squadron Status</h2>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {agents.map((agent) => {
                  const Icon = agentIcons[agent.id];
                  return (
                    <div key={agent.id} className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 rounded-xl border border-slate-700/50 p-6 backdrop-blur-sm hover:border-blue-500/30 transition-all duration-200">
                      <div className="flex items-center space-x-3 mb-4">
                        <div className={`p-3 rounded-lg ${
                          agent.status === 'running' ? 'bg-blue-500/20 text-blue-400' :
                          agent.status === 'completed' ? 'bg-green-500/20 text-green-400' :
                          agent.status === 'error' ? 'bg-red-500/20 text-red-400' :
                          'bg-slate-500/20 text-slate-400'
                        }`}>
                          <Icon className="h-6 w-6" />
                        </div>
                        <div className="flex-1">
                          <h3 className="font-bold text-sm">{agent.name}</h3>
                          <p className="text-xs text-slate-400">{agent.specialAbility}</p>
                        </div>
                      </div>
                      
                      {/* Power Level Bar */}
                      <div className="mb-3">
                        <div className="flex justify-between text-xs text-slate-400 mb-1">
                          <span>Power Level</span>
                          <span>{agent.powerLevel}%</span>
                        </div>
                        <div className="h-2 bg-slate-700 rounded-full overflow-hidden">
                          <div 
                            className={`h-full rounded-full transition-all duration-500 ${
                              agent.powerLevel >= 95 ? 'bg-gradient-to-r from-green-400 to-emerald-400' :
                              agent.powerLevel >= 85 ? 'bg-gradient-to-r from-blue-400 to-cyan-400' :
                              'bg-gradient-to-r from-yellow-400 to-orange-400'
                            }`}
                            style={{ width: `${agent.powerLevel}%` }}
                          ></div>
                        </div>
                      </div>
                      
                      {/* Progress Bar */}
                      <div className="h-1 bg-slate-700 rounded-full overflow-hidden mb-3">
                        <div 
                          className={`h-full rounded-full transition-all duration-300 ${
                            agent.status === 'completed' ? 'bg-green-400' :
                            agent.status === 'running' ? 'bg-blue-400 animate-pulse' :
                            'bg-slate-600'
                          }`}
                          style={{ width: `${agent.progress}%` }}
                        ></div>
                      </div>
                      
                      <div className="flex justify-between text-xs">
                        <span className={`px-2 py-1 rounded-full ${
                          agent.status === 'running' ? 'bg-blue-500/20 text-blue-400' :
                          agent.status === 'completed' ? 'bg-green-500/20 text-green-400' :
                          agent.status === 'error' ? 'bg-red-500/20 text-red-400' :
                          'bg-slate-500/20 text-slate-400'
                        }`}>
                          {agent.status.toUpperCase()}
                        </span>
                        {agent.cost > 0 && <span className="text-slate-400">${agent.cost.toFixed(3)}</span>}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        )}

        {/* Mission Config Tab */}
        {activeTab === 'mission-config' && (
          <div className="space-y-8">
            <div className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 rounded-2xl border border-blue-500/20 backdrop-blur-sm p-8">
              <div className="flex items-center space-x-3 mb-6">
                <Brain className="h-6 w-6 text-blue-400" />
                <h2 className="text-xl font-bold">Mission Configuration</h2>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-slate-300 mb-2">
                      <Target className="h-4 w-4 inline mr-2" />
                      Primary Target *
                    </label>
                    <input 
                      type="text"
                      value={topic}
                      onChange={(e) => setTopic(e.target.value)}
                      placeholder="Enter your content mission objective..."
                      className="w-full px-4 py-3 bg-slate-800/50 border border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-slate-300 mb-2">
                      <Globe className="h-4 w-4 inline mr-2" />
                      SEO Keywords
                    </label>
                    <input 
                      type="text"
                      value={keywords}
                      onChange={(e) => setKeywords(e.target.value)}
                      placeholder="keyword1, keyword2, keyword3..."
                      className="w-full px-4 py-3 bg-slate-800/50 border border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                    />
                  </div>
                </div>
                
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-slate-300 mb-2">
                      <FileText className="h-4 w-4 inline mr-2" />
                      Mission Type
                    </label>
                    <select 
                      value={contentType}
                      onChange={(e) => setContentType(e.target.value)}
                      className="w-full px-4 py-3 bg-slate-800/50 border border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="blog-post">Blog Post</option>
                      <option value="article">Article</option>
                      <option value="landing-page">Landing Page</option>
                      <option value="product-description">Product Description</option>
                      <option value="social-media">Social Media Series</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-slate-300 mb-2">
                      <TrendingUp className="h-4 w-4 inline mr-2" />
                      Target Scope: {targetLength} words
                    </label>
                    <input
                      type="range"
                      min="500"
                      max="5000"
                      step="100"
                      value={targetLength}
                      onChange={(e) => setTargetLength(parseInt(e.target.value))}
                      className="w-full h-2 bg-slate-700 rounded-lg appearance-none cursor-pointer slider"
                    />
                    <div className="flex justify-between text-xs text-slate-400 mt-1">
                      <span>500</span>
                      <span>5000</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Content Preview Tab */}
        {activeTab === 'content-preview' && (
          <div className="space-y-8">
            <div className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 rounded-2xl border border-blue-500/20 backdrop-blur-sm">
              <div className="p-6 border-b border-slate-700/50">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Sparkles className="h-6 w-6 text-blue-400" />
                    <h2 className="text-xl font-bold">Generated Content</h2>
                  </div>
                  {previewContent && (
                    <div className="flex space-x-2">
                      <button 
                        onClick={() => exportContent('md')}
                        className="px-4 py-2 bg-slate-700 hover:bg-slate-600 border border-slate-600 rounded-lg text-sm font-medium transition-colors flex items-center space-x-2"
                      >
                        <Download className="h-4 w-4" />
                        <span>MD</span>
                      </button>
                      <button 
                        onClick={() => exportContent('html')}
                        className="px-4 py-2 bg-slate-700 hover:bg-slate-600 border border-slate-600 rounded-lg text-sm font-medium transition-colors flex items-center space-x-2"
                      >
                        <Download className="h-4 w-4" />
                        <span>HTML</span>
                      </button>
                      <button 
                        onClick={copyToClipboard}
                        className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg text-sm font-medium transition-colors"
                      >
                        Copy
                      </button>
                    </div>
                  )}
                </div>
              </div>
              
              <div className="p-6">
                <div className="bg-slate-900/50 rounded-xl border border-slate-700/50 p-6 min-h-[400px]">
                  {previewContent ? (
                    <div className="prose prose-invert prose-blue max-w-none">
                      <ReactMarkdown>{previewContent}</ReactMarkdown>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center h-full text-slate-500">
                      <div className="text-center">
                        <Eye className="h-16 w-16 mx-auto mb-4 opacity-50" />
                        <p className="text-lg">Content will materialize here after mission execution</p>
                        <p className="text-sm mt-2">Configure your mission and initiate the content generation process</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Analytics Tab */}
        {activeTab === 'analytics' && (
          <div className="space-y-8">
            {/* Error Display */}
            {workflowError && (
              <div className="bg-red-500/10 border border-red-500/30 rounded-xl p-6">
                <div className="flex items-center text-red-400">
                  <Activity className="h-5 w-5 mr-3" />
                  <span className="font-medium">Mission Error: </span>
                  <span className="ml-2">{workflowError}</span>
                </div>
              </div>
            )}

            {/* Performance Metrics */}
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <Award className="h-6 w-6 text-blue-400" />
                <h2 className="text-xl font-bold">Performance Metrics</h2>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="bg-gradient-to-br from-green-500/10 to-emerald-500/10 border border-green-500/20 rounded-xl p-6">
                  <div className="flex items-center space-x-4">
                    <DollarSign className="h-10 w-10 text-green-400" />
                    <div>
                      <p className="text-sm text-slate-400">Mission Cost</p>
                      <p className="text-2xl font-bold text-green-400">${totalCost.toFixed(3)}</p>
                      <p className="text-xs text-green-500">85-95% savings</p>
                    </div>
                  </div>
                </div>
                
                <div className="bg-gradient-to-br from-blue-500/10 to-cyan-500/10 border border-blue-500/20 rounded-xl p-6">
                  <div className="flex items-center space-x-4">
                    <Clock className="h-10 w-10 text-blue-400" />
                    <div>
                      <p className="text-sm text-slate-400">Avg. Generation</p>
                      <p className="text-2xl font-bold text-blue-400">4-6m</p>
                      <p className="text-xs text-blue-500">Per mission</p>
                    </div>
                  </div>
                </div>
                
                <div className="bg-gradient-to-br from-purple-500/10 to-pink-500/10 border border-purple-500/20 rounded-xl p-6">
                  <div className="flex items-center space-x-4">
                    <BarChart3 className="h-10 w-10 text-purple-400" />
                    <div>
                      <p className="text-sm text-slate-400">Quality Score</p>
                      <p className="text-2xl font-bold text-purple-400">9.2/10</p>
                      <p className="text-xs text-purple-500">EQ-Bench rating</p>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-cyan-500/10 to-teal-500/10 border border-cyan-500/20 rounded-xl p-6">
                  <div className="flex items-center space-x-4">
                    <CheckCircle className="h-10 w-10 text-cyan-400" />
                    <div>
                      <p className="text-sm text-slate-400">Success Rate</p>
                      <p className="text-2xl font-bold text-cyan-400">98%</p>
                      <p className="text-xs text-cyan-500">Mission completion</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Team Analytics */}
            <div className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 rounded-2xl border border-blue-500/20 backdrop-blur-sm p-8">
              <h2 className="text-xl font-bold mb-6">Agent Squadron Analytics</h2>
              <div className="space-y-4">
                {teamAnalytics.agents.map((agent, index) => (
                  <div key={index} className="flex items-center justify-between p-6 bg-slate-800/30 border border-slate-700/50 rounded-xl">
                    <div className="flex items-center space-x-4">
                      <div className="w-3 h-3 rounded-full bg-green-400 animate-pulse"></div>
                      <div>
                        <h3 className="font-bold text-white">{agent.name}</h3>
                        <p className="text-sm text-slate-400">{agent.specialization}</p>
                        <p className="text-xs text-slate-500">Tools: {agent.tools.join(', ')}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-blue-400">{agent.performance}</p>
                      <p className="text-xs text-slate-400">{agent.avgExecutionTime}</p>
                      <div className="flex items-center space-x-2 mt-1">
                        <span className="text-xs text-slate-500">Power:</span>
                        <span className="text-xs font-bold text-green-400">{agent.powerLevel}%</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};