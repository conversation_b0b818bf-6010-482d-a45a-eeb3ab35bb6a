'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Shield,
  Zap,
  Users,
  Target,
  Search,
  FileText,
  PenTool,
  CheckCircle,
  ArrowRight,
  Clock,
  DollarSign,
  TrendingUp,
  Brain,
  Cpu
} from 'lucide-react'

interface InvincibleWriterPreviewProps {
  className?: string
}

export default function InvincibleWriterPreview({ className = "" }: InvincibleWriterPreviewProps) {
  const [currentStep, setCurrentStep] = useState(0)
  const [isGenerating, setIsGenerating] = useState(false)

  const agents = [
    { 
      name: 'Research Agent', 
      icon: Search, 
      status: 'ready',
      powerLevel: 95,
      specialAbility: 'Deep Web Intelligence'
    },
    { 
      name: 'SEO Strategist', 
      icon: Target, 
      status: 'ready',
      powerLevel: 88,
      specialAbility: 'Search Domination'
    },
    { 
      name: 'Content Architect', 
      icon: FileText, 
      status: 'ready',
      powerLevel: 92,
      specialAbility: 'Structure Mastery'
    },
    { 
      name: 'Content Creator', 
      icon: PenTool, 
      status: 'ready',
      powerLevel: 98,
      specialAbility: 'Creative Genesis'
    },
    { 
      name: 'Quality Editor', 
      icon: CheckCircle, 
      status: 'ready',
      powerLevel: 90,
      specialAbility: 'Perfection Protocol'
    }
  ]

  const startDemo = () => {
    setIsGenerating(true)
    setCurrentStep(0)
    
    const interval = setInterval(() => {
      setCurrentStep(prev => {
        if (prev >= agents.length - 1) {
          clearInterval(interval)
          setTimeout(() => {
            setIsGenerating(false)
            setCurrentStep(0)
          }, 1000)
          return prev
        }
        return prev + 1
      })
    }, 800)
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="relative">
            <Shield className="h-8 w-8 text-blue-400" />
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
          </div>
          <div>
            <h3 className="text-xl font-bold bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">
              Invincible AI Writer
            </h3>
            <p className="text-sm text-gray-400">5-Agent Content System</p>
          </div>
        </div>
        
        <motion.button
          onClick={startDemo}
          disabled={isGenerating}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          className="px-4 py-2 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 disabled:from-slate-600 disabled:to-slate-700 disabled:cursor-not-allowed rounded-lg font-medium transition-all duration-200 flex items-center space-x-2 text-sm"
        >
          <Zap className="h-4 w-4" />
          <span>{isGenerating ? 'EXECUTING...' : 'DEMO'}</span>
        </motion.button>
      </div>

      {/* System Stats */}
      <div className="grid grid-cols-3 gap-4">
        <div className="bg-gradient-to-br from-green-500/10 to-emerald-500/10 border border-green-500/20 rounded-xl p-4">
          <div className="flex items-center space-x-3">
            <DollarSign className="h-6 w-6 text-green-400" />
            <div>
              <p className="text-xs text-gray-400">Cost Savings</p>
              <p className="text-lg font-bold text-green-400">85-95%</p>
            </div>
          </div>
        </div>
        
        <div className="bg-gradient-to-br from-blue-500/10 to-cyan-500/10 border border-blue-500/20 rounded-xl p-4">
          <div className="flex items-center space-x-3">
            <Clock className="h-6 w-6 text-blue-400" />
            <div>
              <p className="text-xs text-gray-400">Generation</p>
              <p className="text-lg font-bold text-blue-400">4-6min</p>
            </div>
          </div>
        </div>
        
        <div className="bg-gradient-to-br from-purple-500/10 to-pink-500/10 border border-purple-500/20 rounded-xl p-4">
          <div className="flex items-center space-x-3">
            <TrendingUp className="h-6 w-6 text-purple-400" />
            <div>
              <p className="text-xs text-gray-400">Quality</p>
              <p className="text-lg font-bold text-purple-400">9.2/10</p>
            </div>
          </div>
        </div>
      </div>

      {/* Agent Squadron */}
      <div>
        <div className="flex items-center space-x-2 mb-4">
          <Users className="h-5 w-5 text-blue-400" />
          <h4 className="font-semibold text-white">Agent Squadron</h4>
          {isGenerating && (
            <div className="flex items-center space-x-1 text-xs text-blue-400">
              <Cpu className="h-3 w-3 animate-pulse" />
              <span>ACTIVE</span>
            </div>
          )}
        </div>
        
        <div className="grid grid-cols-1 gap-3">
          {agents.map((agent, index) => {
            const Icon = agent.icon
            const isActive = isGenerating && currentStep === index
            const isCompleted = isGenerating && currentStep > index
            
            return (
              <motion.div
                key={agent.name}
                initial={{ opacity: 0.7, scale: 1 }}
                animate={{ 
                  opacity: isActive ? 1 : isCompleted ? 1 : 0.7,
                  scale: isActive ? 1.02 : 1
                }}
                className={`flex items-center space-x-3 p-3 rounded-lg border transition-all duration-300 ${
                  isActive 
                    ? 'bg-blue-500/20 border-blue-500/40' 
                    : isCompleted 
                      ? 'bg-green-500/20 border-green-500/40' 
                      : 'bg-slate-800/30 border-slate-700/50'
                }`}
              >
                <div className={`p-2 rounded-lg ${
                  isActive 
                    ? 'bg-blue-500/30 text-blue-400' 
                    : isCompleted 
                      ? 'bg-green-500/30 text-green-400' 
                      : 'bg-slate-600/30 text-slate-400'
                }`}>
                  <Icon className="h-4 w-4" />
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <p className="font-medium text-sm truncate">{agent.name}</p>
                    <div className="flex items-center space-x-2">
                      <span className="text-xs text-gray-400">{agent.powerLevel}%</span>
                      {isActive && (
                        <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                      )}
                      {isCompleted && (
                        <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                      )}
                    </div>
                  </div>
                  <p className="text-xs text-gray-500 truncate">{agent.specialAbility}</p>
                </div>
              </motion.div>
            )
          })}
        </div>
      </div>

      {/* Features */}
      <div className="bg-slate-800/30 border border-slate-700/50 rounded-xl p-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Brain className="h-4 w-4 text-blue-400" />
              <span className="text-sm font-medium">Multi-Agent System</span>
            </div>
            <div className="flex items-center space-x-2">
              <Target className="h-4 w-4 text-green-400" />
              <span className="text-sm font-medium">SEO Optimized</span>
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Zap className="h-4 w-4 text-purple-400" />
              <span className="text-sm font-medium">Real-time Progress</span>
            </div>
            <div className="flex items-center space-x-2">
              <DollarSign className="h-4 w-4 text-emerald-400" />
              <span className="text-sm font-medium">Cost Effective</span>
            </div>
          </div>
        </div>
      </div>

      {/* CTA */}
      <motion.div
        whileHover={{ y: -2 }}
        className="bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl p-4 cursor-pointer"
      >
        <div className="flex items-center justify-between">
          <div>
            <p className="font-semibold text-white">Ready to create?</p>
            <p className="text-sm text-blue-100">Launch your content mission</p>
          </div>
          <ArrowRight className="h-5 w-5 text-white" />
        </div>
      </motion.div>
    </div>
  )
}