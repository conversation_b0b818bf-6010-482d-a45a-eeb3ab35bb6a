"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layers.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-help.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/video.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ProfileButton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ProfileButton */ \"(app-pages-browser)/./src/components/ProfileButton.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Lazy load RecentContent to reduce initial bundle size\nconst OptimizedRecentContent = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_dashboard_RecentContent_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/dashboard/RecentContent */ \"(app-pages-browser)/./src/components/dashboard/RecentContent.tsx\")).then((mod)=>({\n            default: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(mod.default)\n        })), {\n    loadableGenerated: {\n        modules: [\n            \"app/dashboard/page.tsx -> \" + \"@/components/dashboard/RecentContent\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: Array.from({\n                length: 3\n            }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white/10 rounded-lg p-4 animate-pulse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 bg-white/10 rounded mb-2 w-3/4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-3 bg-white/10 rounded w-1/2\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, i, true, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n            lineNumber: 63,\n            columnNumber: 5\n        }, undefined)\n});\n_c = OptimizedRecentContent;\nconst OptimizedBlogPreview = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_c1 = ()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_BlogPreview_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/BlogPreview */ \"(app-pages-browser)/./src/components/BlogPreview.tsx\")).then((mod)=>({\n            default: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(mod.default)\n        })), {\n    loadableGenerated: {\n        modules: [\n            \"app/dashboard/page.tsx -> \" + \"@/components/BlogPreview\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-16 h-16 border-2 border-pink-500/30 border-t-pink-500 rounded-full animate-spin mx-auto\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n            lineNumber: 77,\n            columnNumber: 18\n        }, undefined)\n});\n_c2 = OptimizedBlogPreview;\nconst OptimizedEmailPreview = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_c3 = ()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_EmailPreview_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/EmailPreview */ \"(app-pages-browser)/./src/components/EmailPreview.tsx\")).then((mod)=>({\n            default: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(mod.default)\n        })), {\n    loadableGenerated: {\n        modules: [\n            \"app/dashboard/page.tsx -> \" + \"@/components/EmailPreview\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-16 h-16 border-2 border-emerald-500/30 border-t-emerald-500 rounded-full animate-spin mx-auto\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n            lineNumber: 82,\n            columnNumber: 18\n        }, undefined)\n});\n_c4 = OptimizedEmailPreview;\nconst OptimizedSocialMediaPreview = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_c5 = ()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_SocialMediaPreview_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/SocialMediaPreview */ \"(app-pages-browser)/./src/components/SocialMediaPreview.tsx\")).then((mod)=>({\n            default: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(mod.default)\n        })), {\n    loadableGenerated: {\n        modules: [\n            \"app/dashboard/page.tsx -> \" + \"@/components/SocialMediaPreview\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-16 h-16 border-2 border-blue-500/30 border-t-blue-500 rounded-full animate-spin mx-auto\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n            lineNumber: 87,\n            columnNumber: 18\n        }, undefined)\n});\n_c6 = OptimizedSocialMediaPreview;\nconst OptimizedVideoScriptPreview = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_c7 = ()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_VideoScriptPreview_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/VideoScriptPreview */ \"(app-pages-browser)/./src/components/VideoScriptPreview.tsx\")).then((mod)=>({\n            default: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(mod.default)\n        })), {\n    loadableGenerated: {\n        modules: [\n            \"app/dashboard/page.tsx -> \" + \"@/components/VideoScriptPreview\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-16 h-16 border-2 border-red-500/30 border-t-red-500 rounded-full animate-spin mx-auto\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n            lineNumber: 92,\n            columnNumber: 18\n        }, undefined)\n});\n_c8 = OptimizedVideoScriptPreview;\nconst OptimizedVideoAlchemyPreview = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_c9 = ()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_VideoAlchemyPreview_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/VideoAlchemyPreview */ \"(app-pages-browser)/./src/components/VideoAlchemyPreview.tsx\")).then((mod)=>({\n            default: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(mod.default)\n        })), {\n    loadableGenerated: {\n        modules: [\n            \"app/dashboard/page.tsx -> \" + \"@/components/VideoAlchemyPreview\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-16 h-16 border-2 border-purple-500/30 border-t-purple-500 rounded-full animate-spin mx-auto\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n            lineNumber: 97,\n            columnNumber: 18\n        }, undefined)\n});\n_c10 = OptimizedVideoAlchemyPreview;\nconst OptimizedMegatronPreview = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_c11 = ()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_MegatronPreview_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/MegatronPreview */ \"(app-pages-browser)/./src/components/MegatronPreview.tsx\")).then((mod)=>({\n            default: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(mod.default)\n        })), {\n    loadableGenerated: {\n        modules: [\n            \"app/dashboard/page.tsx -> \" + \"@/components/MegatronPreview\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-16 h-16 border-2 border-red-500/30 border-t-red-500 rounded-full animate-spin mx-auto\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n            lineNumber: 102,\n            columnNumber: 18\n        }, undefined)\n});\n_c12 = OptimizedMegatronPreview;\n// Optimized Sidebar Component with Memoization\nconst EnhancedSidebar = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(_s((param)=>{\n    let { isOpen, onClose, tools, selectedTool, setSelectedTool, hoveredTool, setHoveredTool } = param;\n    _s();\n    // Memoized animation variants for better performance\n    const sidebarVariants = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"EnhancedSidebar.useMemo[sidebarVariants]\": ()=>({\n                open: {\n                    x: 0,\n                    transition: {\n                        type: \"spring\",\n                        stiffness: 300,\n                        damping: 30\n                    }\n                },\n                closed: {\n                    x: -320,\n                    transition: {\n                        type: \"spring\",\n                        stiffness: 300,\n                        damping: 30\n                    }\n                }\n            })\n    }[\"EnhancedSidebar.useMemo[sidebarVariants]\"], []);\n    // Memoized handlers to prevent re-renders\n    const handleToolSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedSidebar.useCallback[handleToolSelect]\": (toolId)=>{\n            setSelectedTool(toolId);\n        }\n    }[\"EnhancedSidebar.useCallback[handleToolSelect]\"], [\n        setSelectedTool\n    ]);\n    const handleToolHover = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedSidebar.useCallback[handleToolHover]\": (toolId)=>{\n            setHoveredTool(toolId);\n        }\n    }[\"EnhancedSidebar.useCallback[handleToolHover]\"], [\n        setHoveredTool\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    transition: {\n                        duration: 0.2\n                    },\n                    onClick: onClose,\n                    className: \"fixed inset-0 bg-black/60 backdrop-blur-sm z-40 lg:hidden\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 149,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.aside, {\n                initial: \"closed\",\n                animate: isOpen ? \"open\" : \"closed\",\n                variants: sidebarVariants,\n                className: \"fixed left-0 top-0 h-screen w-[320px] z-50 lg:z-10\",\n                style: {\n                    willChange: 'transform'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black/85 backdrop-blur-xl border-r border-white/10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-br from-violet-950/15 via-black/60 to-indigo-950/15\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 h-full flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 border-b border-white/10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"flex items-center space-x-4 group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-r from-violet-500 to-indigo-500 rounded-xl blur-lg opacity-50\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                                    whileHover: {\n                                                        scale: 1.05\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.95\n                                                    },\n                                                    transition: {\n                                                        duration: 0.2\n                                                    },\n                                                    className: \"relative bg-gradient-to-r from-violet-800 to-indigo-800 rounded-xl p-3 border border-white/20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-7 h-7 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-2xl font-bold text-white\",\n                                                    children: \"Content Creator\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"Creative AI Suite\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"flex-1 p-4 space-y-2 overflow-y-auto custom-scrollbar\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                        onClick: ()=>handleToolSelect(''),\n                                        whileHover: {\n                                            x: 2\n                                        },\n                                        whileTap: {\n                                            scale: 0.98\n                                        },\n                                        transition: {\n                                            duration: 0.1\n                                        },\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"w-full flex items-center space-x-4 px-4 py-4 rounded-xl transition-all duration-200 relative\", !selectedTool ? \"bg-white/15 text-white shadow-lg border border-white/20\" : \"text-gray-400 hover:text-white hover:bg-white/8\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-2 rounded-lg bg-white/10\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 216,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-left\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-semibold\",\n                                                                children: \"Dashboard\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 219,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs opacity-70\",\n                                                                children: \"Overview & Stats\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 220,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            !selectedTool && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute right-3 w-1 h-8 bg-white rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"pt-6 pb-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 px-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-px flex-1 bg-white/20\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs font-semibold text-gray-400 uppercase tracking-wider\",\n                                                    children: \"AI Tools\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-px flex-1 bg-white/20\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/content\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                            whileHover: {\n                                                x: 2\n                                            },\n                                            whileTap: {\n                                                scale: 0.98\n                                            },\n                                            transition: {\n                                                duration: 0.2\n                                            },\n                                            className: \"w-full flex items-center space-x-4 px-4 py-4 text-gray-400 hover:text-white hover:bg-white/8 rounded-xl transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 rounded-lg bg-white/5\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"Content Library\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 252,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs opacity-70\",\n                                                            children: \"View Past Content\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 253,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    tools.map((tool)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                            onClick: ()=>handleToolSelect(tool.id),\n                                            onMouseEnter: ()=>handleToolHover(tool.id),\n                                            onMouseLeave: ()=>handleToolHover(null),\n                                            whileHover: {\n                                                x: 2\n                                            },\n                                            whileTap: {\n                                                scale: 0.98\n                                            },\n                                            transition: {\n                                                duration: 0.1\n                                            },\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"w-full flex items-center space-x-4 px-4 py-4 rounded-xl transition-all duration-200 relative\", selectedTool === tool.id ? \"bg-gradient-to-r text-white shadow-lg border border-white/20\" : \"text-gray-400 hover:text-white hover:bg-white/8\", selectedTool === tool.id && tool.color),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"p-2 rounded-lg transition-colors duration-200\", selectedTool === tool.id ? \"bg-white/20\" : \"bg-white/10\"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tool.icon, {\n                                                                className: \"w-5 h-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 283,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-left\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-semibold\",\n                                                                            children: tool.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                            lineNumber: 287,\n                                                                            columnNumber: 23\n                                                                        }, undefined),\n                                                                        tool.beta && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-1.5 py-0.5 text-xs bg-blue-500/20 text-blue-400 rounded border border-blue-500/30\",\n                                                                            children: \"BETA\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                            lineNumber: 289,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        tool.comingSoon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-1.5 py-0.5 text-xs bg-gray-500/20 text-gray-400 rounded border border-gray-500/30\",\n                                                                            children: \"SOON\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                            lineNumber: 294,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 286,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs opacity-70\",\n                                                                    children: tool.subtitle\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 299,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 285,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                selectedTool === tool.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute right-3 w-1 h-8 bg-white rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                hoveredTool === tool.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute right-3 top-3 bg-black/60 rounded-full px-2 py-1 text-xs text-white\",\n                                                    children: tool.stats.generated\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, tool.id, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 15\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border-t border-white/10 space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/settings\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                            whileHover: {\n                                                x: 2\n                                            },\n                                            whileTap: {\n                                                scale: 0.98\n                                            },\n                                            transition: {\n                                                duration: 0.2\n                                            },\n                                            className: \"w-full flex items-center space-x-4 px-4 py-3 text-gray-400 hover:text-white hover:bg-white/8 rounded-xl transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 rounded-lg bg-white/5\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: \"Settings\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                        whileHover: {\n                                            x: 2\n                                        },\n                                        whileTap: {\n                                            scale: 0.98\n                                        },\n                                        transition: {\n                                            duration: 0.2\n                                        },\n                                        className: \"w-full flex items-center space-x-4 px-4 py-3 text-gray-400 hover:text-white hover:bg-white/8 rounded-xl transition-all duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 rounded-lg bg-white/5\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"Help & Support\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n}, \"rL9lWfnAkLdZcznve+ofH+FlFk4=\"));\n_c13 = EnhancedSidebar;\n// Optimized Dashboard with Performance Enhancements\nfunction DashboardPage() {\n    _s1();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    const [selectedTool, setSelectedTool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('email-generator');\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showNotifications, setShowNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hoveredTool, setHoveredTool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userProfile, setUserProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoadingProfile, setIsLoadingProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [userStats, setUserStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoadingStats, setIsLoadingStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // ALL useCallback hooks must be at the top level and in consistent order\n    const toggleSidebar = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DashboardPage.useCallback[toggleSidebar]\": ()=>setSidebarOpen({\n                \"DashboardPage.useCallback[toggleSidebar]\": (prev)=>!prev\n            }[\"DashboardPage.useCallback[toggleSidebar]\"])\n    }[\"DashboardPage.useCallback[toggleSidebar]\"], []);\n    const toggleNotifications = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DashboardPage.useCallback[toggleNotifications]\": ()=>setShowNotifications({\n                \"DashboardPage.useCallback[toggleNotifications]\": (prev)=>!prev\n            }[\"DashboardPage.useCallback[toggleNotifications]\"])\n    }[\"DashboardPage.useCallback[toggleNotifications]\"], []);\n    const handleSearchChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DashboardPage.useCallback[handleSearchChange]\": (e)=>{\n            setSearchQuery(e.target.value);\n        }\n    }[\"DashboardPage.useCallback[handleSearchChange]\"], []);\n    // Memoized stats calculation for tools to prevent recalculation on every render\n    const getToolStats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DashboardPage.useCallback[getToolStats]\": (toolType)=>{\n            if (!(userStats === null || userStats === void 0 ? void 0 : userStats.contentBreakdown)) {\n                return {\n                    generated: 0,\n                    quality: 9.0,\n                    avgTime: '0 min'\n                };\n            }\n            const generated = userStats.contentBreakdown[toolType] || 0;\n            const quality = Math.min(9.8, 8.5 + generated * 0.05);\n            const avgTimes = {\n                'blog': '3 min',\n                'email': '1 min',\n                'social_media': '30 sec',\n                'youtube_script': '4 min',\n                'video_alchemy': '3 min'\n            };\n            return {\n                generated,\n                quality: Math.round(quality * 10) / 10,\n                avgTime: avgTimes[toolType] || '2 min'\n            };\n        }\n    }[\"DashboardPage.useCallback[getToolStats]\"], [\n        userStats === null || userStats === void 0 ? void 0 : userStats.contentBreakdown\n    ]);\n    // Memoized tools configuration to prevent recreation - MUST be before any conditional returns\n    const tools = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"DashboardPage.useMemo[tools]\": ()=>[\n                {\n                    id: 'email-generator',\n                    title: 'Email Generator',\n                    subtitle: 'Professional Emails',\n                    description: 'Generate compelling email campaigns, newsletters, and professional communications with AI-powered personalization.',\n                    icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                    color: 'from-emerald-500 to-teal-600',\n                    bgGradient: 'from-emerald-950/30 to-teal-950/30',\n                    accentColor: 'emerald',\n                    stats: getToolStats('email'),\n                    features: [\n                        'Personalization',\n                        'A/B Testing',\n                        'Professional Tone',\n                        'Quick Generation'\n                    ],\n                    href: '/email-generator'\n                },\n                {\n                    id: 'social-media-generator',\n                    title: 'Social Media',\n                    subtitle: 'Viral Content',\n                    description: 'Create engaging social media posts, captions, and content strategies that resonate with your audience across all platforms.',\n                    icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                    color: 'from-pink-500 to-rose-600',\n                    bgGradient: 'from-pink-950/30 to-rose-950/30',\n                    accentColor: 'pink',\n                    stats: getToolStats('social_media'),\n                    features: [\n                        'Multi-Platform',\n                        'Trending Hashtags',\n                        'Engagement Optimization',\n                        'Quick Generation'\n                    ],\n                    href: '/social-media-generator'\n                },\n                {\n                    id: 'blog-generator',\n                    title: 'Blog Generator',\n                    subtitle: 'SEO Optimized',\n                    description: 'Generate comprehensive, SEO-optimized blog posts with research, proper structure, and engaging content that ranks well.',\n                    icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                    color: 'from-blue-500 to-indigo-600',\n                    bgGradient: 'from-blue-950/30 to-indigo-950/30',\n                    accentColor: 'blue',\n                    stats: getToolStats('blog'),\n                    features: [\n                        'SEO Optimization',\n                        'Research Integration',\n                        'Long-form Content',\n                        'Professional Structure'\n                    ],\n                    href: '/blog-generator'\n                },\n                {\n                    id: 'youtube-script',\n                    title: 'YouTube Scripts',\n                    subtitle: 'Video Content',\n                    description: 'Create compelling YouTube video scripts with hooks, engagement techniques, and structured content for maximum viewer retention.',\n                    icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                    color: 'from-red-500 to-orange-600',\n                    bgGradient: 'from-red-950/30 to-orange-950/30',\n                    accentColor: 'red',\n                    stats: getToolStats('youtube_script'),\n                    features: [\n                        'Hook Generation',\n                        'Retention Optimization',\n                        'CTA Integration',\n                        'Script Structure'\n                    ],\n                    href: '/youtube-script'\n                },\n                {\n                    id: 'video-alchemy',\n                    title: 'Video Alchemy',\n                    subtitle: 'Coming Soon',\n                    description: 'Transform your ideas into stunning video content with AI-powered video generation and editing capabilities.',\n                    icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                    color: 'from-purple-500 to-violet-600',\n                    bgGradient: 'from-purple-950/30 to-violet-950/30',\n                    accentColor: 'purple',\n                    stats: getToolStats('video_alchemy'),\n                    features: [\n                        'AI Video Generation',\n                        'Auto Editing',\n                        'Style Transfer',\n                        'Quick Export'\n                    ],\n                    href: '#',\n                    comingSoon: true\n                },\n                {\n                    id: 'invincible-writer',\n                    title: 'Invincible Writer',\n                    subtitle: 'Multi-Agent AI',\n                    description: 'Professional content creation with 5 specialized AI agents working together: Research, SEO, Architecture, Creation, and Quality.',\n                    icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n                    color: 'from-blue-500 to-cyan-600',\n                    bgGradient: 'from-blue-950/30 to-cyan-950/30',\n                    accentColor: 'blue',\n                    stats: getToolStats('invincible_writer'),\n                    features: [\n                        '5-Agent System',\n                        'SEO Optimized',\n                        'Real-time Progress',\n                        'Cost Effective'\n                    ],\n                    href: '/content-writer'\n                },\n                {\n                    id: 'megatron',\n                    title: 'Megatron',\n                    subtitle: 'Ultimate AI',\n                    description: 'The most powerful AI assistant for complex tasks, research, analysis, and creative projects with unlimited potential.',\n                    icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                    color: 'from-gray-600 to-slate-700',\n                    bgGradient: 'from-gray-950/30 to-slate-950/30',\n                    accentColor: 'gray',\n                    stats: getToolStats('megatron'),\n                    features: [\n                        'Advanced Reasoning',\n                        'Multi-task Handling',\n                        'Research Capabilities',\n                        'Creative Solutions'\n                    ],\n                    href: '#',\n                    comingSoon: true\n                }\n            ]\n    }[\"DashboardPage.useMemo[tools]\"], [\n        getToolStats\n    ]);\n    // Redirect to login if not authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            if (status === 'unauthenticated') {\n                router.push('/login');\n            }\n        }\n    }[\"DashboardPage.useEffect\"], [\n        status,\n        router\n    ]);\n    // Fetch user profile data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            if (session === null || session === void 0 ? void 0 : session.user) {\n                fetchUserProfile();\n                fetchUserStats();\n            }\n        }\n    }[\"DashboardPage.useEffect\"], [\n        session\n    ]);\n    const fetchUserProfile = async ()=>{\n        try {\n            const response = await fetch('/api/user/profile');\n            if (response.ok) {\n                const data = await response.json();\n                setUserProfile(data);\n            }\n        } catch (error) {\n            console.error('Error fetching user profile:', error);\n        } finally{\n            setIsLoadingProfile(false);\n        }\n    };\n    const fetchUserStats = async ()=>{\n        try {\n            const response = await fetch('/api/stats');\n            if (response.ok) {\n                const data = await response.json();\n                setUserStats(data.stats);\n            }\n        } catch (error) {\n            console.error('Error fetching user stats:', error);\n        } finally{\n            setIsLoadingStats(false);\n        }\n    };\n    // Generate user initials for avatar\n    const getUserInitials = ()=>{\n        if ((userProfile === null || userProfile === void 0 ? void 0 : userProfile.firstName) && (userProfile === null || userProfile === void 0 ? void 0 : userProfile.lastName)) {\n            return \"\".concat(userProfile.firstName[0]).concat(userProfile.lastName[0]);\n        } else if (userProfile === null || userProfile === void 0 ? void 0 : userProfile.name) {\n            const names = userProfile.name.split(' ');\n            return names.length > 1 ? \"\".concat(names[0][0]).concat(names[names.length - 1][0]) : names[0][0];\n        } else if (userProfile === null || userProfile === void 0 ? void 0 : userProfile.email) {\n            return userProfile.email[0].toUpperCase();\n        }\n        return 'U';\n    };\n    // Get display name\n    const getDisplayName = ()=>{\n        if ((userProfile === null || userProfile === void 0 ? void 0 : userProfile.firstName) && (userProfile === null || userProfile === void 0 ? void 0 : userProfile.lastName)) {\n            return \"\".concat(userProfile.firstName, \" \").concat(userProfile.lastName);\n        } else if (userProfile === null || userProfile === void 0 ? void 0 : userProfile.name) {\n            return userProfile.name;\n        } else if (userProfile === null || userProfile === void 0 ? void 0 : userProfile.email) {\n            return userProfile.email.split('@')[0];\n        }\n        return 'User';\n    };\n    // Loading state\n    if (status === 'loading' || isLoadingProfile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin w-12 h-12 border-2 border-violet-400 border-t-transparent rounded-full mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 608,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"Loading your dashboard...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 609,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 607,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n            lineNumber: 606,\n            columnNumber: 7\n        }, this);\n    }\n    // Don't render if not authenticated\n    if (status === 'unauthenticated') {\n        return null;\n    }\n    const activeTool = tools.find((t)=>t.id === selectedTool) || tools[0];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-black\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-violet-950/10 via-black to-indigo-950/10\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 626,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        transition: {\n                            duration: 0.5,\n                            ease: [\n                                0.4,\n                                0,\n                                0.2,\n                                1\n                            ]\n                        },\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"absolute inset-0 bg-gradient-to-br opacity-20\", activeTool.bgGradient)\n                    }, activeTool.id, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 629,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-1/4 left-1/4 w-[500px] h-[500px] bg-violet-700/10 rounded-full blur-[100px] animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 641,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-1/4 right-1/4 w-[600px] h-[600px] bg-indigo-700/10 rounded-full blur-[120px] animate-pulse\",\n                        style: {\n                            animationDelay: '1s'\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 642,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 625,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 flex min-h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EnhancedSidebar, {\n                        isOpen: sidebarOpen,\n                        onClose: ()=>setSidebarOpen(false),\n                        tools: tools,\n                        selectedTool: selectedTool,\n                        setSelectedTool: setSelectedTool,\n                        hoveredTool: hoveredTool,\n                        setHoveredTool: setHoveredTool\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 648,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                        onClick: toggleSidebar,\n                        animate: {\n                            left: sidebarOpen ? 308 : 20\n                        },\n                        transition: {\n                            type: \"spring\",\n                            stiffness: 300,\n                            damping: 30\n                        },\n                        whileHover: {\n                            scale: 1.02\n                        },\n                        whileTap: {\n                            scale: 0.98\n                        },\n                        className: \"fixed top-6 z-50 p-3 bg-black/80 backdrop-blur-sm border border-white/20 rounded-xl hover:bg-black/90 transition-colors duration-200\",\n                        style: {\n                            willChange: 'transform'\n                        },\n                        children: sidebarOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                            className: \"w-5 h-5 text-white\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                            lineNumber: 674,\n                            columnNumber: 26\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            className: \"w-5 h-5 text-white\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                            lineNumber: 674,\n                            columnNumber: 75\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 659,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.main, {\n                        animate: {\n                            marginLeft: sidebarOpen ? 320 : 0\n                        },\n                        transition: {\n                            type: \"tween\",\n                            duration: 0.3,\n                            ease: [\n                                0.4,\n                                0,\n                                0.2,\n                                1\n                            ]\n                        },\n                        className: \"flex-1 min-h-screen\",\n                        style: {\n                            willChange: 'margin-left'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                                className: \"sticky top-0 z-40 backdrop-blur-xl bg-black/60 border-b border-white/10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                    animate: {\n                                        paddingLeft: sidebarOpen ? 32 : 80\n                                    },\n                                    transition: {\n                                        type: \"tween\",\n                                        duration: 0.3,\n                                        ease: [\n                                            0.4,\n                                            0,\n                                            0.2,\n                                            1\n                                        ]\n                                    },\n                                    className: \"px-8 py-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-white\",\n                                                        children: selectedTool ? activeTool.title : 'Dashboard Overview'\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 703,\n                                                        columnNumber: 39\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 709,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                placeholder: \"Search tools, features...\",\n                                                                value: searchQuery,\n                                                                onChange: handleSearchChange,\n                                                                className: \"w-96 pl-12 pr-4 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/15 focus:border-violet-500/50 focus:outline-none transition-colors duration-200\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 710,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 708,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 702,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"relative p-3 text-gray-400 hover:text-white transition-colors duration-200 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl hover:bg-white/15\",\n                                                        onClick: toggleNotifications,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"w-5 h-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 726,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"absolute top-2 right-2 w-2 h-2 bg-violet-500 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 727,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 722,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-8 w-px bg-white/20\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 730,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProfileButton__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        userProfile: userProfile,\n                                                        className: \"pl-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 732,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 721,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 701,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 692,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 691,\n                                columnNumber: 23\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                animate: {\n                                    paddingLeft: sidebarOpen ? 32 : 80\n                                },\n                                transition: {\n                                    type: \"tween\",\n                                    duration: 0.3,\n                                    ease: [\n                                        0.4,\n                                        0,\n                                        0.2,\n                                        1\n                                    ]\n                                },\n                                className: \"p-8 pb-16 pr-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                                    mode: \"wait\",\n                                    children: selectedTool ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToolDetails, {\n                                        tool: activeTool\n                                    }, selectedTool, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 755,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DashboardOverview, {\n                                        tools: tools,\n                                        userProfile: userProfile,\n                                        getDisplayName: getDisplayName,\n                                        userStats: userStats,\n                                        isLoadingStats: isLoadingStats\n                                    }, \"overview\", false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 757,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 753,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 742,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 678,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 646,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n        lineNumber: 623,\n        columnNumber: 5\n    }, this);\n}\n_s1(DashboardPage, \"Z4M791NIuitpzA6PhOvnrCXO6M0=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_4__.useSession,\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter\n    ];\n});\n_c14 = DashboardPage;\n// Optimized Tool Details Component with Memoization\nconst ToolDetails = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo((param)=>{\n    let { tool } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n        initial: {\n            opacity: 0,\n            scale: 0.95\n        },\n        animate: {\n            opacity: 1,\n            scale: 1\n        },\n        exit: {\n            opacity: 0,\n            scale: 0.95\n        },\n        transition: {\n            duration: 0.2,\n            ease: \"easeOut\"\n        },\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.3,\n                    delay: 0.1\n                },\n                className: \"relative overflow-hidden rounded-3xl backdrop-blur-xl border p-8 bg-gradient-to-br from-white/5 to-white/0 border-white/10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"absolute inset-0 bg-gradient-to-br opacity-20\", tool.color)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 786,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"p-4 rounded-2xl text-white shadow-2xl bg-gradient-to-br\", tool.color),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tool.icon, {\n                                                    className: \"w-8 h-8\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 798,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 794,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-3xl font-bold text-white\",\n                                                        children: tool.title\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 801,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg text-gray-300\",\n                                                        children: tool.subtitle\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 802,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 800,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 793,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300 leading-relaxed\",\n                                        children: tool.description\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 806,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-3\",\n                                        children: tool.features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-4 py-2 rounded-full text-sm text-white border bg-white/10 backdrop-blur-sm border-white/20\",\n                                                children: feature\n                                            }, index, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 812,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 810,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: tool.href,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                                whileHover: {\n                                                    scale: 1.05\n                                                },\n                                                whileTap: {\n                                                    scale: 0.95\n                                                },\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"px-12 py-6 rounded-2xl font-bold text-lg text-white shadow-2xl transition-all flex items-center space-x-3 bg-gradient-to-r\", tool.color),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                        className: \"w-6 h-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 831,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Launch \",\n                                                            tool.title\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 832,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 823,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 822,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 821,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 792,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative h-[400px] rounded-2xl overflow-hidden border bg-black/40 border-white/10\",\n                                children: tool.preview || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tool.icon, {\n                                                className: \"w-24 h-24 text-white/20 mx-auto\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 843,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400\",\n                                                children: \"Interactive preview coming soon\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 844,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 842,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 841,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 839,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 791,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 780,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.3,\n                    delay: 0.2\n                },\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/content?type=\".concat(tool.id === 'blog-generator' ? 'blog' : tool.id === 'email-generator' ? 'email' : tool.id === 'youtube-script' ? 'youtube_script' : tool.id.replace('-', '_')),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                delay: 0.25\n                            },\n                            className: \"backdrop-blur-xl border rounded-2xl p-6 cursor-pointer hover:bg-white/10 transition-all duration-200 group bg-white/5 border-white/10 hover:border-white/20\",\n                            title: \"View all \".concat(tool.title, \" content in your library\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"w-8 h-8 group-hover:scale-110 transition-transform duration-200\", \"text-\".concat(tool.accentColor, \"-400\"))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 868,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    className: \"w-5 h-5 text-emerald-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 870,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    className: \"w-4 h-4 text-gray-400 group-hover:text-white transition-colors\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 871,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 869,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 867,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-3xl font-bold text-white mb-1 group-hover:text-violet-200 transition-colors\",\n                                    children: tool.stats.generated\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 874,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-400 group-hover:text-gray-300 transition-colors\",\n                                    children: \"Content Generated - Click to view\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 875,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center text-xs text-violet-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                className: \"w-3 h-3 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 880,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"View in Content Library\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 881,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 879,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 878,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                            lineNumber: 860,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 859,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 0.35\n                        },\n                        className: \"backdrop-blur-xl border rounded-2xl p-6 bg-white/5 border-white/10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"w-8 h-8\", \"text-\".concat(tool.accentColor, \"-400\"))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 894,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            1,\n                                            2,\n                                            3,\n                                            4,\n                                            5\n                                        ].map((star)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"w-3 h-3 rounded-full\", star <= Math.round(tool.stats.quality / 2) ? \"bg-yellow-400\" : \"bg-gray-600\")\n                                            }, star, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 897,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 895,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 893,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-3xl font-bold text-white mb-1\",\n                                children: [\n                                    tool.stats.quality,\n                                    \"/10\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 909,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-400\",\n                                children: \"Quality Score\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 910,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 887,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 0.45\n                        },\n                        className: \"backdrop-blur-xl border rounded-2xl p-6 bg-white/5 border-white/10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"w-8 h-8\", \"text-\".concat(tool.accentColor, \"-400\"))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 920,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                        className: \"w-5 h-5 text-blue-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 921,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 919,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-3xl font-bold text-white mb-1\",\n                                children: tool.stats.avgTime\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 923,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-400\",\n                                children: \"Average Time\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 924,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 913,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 853,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n        lineNumber: 772,\n        columnNumber: 5\n    }, undefined);\n});\n_c15 = ToolDetails;\n// Optimized Dashboard Overview Component with Memoization\nconst DashboardOverview = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo((param)=>{\n    let { tools, userProfile, getDisplayName, userStats, isLoadingStats } = param;\n    var _userStats_totalContent, _userStats_trends, _userStats_trends1, _userStats_trends2, _userStats_trends_toolsActive, _userStats_trends3, _userStats_trends4;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n        initial: {\n            opacity: 0,\n            scale: 0.95\n        },\n        animate: {\n            opacity: 1,\n            scale: 1\n        },\n        exit: {\n            opacity: 0,\n            scale: 0.95\n        },\n        transition: {\n            duration: 0.2,\n            ease: \"easeOut\"\n        },\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-violet-800/20 to-indigo-800/20 backdrop-blur-xl border border-white/10 rounded-3xl p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl font-bold text-white mb-4\",\n                        children: [\n                            \"Welcome back, \",\n                            getDisplayName(),\n                            \"! ✨\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 949,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-300 mb-6\",\n                        children: \"Your creative AI toolkit is ready. What will you create today?\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 950,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                        children: isLoadingStats ? // Loading skeleton\n                        Array.from({\n                            length: 4\n                        }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: index * 0.1\n                                },\n                                className: \"bg-black/40 backdrop-blur-sm border border-white/10 rounded-xl p-4 animate-pulse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-5 bg-white/10 rounded mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 963,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-8 bg-white/10 rounded mb-1\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 964,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-white/10 rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 965,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, index, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 956,\n                                columnNumber: 15\n                            }, undefined)) : [\n                            {\n                                label: 'Total Created',\n                                value: (userStats === null || userStats === void 0 ? void 0 : (_userStats_totalContent = userStats.totalContent) === null || _userStats_totalContent === void 0 ? void 0 : _userStats_totalContent.toString()) || '0',\n                                icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                                change: (userStats === null || userStats === void 0 ? void 0 : (_userStats_trends = userStats.trends) === null || _userStats_trends === void 0 ? void 0 : _userStats_trends.contentGrowth) || '+0%'\n                            },\n                            {\n                                label: 'Time Saved',\n                                value: \"\".concat((userStats === null || userStats === void 0 ? void 0 : userStats.timeSavedHours) || 0, \" hrs\"),\n                                icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"],\n                                change: (userStats === null || userStats === void 0 ? void 0 : (_userStats_trends1 = userStats.trends) === null || _userStats_trends1 === void 0 ? void 0 : _userStats_trends1.timeEfficiency) || '+0%'\n                            },\n                            {\n                                label: 'Quality Score',\n                                value: \"\".concat((userStats === null || userStats === void 0 ? void 0 : userStats.qualityScore) || 9.0, \"/10\"),\n                                icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"],\n                                change: (userStats === null || userStats === void 0 ? void 0 : (_userStats_trends2 = userStats.trends) === null || _userStats_trends2 === void 0 ? void 0 : _userStats_trends2.qualityImprovement) || '+0.0'\n                            },\n                            {\n                                label: 'Active Tools',\n                                value: (userStats === null || userStats === void 0 ? void 0 : (_userStats_trends3 = userStats.trends) === null || _userStats_trends3 === void 0 ? void 0 : (_userStats_trends_toolsActive = _userStats_trends3.toolsActive) === null || _userStats_trends_toolsActive === void 0 ? void 0 : _userStats_trends_toolsActive.toString()) || '0',\n                                icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                                change: '+' + ((userStats === null || userStats === void 0 ? void 0 : (_userStats_trends4 = userStats.trends) === null || _userStats_trends4 === void 0 ? void 0 : _userStats_trends4.toolsActive) || 0)\n                            }\n                        ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: index * 0.1\n                                },\n                                className: \"bg-black/40 backdrop-blur-sm border border-white/10 rounded-xl p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                                className: \"w-5 h-5 text-violet-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 1003,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-emerald-400\",\n                                                children: stat.change\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 1004,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 1002,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-white\",\n                                        children: stat.value\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 1006,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: stat.label\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 1007,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, stat.label, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 995,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 952,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 948,\n                columnNumber: 15\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white mb-6\",\n                        children: \"Your AI Tools\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 1016,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                        children: tools.map((tool, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: tool.href,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        scale: 0.9\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        scale: 1\n                                    },\n                                    transition: {\n                                        delay: index * 0.1\n                                    },\n                                    whileHover: {\n                                        y: -8\n                                    },\n                                    className: \"group relative cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl blur-xl\", tool.color)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 1027,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative backdrop-blur-xl border transition-all bg-black/60 border-white/10 hover:border-white/20 rounded-2xl\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start justify-between mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"p-3 rounded-xl text-white bg-gradient-to-br\", tool.color),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tool.icon, {\n                                                                    className: \"w-6 h-6\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 1040,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 1036,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                className: \"w-5 h-5 text-gray-400 group-hover:text-white transition-colors\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 1042,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 1035,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-white mb-1\",\n                                                        children: tool.title\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 1045,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-400 mb-4\",\n                                                        children: tool.subtitle\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 1046,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between text-xs\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: \"/content?type=\".concat(tool.id === 'blog-generator' ? 'blog' : tool.id === 'email-generator' ? 'email' : tool.id === 'youtube-script' ? 'youtube_script' : tool.id === 'invincible-writer' ? 'invincible_writer' : tool.id.replace('-', '_')),\n                                                                onClick: (e)=>e.stopPropagation(),\n                                                                className: \"hover:bg-white/10 rounded px-2 py-1 transition-colors group/stat\",\n                                                                title: \"View all \".concat(tool.title, \" content\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-500 group-hover/stat:text-gray-400\",\n                                                                        children: \"Generated\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 1055,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-white font-medium group-hover/stat:text-violet-200\",\n                                                                                children: tool.stats.generated\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                                lineNumber: 1057,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                className: \"w-3 h-3 text-gray-400 group-hover/stat:text-white opacity-0 group-hover/stat:opacity-100 transition-all\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                                lineNumber: 1058,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 1056,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black/90 text-white text-xs rounded opacity-0 group-hover/stat:opacity-100 transition-opacity pointer-events-none whitespace-nowrap\",\n                                                                        children: \"Click to view content\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 1061,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 1049,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-right\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-500\",\n                                                                        children: \"Quality\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 1066,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-white font-medium\",\n                                                                        children: [\n                                                                            tool.stats.quality,\n                                                                            \"/10\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 1067,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 1065,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 1048,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 1034,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 1032,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 1020,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, tool.id, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 1019,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 1017,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 1015,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(OptimizedRecentContent, {\n                    limit: 5,\n                    showFilters: true\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                    lineNumber: 1080,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 1079,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n        lineNumber: 940,\n        columnNumber: 5\n    }, undefined);\n});\n_c16 = DashboardOverview;\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15, _c16;\n$RefreshReg$(_c, \"OptimizedRecentContent\");\n$RefreshReg$(_c1, \"OptimizedBlogPreview$dynamic\");\n$RefreshReg$(_c2, \"OptimizedBlogPreview\");\n$RefreshReg$(_c3, \"OptimizedEmailPreview$dynamic\");\n$RefreshReg$(_c4, \"OptimizedEmailPreview\");\n$RefreshReg$(_c5, \"OptimizedSocialMediaPreview$dynamic\");\n$RefreshReg$(_c6, \"OptimizedSocialMediaPreview\");\n$RefreshReg$(_c7, \"OptimizedVideoScriptPreview$dynamic\");\n$RefreshReg$(_c8, \"OptimizedVideoScriptPreview\");\n$RefreshReg$(_c9, \"OptimizedVideoAlchemyPreview$dynamic\");\n$RefreshReg$(_c10, \"OptimizedVideoAlchemyPreview\");\n$RefreshReg$(_c11, \"OptimizedMegatronPreview$dynamic\");\n$RefreshReg$(_c12, \"OptimizedMegatronPreview\");\n$RefreshReg$(_c13, \"EnhancedSidebar\");\n$RefreshReg$(_c14, \"DashboardPage\");\n$RefreshReg$(_c15, \"ToolDetails\");\n$RefreshReg$(_c16, \"DashboardOverview\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});