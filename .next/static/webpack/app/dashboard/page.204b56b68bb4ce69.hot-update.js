"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layers.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-help.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/video.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ProfileButton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ProfileButton */ \"(app-pages-browser)/./src/components/ProfileButton.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Lazy load RecentContent to reduce initial bundle size\nconst OptimizedRecentContent = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_dashboard_RecentContent_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/dashboard/RecentContent */ \"(app-pages-browser)/./src/components/dashboard/RecentContent.tsx\")).then((mod)=>({\n            default: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(mod.default)\n        })), {\n    loadableGenerated: {\n        modules: [\n            \"app/dashboard/page.tsx -> \" + \"@/components/dashboard/RecentContent\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: Array.from({\n                length: 3\n            }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white/10 rounded-lg p-4 animate-pulse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 bg-white/10 rounded mb-2 w-3/4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-3 bg-white/10 rounded w-1/2\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, i, true, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n            lineNumber: 63,\n            columnNumber: 5\n        }, undefined)\n});\n_c = OptimizedRecentContent;\nconst OptimizedBlogPreview = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_c1 = ()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_BlogPreview_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/BlogPreview */ \"(app-pages-browser)/./src/components/BlogPreview.tsx\")).then((mod)=>({\n            default: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(mod.default)\n        })), {\n    loadableGenerated: {\n        modules: [\n            \"app/dashboard/page.tsx -> \" + \"@/components/BlogPreview\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-16 h-16 border-2 border-pink-500/30 border-t-pink-500 rounded-full animate-spin mx-auto\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n            lineNumber: 77,\n            columnNumber: 18\n        }, undefined)\n});\n_c2 = OptimizedBlogPreview;\nconst OptimizedEmailPreview = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_c3 = ()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_EmailPreview_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/EmailPreview */ \"(app-pages-browser)/./src/components/EmailPreview.tsx\")).then((mod)=>({\n            default: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(mod.default)\n        })), {\n    loadableGenerated: {\n        modules: [\n            \"app/dashboard/page.tsx -> \" + \"@/components/EmailPreview\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-16 h-16 border-2 border-emerald-500/30 border-t-emerald-500 rounded-full animate-spin mx-auto\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n            lineNumber: 82,\n            columnNumber: 18\n        }, undefined)\n});\n_c4 = OptimizedEmailPreview;\nconst OptimizedSocialMediaPreview = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_c5 = ()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_SocialMediaPreview_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/SocialMediaPreview */ \"(app-pages-browser)/./src/components/SocialMediaPreview.tsx\")).then((mod)=>({\n            default: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(mod.default)\n        })), {\n    loadableGenerated: {\n        modules: [\n            \"app/dashboard/page.tsx -> \" + \"@/components/SocialMediaPreview\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-16 h-16 border-2 border-blue-500/30 border-t-blue-500 rounded-full animate-spin mx-auto\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n            lineNumber: 87,\n            columnNumber: 18\n        }, undefined)\n});\n_c6 = OptimizedSocialMediaPreview;\nconst OptimizedVideoScriptPreview = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_c7 = ()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_VideoScriptPreview_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/VideoScriptPreview */ \"(app-pages-browser)/./src/components/VideoScriptPreview.tsx\")).then((mod)=>({\n            default: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(mod.default)\n        })), {\n    loadableGenerated: {\n        modules: [\n            \"app/dashboard/page.tsx -> \" + \"@/components/VideoScriptPreview\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-16 h-16 border-2 border-red-500/30 border-t-red-500 rounded-full animate-spin mx-auto\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n            lineNumber: 92,\n            columnNumber: 18\n        }, undefined)\n});\n_c8 = OptimizedVideoScriptPreview;\nconst OptimizedVideoAlchemyPreview = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_c9 = ()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_VideoAlchemyPreview_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/VideoAlchemyPreview */ \"(app-pages-browser)/./src/components/VideoAlchemyPreview.tsx\")).then((mod)=>({\n            default: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(mod.default)\n        })), {\n    loadableGenerated: {\n        modules: [\n            \"app/dashboard/page.tsx -> \" + \"@/components/VideoAlchemyPreview\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-16 h-16 border-2 border-purple-500/30 border-t-purple-500 rounded-full animate-spin mx-auto\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n            lineNumber: 97,\n            columnNumber: 18\n        }, undefined)\n});\n_c10 = OptimizedVideoAlchemyPreview;\nconst OptimizedMegatronPreview = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_c11 = ()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_MegatronPreview_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/MegatronPreview */ \"(app-pages-browser)/./src/components/MegatronPreview.tsx\")).then((mod)=>({\n            default: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(mod.default)\n        })), {\n    loadableGenerated: {\n        modules: [\n            \"app/dashboard/page.tsx -> \" + \"@/components/MegatronPreview\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-16 h-16 border-2 border-red-500/30 border-t-red-500 rounded-full animate-spin mx-auto\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n            lineNumber: 102,\n            columnNumber: 18\n        }, undefined)\n});\n_c12 = OptimizedMegatronPreview;\n// Optimized Sidebar Component with Memoization\nconst EnhancedSidebar = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(_s((param)=>{\n    let { isOpen, onClose, tools, selectedTool, setSelectedTool, hoveredTool, setHoveredTool } = param;\n    _s();\n    // Memoized animation variants for better performance\n    const sidebarVariants = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"EnhancedSidebar.useMemo[sidebarVariants]\": ()=>({\n                open: {\n                    x: 0,\n                    transition: {\n                        type: \"spring\",\n                        stiffness: 300,\n                        damping: 30\n                    }\n                },\n                closed: {\n                    x: -320,\n                    transition: {\n                        type: \"spring\",\n                        stiffness: 300,\n                        damping: 30\n                    }\n                }\n            })\n    }[\"EnhancedSidebar.useMemo[sidebarVariants]\"], []);\n    // Memoized handlers to prevent re-renders\n    const handleToolSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedSidebar.useCallback[handleToolSelect]\": (toolId)=>{\n            setSelectedTool(toolId);\n        }\n    }[\"EnhancedSidebar.useCallback[handleToolSelect]\"], [\n        setSelectedTool\n    ]);\n    const handleToolHover = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedSidebar.useCallback[handleToolHover]\": (toolId)=>{\n            setHoveredTool(toolId);\n        }\n    }[\"EnhancedSidebar.useCallback[handleToolHover]\"], [\n        setHoveredTool\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    transition: {\n                        duration: 0.2\n                    },\n                    onClick: onClose,\n                    className: \"fixed inset-0 bg-black/60 backdrop-blur-sm z-40 lg:hidden\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 149,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.aside, {\n                initial: \"closed\",\n                animate: isOpen ? \"open\" : \"closed\",\n                variants: sidebarVariants,\n                className: \"fixed left-0 top-0 h-screen w-[320px] z-50 lg:z-10\",\n                style: {\n                    willChange: 'transform'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black/85 backdrop-blur-xl border-r border-white/10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-br from-violet-950/15 via-black/60 to-indigo-950/15\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 h-full flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 border-b border-white/10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"flex items-center space-x-4 group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-r from-violet-500 to-indigo-500 rounded-xl blur-lg opacity-50\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                                    whileHover: {\n                                                        scale: 1.05\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.95\n                                                    },\n                                                    transition: {\n                                                        duration: 0.2\n                                                    },\n                                                    className: \"relative bg-gradient-to-r from-violet-800 to-indigo-800 rounded-xl p-3 border border-white/20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-7 h-7 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-2xl font-bold text-white\",\n                                                    children: \"Content Creator\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"Creative AI Suite\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"flex-1 p-4 space-y-2 overflow-y-auto custom-scrollbar\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                        onClick: ()=>handleToolSelect(''),\n                                        whileHover: {\n                                            x: 2\n                                        },\n                                        whileTap: {\n                                            scale: 0.98\n                                        },\n                                        transition: {\n                                            duration: 0.1\n                                        },\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"w-full flex items-center space-x-4 px-4 py-4 rounded-xl transition-all duration-200 relative\", !selectedTool ? \"bg-white/15 text-white shadow-lg border border-white/20\" : \"text-gray-400 hover:text-white hover:bg-white/8\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-2 rounded-lg bg-white/10\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 216,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-left\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-semibold\",\n                                                                children: \"Dashboard\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 219,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs opacity-70\",\n                                                                children: \"Overview & Stats\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 220,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            !selectedTool && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute right-3 w-1 h-8 bg-white rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"pt-6 pb-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 px-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-px flex-1 bg-white/20\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs font-semibold text-gray-400 uppercase tracking-wider\",\n                                                    children: \"AI Tools\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-px flex-1 bg-white/20\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/content\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                            whileHover: {\n                                                x: 2\n                                            },\n                                            whileTap: {\n                                                scale: 0.98\n                                            },\n                                            transition: {\n                                                duration: 0.2\n                                            },\n                                            className: \"w-full flex items-center space-x-4 px-4 py-4 text-gray-400 hover:text-white hover:bg-white/8 rounded-xl transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 rounded-lg bg-white/5\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"Content Library\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 252,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs opacity-70\",\n                                                            children: \"View Past Content\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 253,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    tools.map((tool)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                            onClick: ()=>handleToolSelect(tool.id),\n                                            onMouseEnter: ()=>handleToolHover(tool.id),\n                                            onMouseLeave: ()=>handleToolHover(null),\n                                            whileHover: {\n                                                x: 2\n                                            },\n                                            whileTap: {\n                                                scale: 0.98\n                                            },\n                                            transition: {\n                                                duration: 0.1\n                                            },\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"w-full flex items-center space-x-4 px-4 py-4 rounded-xl transition-all duration-200 relative\", selectedTool === tool.id ? \"bg-gradient-to-r text-white shadow-lg border border-white/20\" : \"text-gray-400 hover:text-white hover:bg-white/8\", selectedTool === tool.id && tool.color),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"p-2 rounded-lg transition-colors duration-200\", selectedTool === tool.id ? \"bg-white/20\" : \"bg-white/10\"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tool.icon, {\n                                                                className: \"w-5 h-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 283,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-left\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-semibold\",\n                                                                            children: tool.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                            lineNumber: 287,\n                                                                            columnNumber: 23\n                                                                        }, undefined),\n                                                                        tool.beta && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-1.5 py-0.5 text-xs bg-blue-500/20 text-blue-400 rounded border border-blue-500/30\",\n                                                                            children: \"BETA\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                            lineNumber: 289,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        tool.comingSoon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-1.5 py-0.5 text-xs bg-gray-500/20 text-gray-400 rounded border border-gray-500/30\",\n                                                                            children: \"SOON\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                            lineNumber: 294,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 286,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs opacity-70\",\n                                                                    children: tool.subtitle\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 299,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 285,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                selectedTool === tool.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute right-3 w-1 h-8 bg-white rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                hoveredTool === tool.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute right-3 top-3 bg-black/60 rounded-full px-2 py-1 text-xs text-white\",\n                                                    children: tool.stats.generated\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, tool.id, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 15\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border-t border-white/10 space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/settings\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                            whileHover: {\n                                                x: 2\n                                            },\n                                            whileTap: {\n                                                scale: 0.98\n                                            },\n                                            transition: {\n                                                duration: 0.2\n                                            },\n                                            className: \"w-full flex items-center space-x-4 px-4 py-3 text-gray-400 hover:text-white hover:bg-white/8 rounded-xl transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 rounded-lg bg-white/5\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: \"Settings\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                        whileHover: {\n                                            x: 2\n                                        },\n                                        whileTap: {\n                                            scale: 0.98\n                                        },\n                                        transition: {\n                                            duration: 0.2\n                                        },\n                                        className: \"w-full flex items-center space-x-4 px-4 py-3 text-gray-400 hover:text-white hover:bg-white/8 rounded-xl transition-all duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 rounded-lg bg-white/5\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"Help & Support\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n}, \"rL9lWfnAkLdZcznve+ofH+FlFk4=\"));\n_c13 = EnhancedSidebar;\n// Optimized Dashboard with Performance Enhancements\nfunction DashboardPage() {\n    _s1();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    const [selectedTool, setSelectedTool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('email-generator');\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showNotifications, setShowNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hoveredTool, setHoveredTool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userProfile, setUserProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoadingProfile, setIsLoadingProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [userStats, setUserStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoadingStats, setIsLoadingStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // ALL useCallback hooks must be at the top level and in consistent order\n    const toggleSidebar = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DashboardPage.useCallback[toggleSidebar]\": ()=>setSidebarOpen({\n                \"DashboardPage.useCallback[toggleSidebar]\": (prev)=>!prev\n            }[\"DashboardPage.useCallback[toggleSidebar]\"])\n    }[\"DashboardPage.useCallback[toggleSidebar]\"], []);\n    const toggleNotifications = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DashboardPage.useCallback[toggleNotifications]\": ()=>setShowNotifications({\n                \"DashboardPage.useCallback[toggleNotifications]\": (prev)=>!prev\n            }[\"DashboardPage.useCallback[toggleNotifications]\"])\n    }[\"DashboardPage.useCallback[toggleNotifications]\"], []);\n    const handleSearchChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DashboardPage.useCallback[handleSearchChange]\": (e)=>{\n            setSearchQuery(e.target.value);\n        }\n    }[\"DashboardPage.useCallback[handleSearchChange]\"], []);\n    // Memoized stats calculation for tools to prevent recalculation on every render\n    const getToolStats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DashboardPage.useCallback[getToolStats]\": (toolType)=>{\n            if (!(userStats === null || userStats === void 0 ? void 0 : userStats.contentBreakdown)) {\n                return {\n                    generated: 0,\n                    quality: 9.0,\n                    avgTime: '0 min'\n                };\n            }\n            const generated = userStats.contentBreakdown[toolType] || 0;\n            const quality = Math.min(9.8, 8.5 + generated * 0.05);\n            const avgTimes = {\n                'blog': '3 min',\n                'email': '1 min',\n                'social_media': '30 sec',\n                'youtube_script': '4 min',\n                'video_alchemy': '3 min'\n            };\n            return {\n                generated,\n                quality: Math.round(quality * 10) / 10,\n                avgTime: avgTimes[toolType] || '2 min'\n            };\n        }\n    }[\"DashboardPage.useCallback[getToolStats]\"], [\n        userStats === null || userStats === void 0 ? void 0 : userStats.contentBreakdown\n    ]);\n    // Memoized tools configuration to prevent recreation - MUST be before any conditional returns\n    const tools = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"DashboardPage.useMemo[tools]\": ()=>[\n                {\n                    id: 'email-generator',\n                    title: 'Email Generator',\n                    subtitle: 'Professional Emails',\n                    description: 'Generate compelling email campaigns, newsletters, and professional communications with AI-powered personalization.',\n                    icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                    color: 'from-emerald-500 to-teal-600',\n                    bgGradient: 'from-emerald-950/30 to-teal-950/30',\n                    accentColor: 'emerald',\n                    stats: getToolStats('email'),\n                    features: [\n                        'Personalization',\n                        'A/B Testing',\n                        'Professional Tone',\n                        'Quick Generation'\n                    ],\n                    href: '/email-generator'\n                },\n                {\n                    id: 'social-media-generator',\n                    title: 'Social Media',\n                    subtitle: 'Viral Content',\n                    description: 'Create engaging social media posts, captions, and content strategies that resonate with your audience across all platforms.',\n                    icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                    color: 'from-pink-500 to-rose-600',\n                    bgGradient: 'from-pink-950/30 to-rose-950/30',\n                    accentColor: 'pink',\n                    stats: getToolStats('social_media'),\n                    features: [\n                        'Multi-Platform',\n                        'Trending Hashtags',\n                        'Engagement Optimization',\n                        'Quick Generation'\n                    ],\n                    href: '/social-media-generator'\n                },\n                {\n                    id: 'blog-generator',\n                    title: 'Blog Generator',\n                    subtitle: 'SEO Optimized',\n                    description: 'Generate comprehensive, SEO-optimized blog posts with research, proper structure, and engaging content that ranks well.',\n                    icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                    color: 'from-blue-500 to-indigo-600',\n                    bgGradient: 'from-blue-950/30 to-indigo-950/30',\n                    accentColor: 'blue',\n                    stats: getToolStats('blog'),\n                    features: [\n                        'SEO Optimization',\n                        'Research Integration',\n                        'Long-form Content',\n                        'Professional Structure'\n                    ],\n                    href: '/blog-generator'\n                },\n                {\n                    id: 'youtube-script',\n                    title: 'YouTube Scripts',\n                    subtitle: 'Video Content',\n                    description: 'Create compelling YouTube video scripts with hooks, engagement techniques, and structured content for maximum viewer retention.',\n                    icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                    color: 'from-red-500 to-orange-600',\n                    bgGradient: 'from-red-950/30 to-orange-950/30',\n                    accentColor: 'red',\n                    stats: getToolStats('youtube_script'),\n                    features: [\n                        'Hook Generation',\n                        'Retention Optimization',\n                        'CTA Integration',\n                        'Script Structure'\n                    ],\n                    href: '/youtube-script'\n                },\n                {\n                    id: 'video-alchemy',\n                    title: 'Video Alchemy',\n                    subtitle: 'Coming Soon',\n                    description: 'Transform your ideas into stunning video content with AI-powered video generation and editing capabilities.',\n                    icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                    color: 'from-purple-500 to-violet-600',\n                    bgGradient: 'from-purple-950/30 to-violet-950/30',\n                    accentColor: 'purple',\n                    stats: getToolStats('video_alchemy'),\n                    features: [\n                        'AI Video Generation',\n                        'Auto Editing',\n                        'Style Transfer',\n                        'Quick Export'\n                    ],\n                    href: '#',\n                    comingSoon: true\n                },\n                {\n                    id: 'invincible-writer',\n                    title: 'Invincible Writer',\n                    subtitle: 'Multi-Agent AI',\n                    description: 'Professional content creation with 5 specialized AI agents working together: Research, SEO, Architecture, Creation, and Quality.',\n                    icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n                    color: 'from-blue-500 to-cyan-600',\n                    bgGradient: 'from-blue-950/30 to-cyan-950/30',\n                    accentColor: 'blue',\n                    stats: getToolStats('invincible_writer'),\n                    features: [\n                        '5-Agent System',\n                        'SEO Optimized',\n                        'Real-time Progress',\n                        'Cost Effective'\n                    ],\n                    href: '/content-writer'\n                },\n                {\n                    id: 'megatron',\n                    title: 'Megatron',\n                    subtitle: 'Ultimate AI',\n                    description: 'The most powerful AI assistant for complex tasks, research, analysis, and creative projects with unlimited potential.',\n                    icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                    color: 'from-gray-600 to-slate-700',\n                    bgGradient: 'from-gray-950/30 to-slate-950/30',\n                    accentColor: 'gray',\n                    stats: getToolStats('megatron'),\n                    features: [\n                        'Advanced Reasoning',\n                        'Multi-task Handling',\n                        'Research Capabilities',\n                        'Creative Solutions'\n                    ],\n                    href: '#',\n                    comingSoon: true\n                }\n            ]\n    }[\"DashboardPage.useMemo[tools]\"], [\n        getToolStats\n    ]);\n    // Redirect to login if not authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            if (status === 'unauthenticated') {\n                router.push('/login');\n            }\n        }\n    }[\"DashboardPage.useEffect\"], [\n        status,\n        router\n    ]);\n    // Fetch user profile data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            if (session === null || session === void 0 ? void 0 : session.user) {\n                fetchUserProfile();\n                fetchUserStats();\n            }\n        }\n    }[\"DashboardPage.useEffect\"], [\n        session\n    ]);\n    const fetchUserProfile = async ()=>{\n        try {\n            const response = await fetch('/api/user/profile');\n            if (response.ok) {\n                const data = await response.json();\n                setUserProfile(data);\n            }\n        } catch (error) {\n            console.error('Error fetching user profile:', error);\n        } finally{\n            setIsLoadingProfile(false);\n        }\n    };\n    const fetchUserStats = async ()=>{\n        try {\n            const response = await fetch('/api/stats');\n            if (response.ok) {\n                const data = await response.json();\n                setUserStats(data.stats);\n            }\n        } catch (error) {\n            console.error('Error fetching user stats:', error);\n        } finally{\n            setIsLoadingStats(false);\n        }\n    };\n    // Generate user initials for avatar\n    const getUserInitials = ()=>{\n        if ((userProfile === null || userProfile === void 0 ? void 0 : userProfile.firstName) && (userProfile === null || userProfile === void 0 ? void 0 : userProfile.lastName)) {\n            return \"\".concat(userProfile.firstName[0]).concat(userProfile.lastName[0]);\n        } else if (userProfile === null || userProfile === void 0 ? void 0 : userProfile.name) {\n            const names = userProfile.name.split(' ');\n            return names.length > 1 ? \"\".concat(names[0][0]).concat(names[names.length - 1][0]) : names[0][0];\n        } else if (userProfile === null || userProfile === void 0 ? void 0 : userProfile.email) {\n            return userProfile.email[0].toUpperCase();\n        }\n        return 'U';\n    };\n    // Get display name\n    const getDisplayName = ()=>{\n        if ((userProfile === null || userProfile === void 0 ? void 0 : userProfile.firstName) && (userProfile === null || userProfile === void 0 ? void 0 : userProfile.lastName)) {\n            return \"\".concat(userProfile.firstName, \" \").concat(userProfile.lastName);\n        } else if (userProfile === null || userProfile === void 0 ? void 0 : userProfile.name) {\n            return userProfile.name;\n        } else if (userProfile === null || userProfile === void 0 ? void 0 : userProfile.email) {\n            return userProfile.email.split('@')[0];\n        }\n        return 'User';\n    };\n    // Loading state\n    if (status === 'loading' || isLoadingProfile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin w-12 h-12 border-2 border-violet-400 border-t-transparent rounded-full mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 608,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"Loading your dashboard...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 609,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 607,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n            lineNumber: 606,\n            columnNumber: 7\n        }, this);\n    }\n    // Don't render if not authenticated\n    if (status === 'unauthenticated') {\n        return null;\n    }\n    const activeTool = tools.find((t)=>t.id === selectedTool) || tools[0];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-black\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-violet-950/10 via-black to-indigo-950/10\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 626,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        transition: {\n                            duration: 0.5,\n                            ease: [\n                                0.4,\n                                0,\n                                0.2,\n                                1\n                            ]\n                        },\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"absolute inset-0 bg-gradient-to-br opacity-20\", activeTool.bgGradient)\n                    }, activeTool.id, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 629,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-1/4 left-1/4 w-[500px] h-[500px] bg-violet-700/10 rounded-full blur-[100px] animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 641,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-1/4 right-1/4 w-[600px] h-[600px] bg-indigo-700/10 rounded-full blur-[120px] animate-pulse\",\n                        style: {\n                            animationDelay: '1s'\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 642,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 625,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 flex min-h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EnhancedSidebar, {\n                        isOpen: sidebarOpen,\n                        onClose: ()=>setSidebarOpen(false),\n                        tools: tools,\n                        selectedTool: selectedTool,\n                        setSelectedTool: setSelectedTool,\n                        hoveredTool: hoveredTool,\n                        setHoveredTool: setHoveredTool\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 648,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                        onClick: toggleSidebar,\n                        animate: {\n                            left: sidebarOpen ? 308 : 20\n                        },\n                        transition: {\n                            type: \"spring\",\n                            stiffness: 300,\n                            damping: 30\n                        },\n                        whileHover: {\n                            scale: 1.02\n                        },\n                        whileTap: {\n                            scale: 0.98\n                        },\n                        className: \"fixed top-6 z-50 p-3 bg-black/80 backdrop-blur-sm border border-white/20 rounded-xl hover:bg-black/90 transition-colors duration-200\",\n                        style: {\n                            willChange: 'transform'\n                        },\n                        children: sidebarOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                            className: \"w-5 h-5 text-white\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                            lineNumber: 674,\n                            columnNumber: 26\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            className: \"w-5 h-5 text-white\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                            lineNumber: 674,\n                            columnNumber: 75\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 659,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.main, {\n                        animate: {\n                            marginLeft: sidebarOpen ? 320 : 0\n                        },\n                        transition: {\n                            type: \"tween\",\n                            duration: 0.3,\n                            ease: [\n                                0.4,\n                                0,\n                                0.2,\n                                1\n                            ]\n                        },\n                        className: \"flex-1 min-h-screen\",\n                        style: {\n                            willChange: 'margin-left'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                                className: \"sticky top-0 z-40 backdrop-blur-xl bg-black/60 border-b border-white/10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                    animate: {\n                                        paddingLeft: sidebarOpen ? 32 : 80\n                                    },\n                                    transition: {\n                                        type: \"tween\",\n                                        duration: 0.3,\n                                        ease: [\n                                            0.4,\n                                            0,\n                                            0.2,\n                                            1\n                                        ]\n                                    },\n                                    className: \"px-8 py-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-white\",\n                                                        children: selectedTool ? activeTool.title : 'Dashboard Overview'\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 703,\n                                                        columnNumber: 39\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 709,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                placeholder: \"Search tools, features...\",\n                                                                value: searchQuery,\n                                                                onChange: handleSearchChange,\n                                                                className: \"w-96 pl-12 pr-4 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/15 focus:border-violet-500/50 focus:outline-none transition-colors duration-200\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 710,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 708,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 702,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"relative p-3 text-gray-400 hover:text-white transition-colors duration-200 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl hover:bg-white/15\",\n                                                        onClick: toggleNotifications,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"w-5 h-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 726,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"absolute top-2 right-2 w-2 h-2 bg-violet-500 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 727,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 722,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-8 w-px bg-white/20\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 730,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProfileButton__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        userProfile: userProfile,\n                                                        className: \"pl-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 732,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 721,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 701,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 692,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 691,\n                                columnNumber: 23\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                animate: {\n                                    paddingLeft: sidebarOpen ? 32 : 80\n                                },\n                                transition: {\n                                    type: \"tween\",\n                                    duration: 0.3,\n                                    ease: [\n                                        0.4,\n                                        0,\n                                        0.2,\n                                        1\n                                    ]\n                                },\n                                className: \"p-8 pb-16 pr-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                                    mode: \"wait\",\n                                    children: selectedTool ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToolDetails, {\n                                        tool: activeTool\n                                    }, selectedTool, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 755,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DashboardOverview, {\n                                        tools: tools,\n                                        userProfile: userProfile,\n                                        getDisplayName: getDisplayName,\n                                        userStats: userStats,\n                                        isLoadingStats: isLoadingStats\n                                    }, \"overview\", false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 757,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 753,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 742,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 678,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 646,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n        lineNumber: 623,\n        columnNumber: 5\n    }, this);\n}\n_s1(DashboardPage, \"Z4M791NIuitpzA6PhOvnrCXO6M0=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_4__.useSession,\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter\n    ];\n});\n_c14 = DashboardPage;\n// Optimized Tool Details Component with Memoization\nconst ToolDetails = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo((param)=>{\n    let { tool } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n        initial: {\n            opacity: 0,\n            scale: 0.95\n        },\n        animate: {\n            opacity: 1,\n            scale: 1\n        },\n        exit: {\n            opacity: 0,\n            scale: 0.95\n        },\n        transition: {\n            duration: 0.2,\n            ease: \"easeOut\"\n        },\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.3,\n                    delay: 0.1\n                },\n                className: \"relative overflow-hidden rounded-3xl backdrop-blur-xl border p-8 bg-gradient-to-br from-white/5 to-white/0 border-white/10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"absolute inset-0 bg-gradient-to-br opacity-20\", tool.color)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 786,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"p-4 rounded-2xl text-white shadow-2xl bg-gradient-to-br\", tool.color),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tool.icon, {\n                                                    className: \"w-8 h-8\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 798,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 794,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-3xl font-bold text-white\",\n                                                        children: tool.title\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 801,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg text-gray-300\",\n                                                        children: tool.subtitle\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 802,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 800,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 793,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300 leading-relaxed\",\n                                        children: tool.description\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 806,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-3\",\n                                        children: tool.features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-4 py-2 rounded-full text-sm text-white border bg-white/10 backdrop-blur-sm border-white/20\",\n                                                children: feature\n                                            }, index, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 812,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 810,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: tool.href,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                                whileHover: {\n                                                    scale: 1.05\n                                                },\n                                                whileTap: {\n                                                    scale: 0.95\n                                                },\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"px-12 py-6 rounded-2xl font-bold text-lg text-white shadow-2xl transition-all flex items-center space-x-3 bg-gradient-to-r\", tool.color),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                        className: \"w-6 h-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 831,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Launch \",\n                                                            tool.title\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 832,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 823,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 822,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 821,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 792,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative h-[400px] rounded-2xl overflow-hidden border bg-black/40 border-white/10\",\n                                children: tool.preview || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tool.icon, {\n                                                className: \"w-24 h-24 text-white/20 mx-auto\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 843,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400\",\n                                                children: \"Interactive preview coming soon\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 844,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 842,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 841,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 839,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 791,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 780,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.3,\n                    delay: 0.2\n                },\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/content?type=\".concat(tool.id === 'blog-generator' ? 'blog' : tool.id === 'email-generator' ? 'email' : tool.id === 'youtube-script' ? 'youtube_script' : tool.id === 'invincible-writer' ? 'invincible_writer' : tool.id.replace('-', '_')),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                delay: 0.25\n                            },\n                            className: \"backdrop-blur-xl border rounded-2xl p-6 cursor-pointer hover:bg-white/10 transition-all duration-200 group bg-white/5 border-white/10 hover:border-white/20\",\n                            title: \"View all \".concat(tool.title, \" content in your library\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"w-8 h-8 group-hover:scale-110 transition-transform duration-200\", \"text-\".concat(tool.accentColor, \"-400\"))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 868,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    className: \"w-5 h-5 text-emerald-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 870,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    className: \"w-4 h-4 text-gray-400 group-hover:text-white transition-colors\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 871,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 869,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 867,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-3xl font-bold text-white mb-1 group-hover:text-violet-200 transition-colors\",\n                                    children: tool.stats.generated\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 874,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-400 group-hover:text-gray-300 transition-colors\",\n                                    children: \"Content Generated - Click to view\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 875,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center text-xs text-violet-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                className: \"w-3 h-3 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 880,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"View in Content Library\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 881,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 879,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 878,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                            lineNumber: 860,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 859,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 0.35\n                        },\n                        className: \"backdrop-blur-xl border rounded-2xl p-6 bg-white/5 border-white/10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"w-8 h-8\", \"text-\".concat(tool.accentColor, \"-400\"))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 894,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            1,\n                                            2,\n                                            3,\n                                            4,\n                                            5\n                                        ].map((star)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"w-3 h-3 rounded-full\", star <= Math.round(tool.stats.quality / 2) ? \"bg-yellow-400\" : \"bg-gray-600\")\n                                            }, star, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 897,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 895,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 893,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-3xl font-bold text-white mb-1\",\n                                children: [\n                                    tool.stats.quality,\n                                    \"/10\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 909,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-400\",\n                                children: \"Quality Score\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 910,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 887,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 0.45\n                        },\n                        className: \"backdrop-blur-xl border rounded-2xl p-6 bg-white/5 border-white/10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"w-8 h-8\", \"text-\".concat(tool.accentColor, \"-400\"))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 920,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                        className: \"w-5 h-5 text-blue-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 921,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 919,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-3xl font-bold text-white mb-1\",\n                                children: tool.stats.avgTime\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 923,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-400\",\n                                children: \"Average Time\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 924,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 913,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 853,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n        lineNumber: 772,\n        columnNumber: 5\n    }, undefined);\n});\n_c15 = ToolDetails;\n// Optimized Dashboard Overview Component with Memoization\nconst DashboardOverview = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo((param)=>{\n    let { tools, userProfile, getDisplayName, userStats, isLoadingStats } = param;\n    var _userStats_totalContent, _userStats_trends, _userStats_trends1, _userStats_trends2, _userStats_trends_toolsActive, _userStats_trends3, _userStats_trends4;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n        initial: {\n            opacity: 0,\n            scale: 0.95\n        },\n        animate: {\n            opacity: 1,\n            scale: 1\n        },\n        exit: {\n            opacity: 0,\n            scale: 0.95\n        },\n        transition: {\n            duration: 0.2,\n            ease: \"easeOut\"\n        },\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-violet-800/20 to-indigo-800/20 backdrop-blur-xl border border-white/10 rounded-3xl p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl font-bold text-white mb-4\",\n                        children: [\n                            \"Welcome back, \",\n                            getDisplayName(),\n                            \"! ✨\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 949,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-300 mb-6\",\n                        children: \"Your creative AI toolkit is ready. What will you create today?\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 950,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                        children: isLoadingStats ? // Loading skeleton\n                        Array.from({\n                            length: 4\n                        }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: index * 0.1\n                                },\n                                className: \"bg-black/40 backdrop-blur-sm border border-white/10 rounded-xl p-4 animate-pulse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-5 bg-white/10 rounded mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 963,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-8 bg-white/10 rounded mb-1\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 964,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-white/10 rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 965,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, index, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 956,\n                                columnNumber: 15\n                            }, undefined)) : [\n                            {\n                                label: 'Total Created',\n                                value: (userStats === null || userStats === void 0 ? void 0 : (_userStats_totalContent = userStats.totalContent) === null || _userStats_totalContent === void 0 ? void 0 : _userStats_totalContent.toString()) || '0',\n                                icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                                change: (userStats === null || userStats === void 0 ? void 0 : (_userStats_trends = userStats.trends) === null || _userStats_trends === void 0 ? void 0 : _userStats_trends.contentGrowth) || '+0%'\n                            },\n                            {\n                                label: 'Time Saved',\n                                value: \"\".concat((userStats === null || userStats === void 0 ? void 0 : userStats.timeSavedHours) || 0, \" hrs\"),\n                                icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"],\n                                change: (userStats === null || userStats === void 0 ? void 0 : (_userStats_trends1 = userStats.trends) === null || _userStats_trends1 === void 0 ? void 0 : _userStats_trends1.timeEfficiency) || '+0%'\n                            },\n                            {\n                                label: 'Quality Score',\n                                value: \"\".concat((userStats === null || userStats === void 0 ? void 0 : userStats.qualityScore) || 9.0, \"/10\"),\n                                icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"],\n                                change: (userStats === null || userStats === void 0 ? void 0 : (_userStats_trends2 = userStats.trends) === null || _userStats_trends2 === void 0 ? void 0 : _userStats_trends2.qualityImprovement) || '+0.0'\n                            },\n                            {\n                                label: 'Active Tools',\n                                value: (userStats === null || userStats === void 0 ? void 0 : (_userStats_trends3 = userStats.trends) === null || _userStats_trends3 === void 0 ? void 0 : (_userStats_trends_toolsActive = _userStats_trends3.toolsActive) === null || _userStats_trends_toolsActive === void 0 ? void 0 : _userStats_trends_toolsActive.toString()) || '0',\n                                icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                                change: '+' + ((userStats === null || userStats === void 0 ? void 0 : (_userStats_trends4 = userStats.trends) === null || _userStats_trends4 === void 0 ? void 0 : _userStats_trends4.toolsActive) || 0)\n                            }\n                        ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: index * 0.1\n                                },\n                                className: \"bg-black/40 backdrop-blur-sm border border-white/10 rounded-xl p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                                className: \"w-5 h-5 text-violet-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 1003,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-emerald-400\",\n                                                children: stat.change\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 1004,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 1002,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-white\",\n                                        children: stat.value\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 1006,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: stat.label\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 1007,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, stat.label, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 995,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 952,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 948,\n                columnNumber: 15\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white mb-6\",\n                        children: \"Your AI Tools\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 1016,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                        children: tools.map((tool, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: tool.comingSoon ? '#' : tool.href,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        scale: 0.9\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        scale: 1\n                                    },\n                                    transition: {\n                                        delay: index * 0.1\n                                    },\n                                    whileHover: {\n                                        y: -8\n                                    },\n                                    className: \"group relative cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl blur-xl\", tool.color)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 1027,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative backdrop-blur-xl border transition-all bg-black/60 border-white/10 hover:border-white/20 rounded-2xl\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start justify-between mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"p-3 rounded-xl text-white bg-gradient-to-br\", tool.color),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tool.icon, {\n                                                                    className: \"w-6 h-6\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 1040,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 1036,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                className: \"w-5 h-5 text-gray-400 group-hover:text-white transition-colors\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 1042,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 1035,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-white mb-1\",\n                                                        children: tool.title\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 1045,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-400 mb-4\",\n                                                        children: tool.subtitle\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 1046,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between text-xs\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: \"/content?type=\".concat(tool.id === 'blog-generator' ? 'blog' : tool.id === 'email-generator' ? 'email' : tool.id === 'youtube-script' ? 'youtube_script' : tool.id === 'invincible-writer' ? 'invincible_writer' : tool.id.replace('-', '_')),\n                                                                onClick: (e)=>e.stopPropagation(),\n                                                                className: \"hover:bg-white/10 rounded px-2 py-1 transition-colors group/stat\",\n                                                                title: \"View all \".concat(tool.title, \" content\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-500 group-hover/stat:text-gray-400\",\n                                                                        children: \"Generated\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 1055,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-white font-medium group-hover/stat:text-violet-200\",\n                                                                                children: tool.stats.generated\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                                lineNumber: 1057,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                className: \"w-3 h-3 text-gray-400 group-hover/stat:text-white opacity-0 group-hover/stat:opacity-100 transition-all\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                                lineNumber: 1058,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 1056,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black/90 text-white text-xs rounded opacity-0 group-hover/stat:opacity-100 transition-opacity pointer-events-none whitespace-nowrap\",\n                                                                        children: \"Click to view content\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 1061,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 1049,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-right\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-500\",\n                                                                        children: \"Quality\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 1066,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-white font-medium\",\n                                                                        children: [\n                                                                            tool.stats.quality,\n                                                                            \"/10\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 1067,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 1065,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 1048,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 1034,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 1032,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 1020,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, tool.id, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 1019,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 1017,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 1015,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(OptimizedRecentContent, {\n                    limit: 5,\n                    showFilters: true\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                    lineNumber: 1080,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 1079,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n        lineNumber: 940,\n        columnNumber: 5\n    }, undefined);\n});\n_c16 = DashboardOverview;\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15, _c16;\n$RefreshReg$(_c, \"OptimizedRecentContent\");\n$RefreshReg$(_c1, \"OptimizedBlogPreview$dynamic\");\n$RefreshReg$(_c2, \"OptimizedBlogPreview\");\n$RefreshReg$(_c3, \"OptimizedEmailPreview$dynamic\");\n$RefreshReg$(_c4, \"OptimizedEmailPreview\");\n$RefreshReg$(_c5, \"OptimizedSocialMediaPreview$dynamic\");\n$RefreshReg$(_c6, \"OptimizedSocialMediaPreview\");\n$RefreshReg$(_c7, \"OptimizedVideoScriptPreview$dynamic\");\n$RefreshReg$(_c8, \"OptimizedVideoScriptPreview\");\n$RefreshReg$(_c9, \"OptimizedVideoAlchemyPreview$dynamic\");\n$RefreshReg$(_c10, \"OptimizedVideoAlchemyPreview\");\n$RefreshReg$(_c11, \"OptimizedMegatronPreview$dynamic\");\n$RefreshReg$(_c12, \"OptimizedMegatronPreview\");\n$RefreshReg$(_c13, \"EnhancedSidebar\");\n$RefreshReg$(_c14, \"DashboardPage\");\n$RefreshReg$(_c15, \"ToolDetails\");\n$RefreshReg$(_c16, \"DashboardOverview\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});