"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layers.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-help.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/video.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Bell,Bot,ChevronLeft,Clock,Eye,FileText,HelpCircle,Home,Layers,Mail,Menu,Rocket,Search,Settings,Shield,Sparkles,TrendingUp,Video,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ProfileButton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ProfileButton */ \"(app-pages-browser)/./src/components/ProfileButton.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Lazy load RecentContent to reduce initial bundle size\nconst OptimizedRecentContent = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_dashboard_RecentContent_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/dashboard/RecentContent */ \"(app-pages-browser)/./src/components/dashboard/RecentContent.tsx\")).then((mod)=>({\n            default: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(mod.default)\n        })), {\n    loadableGenerated: {\n        modules: [\n            \"app/dashboard/page.tsx -> \" + \"@/components/dashboard/RecentContent\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: Array.from({\n                length: 3\n            }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white/10 rounded-lg p-4 animate-pulse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 bg-white/10 rounded mb-2 w-3/4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-3 bg-white/10 rounded w-1/2\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, i, true, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n            lineNumber: 63,\n            columnNumber: 5\n        }, undefined)\n});\n_c = OptimizedRecentContent;\nconst OptimizedBlogPreview = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_c1 = ()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_BlogPreview_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/BlogPreview */ \"(app-pages-browser)/./src/components/BlogPreview.tsx\")).then((mod)=>({\n            default: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(mod.default)\n        })), {\n    loadableGenerated: {\n        modules: [\n            \"app/dashboard/page.tsx -> \" + \"@/components/BlogPreview\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-16 h-16 border-2 border-pink-500/30 border-t-pink-500 rounded-full animate-spin mx-auto\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n            lineNumber: 77,\n            columnNumber: 18\n        }, undefined)\n});\n_c2 = OptimizedBlogPreview;\nconst OptimizedEmailPreview = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_c3 = ()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_EmailPreview_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/EmailPreview */ \"(app-pages-browser)/./src/components/EmailPreview.tsx\")).then((mod)=>({\n            default: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(mod.default)\n        })), {\n    loadableGenerated: {\n        modules: [\n            \"app/dashboard/page.tsx -> \" + \"@/components/EmailPreview\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-16 h-16 border-2 border-emerald-500/30 border-t-emerald-500 rounded-full animate-spin mx-auto\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n            lineNumber: 82,\n            columnNumber: 18\n        }, undefined)\n});\n_c4 = OptimizedEmailPreview;\nconst OptimizedSocialMediaPreview = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_c5 = ()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_SocialMediaPreview_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/SocialMediaPreview */ \"(app-pages-browser)/./src/components/SocialMediaPreview.tsx\")).then((mod)=>({\n            default: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(mod.default)\n        })), {\n    loadableGenerated: {\n        modules: [\n            \"app/dashboard/page.tsx -> \" + \"@/components/SocialMediaPreview\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-16 h-16 border-2 border-blue-500/30 border-t-blue-500 rounded-full animate-spin mx-auto\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n            lineNumber: 87,\n            columnNumber: 18\n        }, undefined)\n});\n_c6 = OptimizedSocialMediaPreview;\nconst OptimizedVideoScriptPreview = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_c7 = ()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_VideoScriptPreview_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/VideoScriptPreview */ \"(app-pages-browser)/./src/components/VideoScriptPreview.tsx\")).then((mod)=>({\n            default: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(mod.default)\n        })), {\n    loadableGenerated: {\n        modules: [\n            \"app/dashboard/page.tsx -> \" + \"@/components/VideoScriptPreview\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-16 h-16 border-2 border-red-500/30 border-t-red-500 rounded-full animate-spin mx-auto\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n            lineNumber: 92,\n            columnNumber: 18\n        }, undefined)\n});\n_c8 = OptimizedVideoScriptPreview;\nconst OptimizedVideoAlchemyPreview = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_c9 = ()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_VideoAlchemyPreview_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/VideoAlchemyPreview */ \"(app-pages-browser)/./src/components/VideoAlchemyPreview.tsx\")).then((mod)=>({\n            default: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(mod.default)\n        })), {\n    loadableGenerated: {\n        modules: [\n            \"app/dashboard/page.tsx -> \" + \"@/components/VideoAlchemyPreview\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-16 h-16 border-2 border-purple-500/30 border-t-purple-500 rounded-full animate-spin mx-auto\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n            lineNumber: 97,\n            columnNumber: 18\n        }, undefined)\n});\n_c10 = OptimizedVideoAlchemyPreview;\nconst OptimizedMegatronPreview = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_c11 = ()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_MegatronPreview_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/MegatronPreview */ \"(app-pages-browser)/./src/components/MegatronPreview.tsx\")).then((mod)=>({\n            default: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(mod.default)\n        })), {\n    loadableGenerated: {\n        modules: [\n            \"app/dashboard/page.tsx -> \" + \"@/components/MegatronPreview\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-16 h-16 border-2 border-red-500/30 border-t-red-500 rounded-full animate-spin mx-auto\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n            lineNumber: 102,\n            columnNumber: 18\n        }, undefined)\n});\n_c12 = OptimizedMegatronPreview;\n// Optimized Sidebar Component with Memoization\nconst EnhancedSidebar = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(_s((param)=>{\n    let { isOpen, onClose, tools, selectedTool, setSelectedTool, hoveredTool, setHoveredTool } = param;\n    _s();\n    // Memoized animation variants for better performance\n    const sidebarVariants = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"EnhancedSidebar.useMemo[sidebarVariants]\": ()=>({\n                open: {\n                    x: 0,\n                    transition: {\n                        type: \"spring\",\n                        stiffness: 300,\n                        damping: 30\n                    }\n                },\n                closed: {\n                    x: -320,\n                    transition: {\n                        type: \"spring\",\n                        stiffness: 300,\n                        damping: 30\n                    }\n                }\n            })\n    }[\"EnhancedSidebar.useMemo[sidebarVariants]\"], []);\n    // Memoized handlers to prevent re-renders\n    const handleToolSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedSidebar.useCallback[handleToolSelect]\": (toolId)=>{\n            setSelectedTool(toolId);\n        }\n    }[\"EnhancedSidebar.useCallback[handleToolSelect]\"], [\n        setSelectedTool\n    ]);\n    const handleToolHover = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedSidebar.useCallback[handleToolHover]\": (toolId)=>{\n            setHoveredTool(toolId);\n        }\n    }[\"EnhancedSidebar.useCallback[handleToolHover]\"], [\n        setHoveredTool\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    transition: {\n                        duration: 0.2\n                    },\n                    onClick: onClose,\n                    className: \"fixed inset-0 bg-black/60 backdrop-blur-sm z-40 lg:hidden\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 149,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.aside, {\n                initial: \"closed\",\n                animate: isOpen ? \"open\" : \"closed\",\n                variants: sidebarVariants,\n                className: \"fixed left-0 top-0 h-screen w-[320px] z-50 lg:z-10\",\n                style: {\n                    willChange: 'transform'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black/85 backdrop-blur-xl border-r border-white/10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-br from-violet-950/15 via-black/60 to-indigo-950/15\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 h-full flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 border-b border-white/10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"flex items-center space-x-4 group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-r from-violet-500 to-indigo-500 rounded-xl blur-lg opacity-50\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                                    whileHover: {\n                                                        scale: 1.05\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.95\n                                                    },\n                                                    transition: {\n                                                        duration: 0.2\n                                                    },\n                                                    className: \"relative bg-gradient-to-r from-violet-800 to-indigo-800 rounded-xl p-3 border border-white/20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-7 h-7 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-2xl font-bold text-white\",\n                                                    children: \"Content Creator\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"Creative AI Suite\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"flex-1 p-4 space-y-2 overflow-y-auto custom-scrollbar\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                        onClick: ()=>handleToolSelect(''),\n                                        whileHover: {\n                                            x: 2\n                                        },\n                                        whileTap: {\n                                            scale: 0.98\n                                        },\n                                        transition: {\n                                            duration: 0.1\n                                        },\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"w-full flex items-center space-x-4 px-4 py-4 rounded-xl transition-all duration-200 relative\", !selectedTool ? \"bg-white/15 text-white shadow-lg border border-white/20\" : \"text-gray-400 hover:text-white hover:bg-white/8\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-2 rounded-lg bg-white/10\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 216,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-left\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-semibold\",\n                                                                children: \"Dashboard\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 219,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs opacity-70\",\n                                                                children: \"Overview & Stats\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 220,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            !selectedTool && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute right-3 w-1 h-8 bg-white rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"pt-6 pb-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 px-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-px flex-1 bg-white/20\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs font-semibold text-gray-400 uppercase tracking-wider\",\n                                                    children: \"AI Tools\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-px flex-1 bg-white/20\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/content\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                            whileHover: {\n                                                x: 2\n                                            },\n                                            whileTap: {\n                                                scale: 0.98\n                                            },\n                                            transition: {\n                                                duration: 0.2\n                                            },\n                                            className: \"w-full flex items-center space-x-4 px-4 py-4 text-gray-400 hover:text-white hover:bg-white/8 rounded-xl transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 rounded-lg bg-white/5\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"Content Library\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 252,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs opacity-70\",\n                                                            children: \"View Past Content\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 253,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    tools.map((tool)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                            onClick: ()=>handleToolSelect(tool.id),\n                                            onMouseEnter: ()=>handleToolHover(tool.id),\n                                            onMouseLeave: ()=>handleToolHover(null),\n                                            whileHover: {\n                                                x: 2\n                                            },\n                                            whileTap: {\n                                                scale: 0.98\n                                            },\n                                            transition: {\n                                                duration: 0.1\n                                            },\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"w-full flex items-center space-x-4 px-4 py-4 rounded-xl transition-all duration-200 relative\", selectedTool === tool.id ? \"bg-gradient-to-r text-white shadow-lg border border-white/20\" : \"text-gray-400 hover:text-white hover:bg-white/8\", selectedTool === tool.id && tool.color),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"p-2 rounded-lg transition-colors duration-200\", selectedTool === tool.id ? \"bg-white/20\" : \"bg-white/10\"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tool.icon, {\n                                                                className: \"w-5 h-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 283,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-left\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-semibold\",\n                                                                            children: tool.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                            lineNumber: 287,\n                                                                            columnNumber: 23\n                                                                        }, undefined),\n                                                                        tool.beta && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-1.5 py-0.5 text-xs bg-blue-500/20 text-blue-400 rounded border border-blue-500/30\",\n                                                                            children: \"BETA\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                            lineNumber: 289,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        tool.comingSoon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-1.5 py-0.5 text-xs bg-gray-500/20 text-gray-400 rounded border border-gray-500/30\",\n                                                                            children: \"SOON\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                            lineNumber: 294,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 286,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs opacity-70\",\n                                                                    children: tool.subtitle\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 299,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 285,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                selectedTool === tool.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute right-3 w-1 h-8 bg-white rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                hoveredTool === tool.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute right-3 top-3 bg-black/60 rounded-full px-2 py-1 text-xs text-white\",\n                                                    children: tool.stats.generated\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, tool.id, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 15\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border-t border-white/10 space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/settings\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                            whileHover: {\n                                                x: 2\n                                            },\n                                            whileTap: {\n                                                scale: 0.98\n                                            },\n                                            transition: {\n                                                duration: 0.2\n                                            },\n                                            className: \"w-full flex items-center space-x-4 px-4 py-3 text-gray-400 hover:text-white hover:bg-white/8 rounded-xl transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 rounded-lg bg-white/5\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: \"Settings\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                        whileHover: {\n                                            x: 2\n                                        },\n                                        whileTap: {\n                                            scale: 0.98\n                                        },\n                                        transition: {\n                                            duration: 0.2\n                                        },\n                                        className: \"w-full flex items-center space-x-4 px-4 py-3 text-gray-400 hover:text-white hover:bg-white/8 rounded-xl transition-all duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 rounded-lg bg-white/5\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"Help & Support\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n}, \"rL9lWfnAkLdZcznve+ofH+FlFk4=\"));\n_c13 = EnhancedSidebar;\n// Optimized Dashboard with Performance Enhancements\nfunction DashboardPage() {\n    _s1();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    const [selectedTool, setSelectedTool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('email-generator');\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showNotifications, setShowNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hoveredTool, setHoveredTool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userProfile, setUserProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoadingProfile, setIsLoadingProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [userStats, setUserStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoadingStats, setIsLoadingStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // ALL useCallback hooks must be at the top level and in consistent order\n    const toggleSidebar = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DashboardPage.useCallback[toggleSidebar]\": ()=>setSidebarOpen({\n                \"DashboardPage.useCallback[toggleSidebar]\": (prev)=>!prev\n            }[\"DashboardPage.useCallback[toggleSidebar]\"])\n    }[\"DashboardPage.useCallback[toggleSidebar]\"], []);\n    const toggleNotifications = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DashboardPage.useCallback[toggleNotifications]\": ()=>setShowNotifications({\n                \"DashboardPage.useCallback[toggleNotifications]\": (prev)=>!prev\n            }[\"DashboardPage.useCallback[toggleNotifications]\"])\n    }[\"DashboardPage.useCallback[toggleNotifications]\"], []);\n    const handleSearchChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DashboardPage.useCallback[handleSearchChange]\": (e)=>{\n            setSearchQuery(e.target.value);\n        }\n    }[\"DashboardPage.useCallback[handleSearchChange]\"], []);\n    // Memoized stats calculation for tools to prevent recalculation on every render\n    const getToolStats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DashboardPage.useCallback[getToolStats]\": (toolType)=>{\n            if (!(userStats === null || userStats === void 0 ? void 0 : userStats.contentBreakdown)) {\n                return {\n                    generated: 0,\n                    quality: 9.0,\n                    avgTime: '0 min'\n                };\n            }\n            const generated = userStats.contentBreakdown[toolType] || 0;\n            const quality = Math.min(9.8, 8.5 + generated * 0.05);\n            const avgTimes = {\n                'blog': '3 min',\n                'email': '1 min',\n                'social_media': '30 sec',\n                'youtube_script': '4 min',\n                'video_alchemy': '3 min'\n            };\n            return {\n                generated,\n                quality: Math.round(quality * 10) / 10,\n                avgTime: avgTimes[toolType] || '2 min'\n            };\n        }\n    }[\"DashboardPage.useCallback[getToolStats]\"], [\n        userStats === null || userStats === void 0 ? void 0 : userStats.contentBreakdown\n    ]);\n    // Memoized tools configuration to prevent recreation - MUST be before any conditional returns\n    const tools = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"DashboardPage.useMemo[tools]\": ()=>[\n                {\n                    id: 'email-generator',\n                    title: 'Email Generator',\n                    subtitle: 'Professional Emails',\n                    description: 'Generate compelling email campaigns, newsletters, and professional communications with AI-powered personalization.',\n                    icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                    color: 'from-emerald-500 to-teal-600',\n                    bgGradient: 'from-emerald-950/30 to-teal-950/30',\n                    accentColor: 'emerald',\n                    stats: getToolStats('email'),\n                    features: [\n                        'Personalization',\n                        'A/B Testing',\n                        'Professional Tone',\n                        'Quick Generation'\n                    ],\n                    href: '/email-generator'\n                },\n                {\n                    id: 'social-media-generator',\n                    title: 'Social Media',\n                    subtitle: 'Viral Content',\n                    description: 'Create engaging social media posts, captions, and content strategies that resonate with your audience across all platforms.',\n                    icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                    color: 'from-pink-500 to-rose-600',\n                    bgGradient: 'from-pink-950/30 to-rose-950/30',\n                    accentColor: 'pink',\n                    stats: getToolStats('social_media'),\n                    features: [\n                        'Multi-Platform',\n                        'Trending Hashtags',\n                        'Engagement Optimization',\n                        'Quick Generation'\n                    ],\n                    href: '/social-media-generator'\n                },\n                {\n                    id: 'blog-generator',\n                    title: 'Blog Generator',\n                    subtitle: 'SEO Optimized',\n                    description: 'Generate comprehensive, SEO-optimized blog posts with research, proper structure, and engaging content that ranks well.',\n                    icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                    color: 'from-blue-500 to-indigo-600',\n                    bgGradient: 'from-blue-950/30 to-indigo-950/30',\n                    accentColor: 'blue',\n                    stats: getToolStats('blog'),\n                    features: [\n                        'SEO Optimization',\n                        'Research Integration',\n                        'Long-form Content',\n                        'Professional Structure'\n                    ],\n                    href: '/blog-generator'\n                },\n                {\n                    id: 'youtube-script',\n                    title: 'YouTube Scripts',\n                    subtitle: 'Video Content',\n                    description: 'Create compelling YouTube video scripts with hooks, engagement techniques, and structured content for maximum viewer retention.',\n                    icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                    color: 'from-red-500 to-orange-600',\n                    bgGradient: 'from-red-950/30 to-orange-950/30',\n                    accentColor: 'red',\n                    stats: getToolStats('youtube_script'),\n                    features: [\n                        'Hook Generation',\n                        'Retention Optimization',\n                        'CTA Integration',\n                        'Script Structure'\n                    ],\n                    href: '/youtube-script'\n                },\n                {\n                    id: 'video-alchemy',\n                    title: 'Video Alchemy',\n                    subtitle: 'Coming Soon',\n                    description: 'Transform your ideas into stunning video content with AI-powered video generation and editing capabilities.',\n                    icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                    color: 'from-purple-500 to-violet-600',\n                    bgGradient: 'from-purple-950/30 to-violet-950/30',\n                    accentColor: 'purple',\n                    stats: getToolStats('video_alchemy'),\n                    features: [\n                        'AI Video Generation',\n                        'Auto Editing',\n                        'Style Transfer',\n                        'Quick Export'\n                    ],\n                    href: '#',\n                    comingSoon: true\n                },\n                {\n                    id: 'invincible-writer',\n                    title: 'Invincible Writer',\n                    subtitle: 'Multi-Agent AI',\n                    description: 'Professional content creation with 5 specialized AI agents working together: Research, SEO, Architecture, Creation, and Quality.',\n                    icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n                    color: 'from-blue-500 to-cyan-600',\n                    bgGradient: 'from-blue-950/30 to-cyan-950/30',\n                    accentColor: 'blue',\n                    stats: getToolStats('invincible_writer'),\n                    features: [\n                        '5-Agent System',\n                        'SEO Optimized',\n                        'Real-time Progress',\n                        'Cost Effective'\n                    ],\n                    href: '/content-writer'\n                },\n                {\n                    id: 'megatron',\n                    title: 'Megatron',\n                    subtitle: 'Ultimate AI',\n                    description: 'The most powerful AI assistant for complex tasks, research, analysis, and creative projects with unlimited potential.',\n                    icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                    color: 'from-gray-600 to-slate-700',\n                    bgGradient: 'from-gray-950/30 to-slate-950/30',\n                    accentColor: 'gray',\n                    stats: getToolStats('megatron'),\n                    features: [\n                        'Advanced Reasoning',\n                        'Multi-task Handling',\n                        'Research Capabilities',\n                        'Creative Solutions'\n                    ],\n                    href: '#',\n                    comingSoon: true\n                }\n            ]\n    }[\"DashboardPage.useMemo[tools]\"], [\n        getToolStats\n    ]);\n    // Redirect to login if not authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            if (status === 'unauthenticated') {\n                router.push('/login');\n            }\n        }\n    }[\"DashboardPage.useEffect\"], [\n        status,\n        router\n    ]);\n    // Fetch user profile data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            if (session === null || session === void 0 ? void 0 : session.user) {\n                fetchUserProfile();\n                fetchUserStats();\n            }\n        }\n    }[\"DashboardPage.useEffect\"], [\n        session\n    ]);\n    const fetchUserProfile = async ()=>{\n        try {\n            const response = await fetch('/api/user/profile');\n            if (response.ok) {\n                const data = await response.json();\n                setUserProfile(data);\n            }\n        } catch (error) {\n            console.error('Error fetching user profile:', error);\n        } finally{\n            setIsLoadingProfile(false);\n        }\n    };\n    const fetchUserStats = async ()=>{\n        try {\n            const response = await fetch('/api/stats');\n            if (response.ok) {\n                const data = await response.json();\n                setUserStats(data.stats);\n            }\n        } catch (error) {\n            console.error('Error fetching user stats:', error);\n        } finally{\n            setIsLoadingStats(false);\n        }\n    };\n    // Generate user initials for avatar\n    const getUserInitials = ()=>{\n        if ((userProfile === null || userProfile === void 0 ? void 0 : userProfile.firstName) && (userProfile === null || userProfile === void 0 ? void 0 : userProfile.lastName)) {\n            return \"\".concat(userProfile.firstName[0]).concat(userProfile.lastName[0]);\n        } else if (userProfile === null || userProfile === void 0 ? void 0 : userProfile.name) {\n            const names = userProfile.name.split(' ');\n            return names.length > 1 ? \"\".concat(names[0][0]).concat(names[names.length - 1][0]) : names[0][0];\n        } else if (userProfile === null || userProfile === void 0 ? void 0 : userProfile.email) {\n            return userProfile.email[0].toUpperCase();\n        }\n        return 'U';\n    };\n    // Get display name\n    const getDisplayName = ()=>{\n        if ((userProfile === null || userProfile === void 0 ? void 0 : userProfile.firstName) && (userProfile === null || userProfile === void 0 ? void 0 : userProfile.lastName)) {\n            return \"\".concat(userProfile.firstName, \" \").concat(userProfile.lastName);\n        } else if (userProfile === null || userProfile === void 0 ? void 0 : userProfile.name) {\n            return userProfile.name;\n        } else if (userProfile === null || userProfile === void 0 ? void 0 : userProfile.email) {\n            return userProfile.email.split('@')[0];\n        }\n        return 'User';\n    };\n    // Loading state\n    if (status === 'loading' || isLoadingProfile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin w-12 h-12 border-2 border-violet-400 border-t-transparent rounded-full mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 608,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"Loading your dashboard...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 609,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 607,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n            lineNumber: 606,\n            columnNumber: 7\n        }, this);\n    }\n    // Don't render if not authenticated\n    if (status === 'unauthenticated') {\n        return null;\n    }\n    const activeTool = tools.find((t)=>t.id === selectedTool) || tools[0];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-black\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-violet-950/10 via-black to-indigo-950/10\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 626,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        transition: {\n                            duration: 0.5,\n                            ease: [\n                                0.4,\n                                0,\n                                0.2,\n                                1\n                            ]\n                        },\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"absolute inset-0 bg-gradient-to-br opacity-20\", activeTool.bgGradient)\n                    }, activeTool.id, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 629,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-1/4 left-1/4 w-[500px] h-[500px] bg-violet-700/10 rounded-full blur-[100px] animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 641,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-1/4 right-1/4 w-[600px] h-[600px] bg-indigo-700/10 rounded-full blur-[120px] animate-pulse\",\n                        style: {\n                            animationDelay: '1s'\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 642,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 625,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 flex min-h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EnhancedSidebar, {\n                        isOpen: sidebarOpen,\n                        onClose: ()=>setSidebarOpen(false),\n                        tools: tools,\n                        selectedTool: selectedTool,\n                        setSelectedTool: setSelectedTool,\n                        hoveredTool: hoveredTool,\n                        setHoveredTool: setHoveredTool\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 648,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                        onClick: toggleSidebar,\n                        animate: {\n                            left: sidebarOpen ? 308 : 20\n                        },\n                        transition: {\n                            type: \"spring\",\n                            stiffness: 300,\n                            damping: 30\n                        },\n                        whileHover: {\n                            scale: 1.02\n                        },\n                        whileTap: {\n                            scale: 0.98\n                        },\n                        className: \"fixed top-6 z-50 p-3 bg-black/80 backdrop-blur-sm border border-white/20 rounded-xl hover:bg-black/90 transition-colors duration-200\",\n                        style: {\n                            willChange: 'transform'\n                        },\n                        children: sidebarOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                            className: \"w-5 h-5 text-white\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                            lineNumber: 674,\n                            columnNumber: 26\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            className: \"w-5 h-5 text-white\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                            lineNumber: 674,\n                            columnNumber: 75\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 659,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.main, {\n                        animate: {\n                            marginLeft: sidebarOpen ? 320 : 0\n                        },\n                        transition: {\n                            type: \"tween\",\n                            duration: 0.3,\n                            ease: [\n                                0.4,\n                                0,\n                                0.2,\n                                1\n                            ]\n                        },\n                        className: \"flex-1 min-h-screen\",\n                        style: {\n                            willChange: 'margin-left'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                                className: \"sticky top-0 z-40 backdrop-blur-xl bg-black/60 border-b border-white/10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                    animate: {\n                                        paddingLeft: sidebarOpen ? 32 : 80\n                                    },\n                                    transition: {\n                                        type: \"tween\",\n                                        duration: 0.3,\n                                        ease: [\n                                            0.4,\n                                            0,\n                                            0.2,\n                                            1\n                                        ]\n                                    },\n                                    className: \"px-8 py-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-white\",\n                                                        children: selectedTool ? activeTool.title : 'Dashboard Overview'\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 703,\n                                                        columnNumber: 39\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 709,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                placeholder: \"Search tools, features...\",\n                                                                value: searchQuery,\n                                                                onChange: handleSearchChange,\n                                                                className: \"w-96 pl-12 pr-4 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/15 focus:border-violet-500/50 focus:outline-none transition-colors duration-200\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 710,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 708,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 702,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"relative p-3 text-gray-400 hover:text-white transition-colors duration-200 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl hover:bg-white/15\",\n                                                        onClick: toggleNotifications,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"w-5 h-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 726,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"absolute top-2 right-2 w-2 h-2 bg-violet-500 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 727,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 722,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-8 w-px bg-white/20\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 730,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProfileButton__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        userProfile: userProfile,\n                                                        className: \"pl-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 732,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 721,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 701,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 692,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 691,\n                                columnNumber: 23\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                animate: {\n                                    paddingLeft: sidebarOpen ? 32 : 80\n                                },\n                                transition: {\n                                    type: \"tween\",\n                                    duration: 0.3,\n                                    ease: [\n                                        0.4,\n                                        0,\n                                        0.2,\n                                        1\n                                    ]\n                                },\n                                className: \"p-8 pb-16 pr-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                                    mode: \"wait\",\n                                    children: selectedTool ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToolDetails, {\n                                        tool: activeTool\n                                    }, selectedTool, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 755,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DashboardOverview, {\n                                        tools: tools,\n                                        userProfile: userProfile,\n                                        getDisplayName: getDisplayName,\n                                        userStats: userStats,\n                                        isLoadingStats: isLoadingStats\n                                    }, \"overview\", false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 757,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 753,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 742,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 678,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 646,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n        lineNumber: 623,\n        columnNumber: 5\n    }, this);\n}\n_s1(DashboardPage, \"Z4M791NIuitpzA6PhOvnrCXO6M0=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_4__.useSession,\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter\n    ];\n});\n_c14 = DashboardPage;\n// Optimized Tool Details Component with Memoization\nconst ToolDetails = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo((param)=>{\n    let { tool } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n        initial: {\n            opacity: 0,\n            scale: 0.95\n        },\n        animate: {\n            opacity: 1,\n            scale: 1\n        },\n        exit: {\n            opacity: 0,\n            scale: 0.95\n        },\n        transition: {\n            duration: 0.2,\n            ease: \"easeOut\"\n        },\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.3,\n                    delay: 0.1\n                },\n                className: \"relative overflow-hidden rounded-3xl backdrop-blur-xl border p-8 bg-gradient-to-br from-white/5 to-white/0 border-white/10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"absolute inset-0 bg-gradient-to-br opacity-20\", tool.color)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 786,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"p-4 rounded-2xl text-white shadow-2xl bg-gradient-to-br\", tool.color),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tool.icon, {\n                                                    className: \"w-8 h-8\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 798,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 794,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-3xl font-bold text-white\",\n                                                        children: tool.title\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 801,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg text-gray-300\",\n                                                        children: tool.subtitle\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 802,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 800,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 793,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300 leading-relaxed\",\n                                        children: tool.description\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 806,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-3\",\n                                        children: tool.features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-4 py-2 rounded-full text-sm text-white border bg-white/10 backdrop-blur-sm border-white/20\",\n                                                children: feature\n                                            }, index, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 812,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 810,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: tool.href,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                                whileHover: {\n                                                    scale: 1.05\n                                                },\n                                                whileTap: {\n                                                    scale: 0.95\n                                                },\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"px-12 py-6 rounded-2xl font-bold text-lg text-white shadow-2xl transition-all flex items-center space-x-3 bg-gradient-to-r\", tool.color),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                        className: \"w-6 h-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 831,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Launch \",\n                                                            tool.title\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 832,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 823,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 822,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 821,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 792,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative h-[400px] rounded-2xl overflow-hidden border bg-black/40 border-white/10\",\n                                children: tool.preview || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tool.icon, {\n                                                className: \"w-24 h-24 text-white/20 mx-auto\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 843,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400\",\n                                                children: \"Interactive preview coming soon\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 844,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 842,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 841,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 839,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 791,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 780,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.3,\n                    delay: 0.2\n                },\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/content?type=\".concat(tool.id === 'blog-generator' ? 'blog' : tool.id === 'email-generator' ? 'email' : tool.id === 'youtube-script' ? 'youtube_script' : tool.id === 'invincible-writer' ? 'invincible_writer' : tool.id.replace('-', '_')),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                delay: 0.25\n                            },\n                            className: \"backdrop-blur-xl border rounded-2xl p-6 cursor-pointer hover:bg-white/10 transition-all duration-200 group bg-white/5 border-white/10 hover:border-white/20\",\n                            title: \"View all \".concat(tool.title, \" content in your library\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"w-8 h-8 group-hover:scale-110 transition-transform duration-200\", \"text-\".concat(tool.accentColor, \"-400\"))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 868,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    className: \"w-5 h-5 text-emerald-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 870,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    className: \"w-4 h-4 text-gray-400 group-hover:text-white transition-colors\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 871,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 869,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 867,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-3xl font-bold text-white mb-1 group-hover:text-violet-200 transition-colors\",\n                                    children: tool.stats.generated\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 874,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-400 group-hover:text-gray-300 transition-colors\",\n                                    children: \"Content Generated - Click to view\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 875,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center text-xs text-violet-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                className: \"w-3 h-3 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 880,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"View in Content Library\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 881,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 879,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 878,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                            lineNumber: 860,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 859,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 0.35\n                        },\n                        className: \"backdrop-blur-xl border rounded-2xl p-6 bg-white/5 border-white/10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"w-8 h-8\", \"text-\".concat(tool.accentColor, \"-400\"))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 894,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            1,\n                                            2,\n                                            3,\n                                            4,\n                                            5\n                                        ].map((star)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"w-3 h-3 rounded-full\", star <= Math.round(tool.stats.quality / 2) ? \"bg-yellow-400\" : \"bg-gray-600\")\n                                            }, star, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 897,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 895,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 893,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-3xl font-bold text-white mb-1\",\n                                children: [\n                                    tool.stats.quality,\n                                    \"/10\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 909,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-400\",\n                                children: \"Quality Score\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 910,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 887,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 0.45\n                        },\n                        className: \"backdrop-blur-xl border rounded-2xl p-6 bg-white/5 border-white/10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"w-8 h-8\", \"text-\".concat(tool.accentColor, \"-400\"))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 920,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                        className: \"w-5 h-5 text-blue-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 921,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 919,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-3xl font-bold text-white mb-1\",\n                                children: tool.stats.avgTime\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 923,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-400\",\n                                children: \"Average Time\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 924,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 913,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 853,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n        lineNumber: 772,\n        columnNumber: 5\n    }, undefined);\n});\n_c15 = ToolDetails;\n// Optimized Dashboard Overview Component with Memoization\nconst DashboardOverview = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo((param)=>{\n    let { tools, userProfile, getDisplayName, userStats, isLoadingStats } = param;\n    var _userStats_totalContent, _userStats_trends, _userStats_trends1, _userStats_trends2, _userStats_trends_toolsActive, _userStats_trends3, _userStats_trends4;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n        initial: {\n            opacity: 0,\n            scale: 0.95\n        },\n        animate: {\n            opacity: 1,\n            scale: 1\n        },\n        exit: {\n            opacity: 0,\n            scale: 0.95\n        },\n        transition: {\n            duration: 0.2,\n            ease: \"easeOut\"\n        },\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-violet-800/20 to-indigo-800/20 backdrop-blur-xl border border-white/10 rounded-3xl p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl font-bold text-white mb-4\",\n                        children: [\n                            \"Welcome back, \",\n                            getDisplayName(),\n                            \"! ✨\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 949,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-300 mb-6\",\n                        children: \"Your creative AI toolkit is ready. What will you create today?\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 950,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                        children: isLoadingStats ? // Loading skeleton\n                        Array.from({\n                            length: 4\n                        }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: index * 0.1\n                                },\n                                className: \"bg-black/40 backdrop-blur-sm border border-white/10 rounded-xl p-4 animate-pulse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-5 bg-white/10 rounded mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 963,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-8 bg-white/10 rounded mb-1\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 964,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-white/10 rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 965,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, index, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 956,\n                                columnNumber: 15\n                            }, undefined)) : [\n                            {\n                                label: 'Total Created',\n                                value: (userStats === null || userStats === void 0 ? void 0 : (_userStats_totalContent = userStats.totalContent) === null || _userStats_totalContent === void 0 ? void 0 : _userStats_totalContent.toString()) || '0',\n                                icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                                change: (userStats === null || userStats === void 0 ? void 0 : (_userStats_trends = userStats.trends) === null || _userStats_trends === void 0 ? void 0 : _userStats_trends.contentGrowth) || '+0%'\n                            },\n                            {\n                                label: 'Time Saved',\n                                value: \"\".concat((userStats === null || userStats === void 0 ? void 0 : userStats.timeSavedHours) || 0, \" hrs\"),\n                                icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"],\n                                change: (userStats === null || userStats === void 0 ? void 0 : (_userStats_trends1 = userStats.trends) === null || _userStats_trends1 === void 0 ? void 0 : _userStats_trends1.timeEfficiency) || '+0%'\n                            },\n                            {\n                                label: 'Quality Score',\n                                value: \"\".concat((userStats === null || userStats === void 0 ? void 0 : userStats.qualityScore) || 9.0, \"/10\"),\n                                icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"],\n                                change: (userStats === null || userStats === void 0 ? void 0 : (_userStats_trends2 = userStats.trends) === null || _userStats_trends2 === void 0 ? void 0 : _userStats_trends2.qualityImprovement) || '+0.0'\n                            },\n                            {\n                                label: 'Active Tools',\n                                value: (userStats === null || userStats === void 0 ? void 0 : (_userStats_trends3 = userStats.trends) === null || _userStats_trends3 === void 0 ? void 0 : (_userStats_trends_toolsActive = _userStats_trends3.toolsActive) === null || _userStats_trends_toolsActive === void 0 ? void 0 : _userStats_trends_toolsActive.toString()) || '0',\n                                icon: _barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                                change: '+' + ((userStats === null || userStats === void 0 ? void 0 : (_userStats_trends4 = userStats.trends) === null || _userStats_trends4 === void 0 ? void 0 : _userStats_trends4.toolsActive) || 0)\n                            }\n                        ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: index * 0.1\n                                },\n                                className: \"bg-black/40 backdrop-blur-sm border border-white/10 rounded-xl p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                                className: \"w-5 h-5 text-violet-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 1003,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-emerald-400\",\n                                                children: stat.change\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 1004,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 1002,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-white\",\n                                        children: stat.value\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 1006,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: stat.label\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 1007,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, stat.label, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 995,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 952,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 948,\n                columnNumber: 15\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white mb-6\",\n                        children: \"Your AI Tools\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 1016,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                        children: tools.map((tool, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: tool.href,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        scale: 0.9\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        scale: 1\n                                    },\n                                    transition: {\n                                        delay: index * 0.1\n                                    },\n                                    whileHover: {\n                                        y: -8\n                                    },\n                                    className: \"group relative cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl blur-xl\", tool.color)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 1027,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative backdrop-blur-xl border transition-all bg-black/60 border-white/10 hover:border-white/20 rounded-2xl\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start justify-between mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"p-3 rounded-xl text-white bg-gradient-to-br\", tool.color),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tool.icon, {\n                                                                    className: \"w-6 h-6\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 1040,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 1036,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                className: \"w-5 h-5 text-gray-400 group-hover:text-white transition-colors\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 1042,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 1035,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-white mb-1\",\n                                                        children: tool.title\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 1045,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-400 mb-4\",\n                                                        children: tool.subtitle\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 1046,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between text-xs\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: \"/content?type=\".concat(tool.id === 'blog-generator' ? 'blog' : tool.id === 'email-generator' ? 'email' : tool.id === 'youtube-script' ? 'youtube_script' : tool.id === 'invincible-writer' ? 'invincible_writer' : tool.id.replace('-', '_')),\n                                                                onClick: (e)=>e.stopPropagation(),\n                                                                className: \"hover:bg-white/10 rounded px-2 py-1 transition-colors group/stat\",\n                                                                title: \"View all \".concat(tool.title, \" content\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-500 group-hover/stat:text-gray-400\",\n                                                                        children: \"Generated\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 1055,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-white font-medium group-hover/stat:text-violet-200\",\n                                                                                children: tool.stats.generated\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                                lineNumber: 1057,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Bell_Bot_ChevronLeft_Clock_Eye_FileText_HelpCircle_Home_Layers_Mail_Menu_Rocket_Search_Settings_Shield_Sparkles_TrendingUp_Video_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                className: \"w-3 h-3 text-gray-400 group-hover/stat:text-white opacity-0 group-hover/stat:opacity-100 transition-all\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                                lineNumber: 1058,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 1056,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black/90 text-white text-xs rounded opacity-0 group-hover/stat:opacity-100 transition-opacity pointer-events-none whitespace-nowrap\",\n                                                                        children: \"Click to view content\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 1061,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 1049,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-right\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-500\",\n                                                                        children: \"Quality\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 1066,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-white font-medium\",\n                                                                        children: [\n                                                                            tool.stats.quality,\n                                                                            \"/10\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 1067,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 1065,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 1048,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 1034,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 1032,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 1020,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, tool.id, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 1019,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 1017,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 1015,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(OptimizedRecentContent, {\n                    limit: 5,\n                    showFilters: true\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                    lineNumber: 1080,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                lineNumber: 1079,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n        lineNumber: 940,\n        columnNumber: 5\n    }, undefined);\n});\n_c16 = DashboardOverview;\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15, _c16;\n$RefreshReg$(_c, \"OptimizedRecentContent\");\n$RefreshReg$(_c1, \"OptimizedBlogPreview$dynamic\");\n$RefreshReg$(_c2, \"OptimizedBlogPreview\");\n$RefreshReg$(_c3, \"OptimizedEmailPreview$dynamic\");\n$RefreshReg$(_c4, \"OptimizedEmailPreview\");\n$RefreshReg$(_c5, \"OptimizedSocialMediaPreview$dynamic\");\n$RefreshReg$(_c6, \"OptimizedSocialMediaPreview\");\n$RefreshReg$(_c7, \"OptimizedVideoScriptPreview$dynamic\");\n$RefreshReg$(_c8, \"OptimizedVideoScriptPreview\");\n$RefreshReg$(_c9, \"OptimizedVideoAlchemyPreview$dynamic\");\n$RefreshReg$(_c10, \"OptimizedVideoAlchemyPreview\");\n$RefreshReg$(_c11, \"OptimizedMegatronPreview$dynamic\");\n$RefreshReg$(_c12, \"OptimizedMegatronPreview\");\n$RefreshReg$(_c13, \"EnhancedSidebar\");\n$RefreshReg$(_c14, \"DashboardPage\");\n$RefreshReg$(_c15, \"ToolDetails\");\n$RefreshReg$(_c16, \"DashboardOverview\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});