"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/content-writer/page",{

/***/ "(app-pages-browser)/./src/app/content-writer/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/content-writer/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ InvincibleContentWriter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-tool.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// Enhanced team analytics with Invincible theme\nconst getTeamAnalytics = ()=>({\n        agents: [\n            {\n                name: 'Research Agent',\n                specialization: 'Web Research & Data Collection',\n                tools: [\n                    'Tavily Search',\n                    'SERP Analysis'\n                ],\n                performance: 'Excellent',\n                avgExecutionTime: '45s',\n                powerLevel: 95,\n                specialAbility: 'Deep Web Intelligence'\n            },\n            {\n                name: 'SEO Strategist',\n                specialization: 'Keyword Research & Strategy',\n                tools: [\n                    'Keyword Analysis',\n                    'Competition Research'\n                ],\n                performance: 'Very Good',\n                avgExecutionTime: '30s',\n                powerLevel: 88,\n                specialAbility: 'Search Domination'\n            },\n            {\n                name: 'Content Architect',\n                specialization: 'Content Structure & Planning',\n                tools: [\n                    'Outline Generator',\n                    'Content Planner'\n                ],\n                performance: 'Excellent',\n                avgExecutionTime: '25s',\n                powerLevel: 92,\n                specialAbility: 'Structure Mastery'\n            },\n            {\n                name: 'Content Creator',\n                specialization: 'AI-Powered Writing',\n                tools: [\n                    'Kimi K2 Model',\n                    'Content Generator'\n                ],\n                performance: 'Outstanding',\n                avgExecutionTime: '2m',\n                powerLevel: 98,\n                specialAbility: 'Creative Genesis'\n            },\n            {\n                name: 'Quality Editor',\n                specialization: 'Content Review & Optimization',\n                tools: [\n                    'Grammar Check',\n                    'SEO Optimization'\n                ],\n                performance: 'Excellent',\n                avgExecutionTime: '40s',\n                powerLevel: 90,\n                specialAbility: 'Perfection Protocol'\n            }\n        ],\n        capabilities: {\n            content_types: [\n                'Blog Posts',\n                'Articles',\n                'Landing Pages',\n                'Social Media',\n                'Email Campaigns',\n                'Product Descriptions'\n            ],\n            word_count_range: '500-5000',\n            seo_optimization: 'Real-time SEO scoring and recommendations',\n            fact_checking: 'Automated fact-checking with source verification',\n            brand_voice: 'Adaptive brand voice matching'\n        },\n        workflows: [\n            {\n                name: 'Standard Content Creation',\n                agents: 5,\n                tasks: 5,\n                estimatedTime: '4-6 minutes',\n                costEstimate: '$0.40-0.80',\n                success_rate: '98%'\n            },\n            {\n                name: 'SEO-Focused Content',\n                agents: 5,\n                tasks: 7,\n                estimatedTime: '6-8 minutes',\n                costEstimate: '$0.60-1.00',\n                success_rate: '96%'\n            },\n            {\n                name: 'Technical Content',\n                agents: 5,\n                tasks: 6,\n                estimatedTime: '8-12 minutes',\n                costEstimate: '$0.80-1.20',\n                success_rate: '94%'\n            }\n        ]\n    });\nconst validateTeamConfiguration = ()=>({\n        isValid: true,\n        issues: []\n    });\nfunction InvincibleContentWriter() {\n    _s();\n    const [projects, setProjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentProject, setCurrentProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [newProjectTopic, setNewProjectTopic] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [targetKeywords, setTargetKeywords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [contentType, setContentType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('blog-post');\n    const [wordCount, setWordCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(2000);\n    const [isRunning, setIsRunning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [totalCost, setTotalCost] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [previewContent, setPreviewContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [teamAnalytics, setTeamAnalytics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(getTeamAnalytics());\n    const [workflowError, setWorkflowError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('command-center');\n    const [topic, setTopic] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [keywords, setKeywords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [targetLength, setTargetLength] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(2000);\n    // Enhanced UI states\n    const [systemStatus, setSystemStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('READY');\n    const [powerLevel, setPowerLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(100);\n    // Mock current task data\n    const getOverallProgress = ()=>{\n        if (!currentProject || !currentProject.steps || currentProject.steps.length === 0) return 0;\n        const completedSteps = currentProject.steps.filter((s)=>s.status === 'completed').length;\n        return completedSteps / currentProject.steps.length * 100;\n    };\n    // Current task computed state\n    const currentTask = {\n        topic: (currentProject === null || currentProject === void 0 ? void 0 : currentProject.topic) || topic || '',\n        status: (currentProject === null || currentProject === void 0 ? void 0 : currentProject.status) || 'pending',\n        progress: getOverallProgress()\n    };\n    // Enhanced agents data with power levels\n    const [agents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 'Research Agent',\n            name: 'Research Agent',\n            status: 'idle',\n            progress: 0,\n            currentTask: '',\n            cost: 0,\n            powerLevel: 95,\n            specialAbility: 'Deep Web Intelligence'\n        },\n        {\n            id: 'SEO Strategist',\n            name: 'SEO Strategist',\n            status: 'idle',\n            progress: 0,\n            currentTask: '',\n            cost: 0,\n            powerLevel: 88,\n            specialAbility: 'Search Domination'\n        },\n        {\n            id: 'Content Architect',\n            name: 'Content Architect',\n            status: 'idle',\n            progress: 0,\n            currentTask: '',\n            cost: 0,\n            powerLevel: 92,\n            specialAbility: 'Structure Mastery'\n        },\n        {\n            id: 'Content Creator',\n            name: 'Content Creator',\n            status: 'idle',\n            progress: 0,\n            currentTask: '',\n            cost: 0,\n            powerLevel: 98,\n            specialAbility: 'Creative Genesis'\n        },\n        {\n            id: 'Quality Editor',\n            name: 'Quality Editor',\n            status: 'idle',\n            progress: 0,\n            currentTask: '',\n            cost: 0,\n            powerLevel: 90,\n            specialAbility: 'Perfection Protocol'\n        }\n    ]);\n    const agentIcons = {\n        'Research Agent': _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        'SEO Strategist': _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        'Content Architect': _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        'Content Creator': _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        'Quality Editor': _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    };\n    // Placeholder functions\n    const stopGeneration = ()=>{\n        setIsRunning(false);\n        setSystemStatus('STOPPED');\n    };\n    const resetWorkflow = ()=>{\n        setIsRunning(false);\n        setCurrentProject(null);\n        setTotalCost(0);\n        setPreviewContent('');\n        setWorkflowError(null);\n        setSystemStatus('READY');\n        setPowerLevel(100);\n    };\n    const startContentGeneration = async ()=>{\n        if (!topic.trim()) {\n            alert('Please enter a topic first');\n            return;\n        }\n        setSystemStatus('INITIALIZING');\n        setPowerLevel(95);\n        // Create a new project with steps\n        const project = {\n            id: Date.now().toString(),\n            topic: topic,\n            targetKeywords: keywords.split(',').map((k)=>k.trim()).filter(Boolean),\n            contentType,\n            wordCount: targetLength,\n            status: 'draft',\n            createdAt: new Date(),\n            steps: [\n                {\n                    id: '1',\n                    name: 'Research & Data Collection',\n                    status: 'pending',\n                    progress: 0,\n                    agent: 'Research Agent'\n                },\n                {\n                    id: '2',\n                    name: 'SEO Strategy & Keywords',\n                    status: 'pending',\n                    progress: 0,\n                    agent: 'SEO Strategist'\n                },\n                {\n                    id: '3',\n                    name: 'Content Architecture',\n                    status: 'pending',\n                    progress: 0,\n                    agent: 'Content Architect'\n                },\n                {\n                    id: '4',\n                    name: 'Content Creation',\n                    status: 'pending',\n                    progress: 0,\n                    agent: 'Content Creator'\n                },\n                {\n                    id: '5',\n                    name: 'Quality Review & Optimization',\n                    status: 'pending',\n                    progress: 0,\n                    agent: 'Quality Editor'\n                }\n            ]\n        };\n        setCurrentProject(project);\n        setProjects((prev)=>[\n                project,\n                ...prev\n            ]);\n        setIsRunning(true);\n        setSystemStatus('ACTIVE');\n        setWorkflowError(null);\n        try {\n            const validation = validateTeamConfiguration();\n            if (!validation.isValid) {\n                throw new Error(\"Configuration errors: \".concat(validation.issues.join(', ')));\n            }\n            const updatedProject = {\n                ...project,\n                status: 'in_progress'\n            };\n            setCurrentProject(updatedProject);\n            for(let i = 0; i < updatedProject.steps.length; i++){\n                const step = updatedProject.steps[i];\n                step.status = 'running';\n                setSystemStatus(\"EXECUTING: \".concat(step.agent.toUpperCase()));\n                setCurrentProject({\n                    ...updatedProject\n                });\n                const executionTimes = {\n                    'Research Agent': 45000,\n                    'SEO Strategist': 30000,\n                    'Content Architect': 25000,\n                    'Content Creator': 120000,\n                    'Quality Editor': 40000\n                };\n                const stepTime = executionTimes[step.agent] || 30000;\n                const progressSteps = 20;\n                const intervalTime = stepTime / progressSteps;\n                for(let progress = 0; progress <= 100; progress += 5){\n                    step.progress = progress;\n                    setPowerLevel(Math.max(70, 100 - progress * 0.3));\n                    setCurrentProject({\n                        ...updatedProject\n                    });\n                    await new Promise((resolve)=>setTimeout(resolve, intervalTime / 20));\n                }\n                step.status = 'completed';\n                const costs = {\n                    'Research Agent': 0.08,\n                    'SEO Strategist': 0.05,\n                    'Content Architect': 0.04,\n                    'Content Creator': 0.25,\n                    'Quality Editor': 0.06\n                };\n                step.cost = costs[step.agent] || 0.05;\n                step.duration = stepTime / 1000;\n                if (step.agent === 'Content Creator') {\n                    step.output = generateSampleContent(updatedProject.topic);\n                    setPreviewContent(step.output);\n                }\n                setTotalCost((prev)=>prev + (step.cost || 0));\n                setPowerLevel(100);\n            }\n            updatedProject.status = 'completed';\n            setCurrentProject(updatedProject);\n            setProjects((prev)=>prev.map((p)=>p.id === updatedProject.id ? updatedProject : p));\n            setSystemStatus('COMPLETED');\n        } catch (error) {\n            console.error('Workflow execution failed:', error);\n            setWorkflowError(error instanceof Error ? error.message : 'Unknown error occurred');\n            setSystemStatus('ERROR');\n        } finally{\n            setIsRunning(false);\n        }\n    };\n    const generateSampleContent = (topic)=>{\n        return \"# \".concat(topic, \"\\n\\n## Introduction\\n\\nThis comprehensive guide explores \").concat(topic, \" and provides valuable insights for readers seeking to understand this important subject.\\n\\n## Key Points\\n\\n- **Research-backed insights**: Our analysis is based on the latest industry research and expert opinions\\n- **Practical applications**: Real-world examples and actionable strategies\\n- **Future trends**: What to expect in the evolving landscape\\n\\n## Detailed Analysis\\n\\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.\\n\\n### Section 1: Understanding the Basics\\n\\nDetailed explanation of fundamental concepts and principles.\\n\\n### Section 2: Advanced Strategies  \\n\\nMore sophisticated approaches and methodologies.\\n\\n## Conclusion\\n\\nThis content has been optimized for search engines while maintaining high readability and engagement for human readers.\\n\\n*Generated by Invincible AI Content Team - Powered by KaibanJS & Kimi K2*\");\n    };\n    const exportContent = (format)=>{\n        if (!previewContent) {\n            alert('No content to export. Please generate content first.');\n            return;\n        }\n        let content = '';\n        let filename = '';\n        let mimeType = '';\n        switch(format){\n            case 'md':\n                content = previewContent;\n                filename = \"\".concat((topic === null || topic === void 0 ? void 0 : topic.replace(/\\s+/g, '-').toLowerCase()) || 'content', \".md\");\n                mimeType = 'text/markdown';\n                break;\n            case 'html':\n                content = '<!DOCTYPE html>\\n<html lang=\"en\">\\n<head>\\n    <meta charset=\"UTF-8\">\\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\\n    <title>'.concat(topic || 'Generated Content', \"</title>\\n    <style>\\n        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; max-width: 800px; margin: 0 auto; padding: 20px; }\\n        h1, h2, h3 { color: #333; }\\n        p { margin-bottom: 16px; }\\n        ul, ol { margin-bottom: 16px; padding-left: 20px; }\\n    </style>\\n</head>\\n<body>\\n    \").concat(previewContent.replace(/\\n/g, '<br>'), \"\\n</body>\\n</html>\");\n                filename = \"\".concat((topic === null || topic === void 0 ? void 0 : topic.replace(/\\s+/g, '-').toLowerCase()) || 'content', \".html\");\n                mimeType = 'text/html';\n                break;\n            case 'txt':\n                content = previewContent.replace(/[#*`]/g, '').replace(/\\n+/g, '\\n');\n                filename = \"\".concat((topic === null || topic === void 0 ? void 0 : topic.replace(/\\s+/g, '-').toLowerCase()) || 'content', \".txt\");\n                mimeType = 'text/plain';\n                break;\n        }\n        const blob = new Blob([\n            content\n        ], {\n            type: mimeType\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = filename;\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n    };\n    const copyToClipboard = async ()=>{\n        if (!previewContent) return;\n        try {\n            await navigator.clipboard.writeText(previewContent);\n            alert('Content copied to clipboard!');\n        } catch (err) {\n            console.error('Failed to copy content:', err);\n            alert('Failed to copy content to clipboard');\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-blue-500/30 bg-slate-900/80 backdrop-blur-lg sticky top-0 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-6 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-10 w-10 text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 444,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 442,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl font-bold bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent\",\n                                                children: \"INVINCIBLE AI WRITER\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 447,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-slate-400\",\n                                                children: \"Multi-Agent Content Generation System\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                lineNumber: 441,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium text-blue-400\",\n                                                children: \"SYSTEM STATUS\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-slate-300\",\n                                                children: systemStatus\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 458,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 456,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative w-16 h-16\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 rounded-full bg-gradient-to-r from-blue-500 to-cyan-500 animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 461,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-1 rounded-full bg-slate-900 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-bold text-cyan-400\",\n                                                    children: [\n                                                        powerLevel,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                    lineNumber: 463,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                lineNumber: 455,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                        lineNumber: 440,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                    lineNumber: 439,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                lineNumber: 438,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-6 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-1 bg-slate-800/50 p-2 rounded-xl mb-8 backdrop-blur-sm\",\n                        children: [\n                            {\n                                id: 'command-center',\n                                label: 'Command Center',\n                                icon: _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n                            },\n                            {\n                                id: 'mission-config',\n                                label: 'Mission Config',\n                                icon: _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                            },\n                            {\n                                id: 'content-preview',\n                                label: 'Content Preview',\n                                icon: _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                            },\n                            {\n                                id: 'analytics',\n                                label: 'Analytics',\n                                icon: _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                            }\n                        ].map((param)=>{\n                            let { id, label, icon: Icon } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(id),\n                                className: \"flex items-center space-x-2 px-4 py-3 rounded-lg font-medium transition-all duration-200 \".concat(activeTab === id ? 'bg-gradient-to-r from-blue-500 to-cyan-500 text-white shadow-lg' : 'text-slate-400 hover:text-white hover:bg-slate-700/50'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: label\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 490,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, id, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                lineNumber: 480,\n                                columnNumber: 13\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                        lineNumber: 473,\n                        columnNumber: 9\n                    }, this),\n                    activeTab === 'command-center' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-slate-800/50 to-slate-900/50 rounded-2xl border border-blue-500/20 backdrop-blur-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 border-b border-slate-700/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-6 w-6 text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 502,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl font-bold\",\n                                                        children: \"Mission Control\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 503,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: startContentGeneration,\n                                                        disabled: isRunning || !topic.trim(),\n                                                        className: \"flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 disabled:from-slate-600 disabled:to-slate-700 disabled:cursor-not-allowed rounded-lg font-medium transition-all duration-200 shadow-lg hover:shadow-xl\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 511,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: isRunning ? 'EXECUTING...' : 'INITIATE MISSION'\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 512,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 506,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: stopGeneration,\n                                                        disabled: !isRunning,\n                                                        className: \"flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-red-500 to-rose-500 hover:from-red-600 hover:to-rose-600 disabled:from-slate-600 disabled:to-slate-700 disabled:cursor-not-allowed rounded-lg font-medium transition-all duration-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 519,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"ABORT\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 520,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 514,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: resetWorkflow,\n                                                        className: \"flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 rounded-lg font-medium transition-all duration-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 526,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"RESET\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 527,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 522,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 505,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg font-medium\",\n                                                        children: topic || 'No active mission'\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 535,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"px-3 py-1 rounded-full text-sm font-medium \".concat(currentTask.status === 'completed' ? 'bg-green-500/20 text-green-400' : currentTask.status === 'in_progress' ? 'bg-blue-500/20 text-blue-400' : 'bg-slate-500/20 text-slate-400'),\n                                                        children: currentTask.status.toUpperCase()\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 538,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 534,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative h-3 bg-slate-700 rounded-full overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-y-0 left-0 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full transition-all duration-500\",\n                                                        style: {\n                                                            width: \"\".concat(currentTask.progress, \"%\")\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 548,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 552,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 547,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-sm text-slate-400 mt-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            Math.round(currentTask.progress),\n                                                            \"% Complete\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 556,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Mission Cost: $\",\n                                                            totalCost.toFixed(3)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 557,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 555,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 533,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                lineNumber: 499,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-6 w-6 text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 565,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-bold\",\n                                                children: \"Agent Squadron Status\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 566,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 564,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                        children: agents.map((agent)=>{\n                                            const Icon = agentIcons[agent.id];\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-br from-slate-800/50 to-slate-900/50 rounded-xl border border-slate-700/50 p-6 backdrop-blur-sm hover:border-blue-500/30 transition-all duration-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3 mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 rounded-lg \".concat(agent.status === 'running' ? 'bg-blue-500/20 text-blue-400' : agent.status === 'completed' ? 'bg-green-500/20 text-green-400' : agent.status === 'error' ? 'bg-red-500/20 text-red-400' : 'bg-slate-500/20 text-slate-400'),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                    className: \"h-6 w-6\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 581,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 575,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-bold text-sm\",\n                                                                        children: agent.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                        lineNumber: 584,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-slate-400\",\n                                                                        children: agent.specialAbility\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                        lineNumber: 585,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 583,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 574,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between text-xs text-slate-400 mb-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Power Level\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                        lineNumber: 592,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            agent.powerLevel,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                        lineNumber: 593,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 591,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-2 bg-slate-700 rounded-full overflow-hidden\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-full rounded-full transition-all duration-500 \".concat(agent.powerLevel >= 95 ? 'bg-gradient-to-r from-green-400 to-emerald-400' : agent.powerLevel >= 85 ? 'bg-gradient-to-r from-blue-400 to-cyan-400' : 'bg-gradient-to-r from-yellow-400 to-orange-400'),\n                                                                    style: {\n                                                                        width: \"\".concat(agent.powerLevel, \"%\")\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 596,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 595,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 590,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-1 bg-slate-700 rounded-full overflow-hidden mb-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-full rounded-full transition-all duration-300 \".concat(agent.status === 'completed' ? 'bg-green-400' : agent.status === 'running' ? 'bg-blue-400 animate-pulse' : 'bg-slate-600'),\n                                                            style: {\n                                                                width: \"\".concat(agent.progress, \"%\")\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 609,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 608,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-xs\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 rounded-full \".concat(agent.status === 'running' ? 'bg-blue-500/20 text-blue-400' : agent.status === 'completed' ? 'bg-green-500/20 text-green-400' : agent.status === 'error' ? 'bg-red-500/20 text-red-400' : 'bg-slate-500/20 text-slate-400'),\n                                                                children: agent.status.toUpperCase()\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 620,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            agent.cost > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-slate-400\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    agent.cost.toFixed(3)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 628,\n                                                                columnNumber: 44\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 619,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, agent.id, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 573,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 569,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                lineNumber: 563,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                        lineNumber: 497,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'mission-config' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-br from-slate-800/50 to-slate-900/50 rounded-2xl border border-blue-500/20 backdrop-blur-sm p-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-6 w-6 text-blue-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                            lineNumber: 643,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-bold\",\n                                            children: \"Mission Configuration\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                            lineNumber: 644,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                    lineNumber: 642,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-slate-300 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                    className: \"h-4 w-4 inline mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 651,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Primary Target *\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 650,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: topic,\n                                                            onChange: (e)=>setTopic(e.target.value),\n                                                            placeholder: \"Enter your content mission objective...\",\n                                                            className: \"w-full px-4 py-3 bg-slate-800/50 border border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 654,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                    lineNumber: 649,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-slate-300 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"h-4 w-4 inline mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 665,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"SEO Keywords\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 664,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: keywords,\n                                                            onChange: (e)=>setKeywords(e.target.value),\n                                                            placeholder: \"keyword1, keyword2, keyword3...\",\n                                                            className: \"w-full px-4 py-3 bg-slate-800/50 border border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 668,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                    lineNumber: 663,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                            lineNumber: 648,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-slate-300 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    className: \"h-4 w-4 inline mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 681,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Mission Type\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 680,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: contentType,\n                                                            onChange: (e)=>setContentType(e.target.value),\n                                                            className: \"w-full px-4 py-3 bg-slate-800/50 border border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"blog-post\",\n                                                                    children: \"Blog Post\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 689,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"article\",\n                                                                    children: \"Article\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 690,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"landing-page\",\n                                                                    children: \"Landing Page\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 691,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"product-description\",\n                                                                    children: \"Product Description\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 692,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"social-media\",\n                                                                    children: \"Social Media Series\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 693,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 684,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                    lineNumber: 679,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-slate-300 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-4 w-4 inline mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 699,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Target Scope: \",\n                                                                targetLength,\n                                                                \" words\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 698,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"range\",\n                                                            min: \"500\",\n                                                            max: \"5000\",\n                                                            step: \"100\",\n                                                            value: targetLength,\n                                                            onChange: (e)=>setTargetLength(parseInt(e.target.value)),\n                                                            className: \"w-full h-2 bg-slate-700 rounded-lg appearance-none cursor-pointer slider\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 702,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-xs text-slate-400 mt-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 712,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"5000\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 713,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 711,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                    lineNumber: 697,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                            lineNumber: 678,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                    lineNumber: 647,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                            lineNumber: 641,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                        lineNumber: 640,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'content-preview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-br from-slate-800/50 to-slate-900/50 rounded-2xl border border-blue-500/20 backdrop-blur-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border-b border-slate-700/50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-6 w-6 text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 729,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl font-bold\",\n                                                        children: \"Generated Content\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 730,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 728,\n                                                columnNumber: 19\n                                            }, this),\n                                            previewContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>exportContent('md'),\n                                                        className: \"px-4 py-2 bg-slate-700 hover:bg-slate-600 border border-slate-600 rounded-lg text-sm font-medium transition-colors flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 738,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"MD\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 739,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 734,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>exportContent('html'),\n                                                        className: \"px-4 py-2 bg-slate-700 hover:bg-slate-600 border border-slate-600 rounded-lg text-sm font-medium transition-colors flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 745,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"HTML\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 746,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 741,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: copyToClipboard,\n                                                        className: \"px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg text-sm font-medium transition-colors\",\n                                                        children: \"Copy\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 748,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 733,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 727,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                    lineNumber: 726,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-slate-900/50 rounded-xl border border-slate-700/50 p-6 min-h-[400px]\",\n                                        children: previewContent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"prose prose-invert prose-blue max-w-none\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_22__.Markdown, {\n                                                children: previewContent\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 763,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                            lineNumber: 762,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center h-full text-slate-500\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-16 w-16 mx-auto mb-4 opacity-50\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 768,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg\",\n                                                        children: \"Content will materialize here after mission execution\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 769,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm mt-2\",\n                                                        children: \"Configure your mission and initiate the content generation process\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 770,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 767,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                            lineNumber: 766,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 760,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                    lineNumber: 759,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                            lineNumber: 725,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                        lineNumber: 724,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'analytics' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            workflowError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-500/10 border border-red-500/30 rounded-xl p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center text-red-400\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"h-5 w-5 mr-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                            lineNumber: 787,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: \"Mission Error: \"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                            lineNumber: 788,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2\",\n                                            children: workflowError\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                            lineNumber: 789,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                    lineNumber: 786,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                lineNumber: 785,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                className: \"h-6 w-6 text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 797,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-bold\",\n                                                children: \"Performance Metrics\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 798,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 796,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-br from-green-500/10 to-emerald-500/10 border border-green-500/20 rounded-xl p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                            className: \"h-10 w-10 text-green-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 804,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-slate-400\",\n                                                                    children: \"Mission Cost\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 806,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-2xl font-bold text-green-400\",\n                                                                    children: [\n                                                                        \"$\",\n                                                                        totalCost.toFixed(3)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 807,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-green-500\",\n                                                                    children: \"85-95% savings\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 808,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 805,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                    lineNumber: 803,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 802,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-br from-blue-500/10 to-cyan-500/10 border border-blue-500/20 rounded-xl p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                            className: \"h-10 w-10 text-blue-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 815,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-slate-400\",\n                                                                    children: \"Avg. Generation\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 817,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-2xl font-bold text-blue-400\",\n                                                                    children: \"4-6m\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 818,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-blue-500\",\n                                                                    children: \"Per mission\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 819,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 816,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                    lineNumber: 814,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 813,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-br from-purple-500/10 to-pink-500/10 border border-purple-500/20 rounded-xl p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-10 w-10 text-purple-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 826,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-slate-400\",\n                                                                    children: \"Quality Score\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 828,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-2xl font-bold text-purple-400\",\n                                                                    children: \"9.2/10\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 829,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-purple-500\",\n                                                                    children: \"EQ-Bench rating\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 830,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 827,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                    lineNumber: 825,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 824,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-br from-cyan-500/10 to-teal-500/10 border border-cyan-500/20 rounded-xl p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"h-10 w-10 text-cyan-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 837,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-slate-400\",\n                                                                    children: \"Success Rate\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 839,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-2xl font-bold text-cyan-400\",\n                                                                    children: \"98%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 840,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-cyan-500\",\n                                                                    children: \"Mission completion\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 841,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 838,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                    lineNumber: 836,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 835,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 801,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                lineNumber: 795,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-slate-800/50 to-slate-900/50 rounded-2xl border border-blue-500/20 backdrop-blur-sm p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold mb-6\",\n                                        children: \"Agent Squadron Analytics\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 850,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: teamAnalytics.agents.map((agent, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-6 bg-slate-800/30 border border-slate-700/50 rounded-xl\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-3 h-3 rounded-full bg-green-400 animate-pulse\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 855,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-bold text-white\",\n                                                                        children: agent.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                        lineNumber: 857,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-slate-400\",\n                                                                        children: agent.specialization\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                        lineNumber: 858,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-slate-500\",\n                                                                        children: [\n                                                                            \"Tools: \",\n                                                                            agent.tools.join(', ')\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                        lineNumber: 859,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 856,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 854,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium text-blue-400\",\n                                                                children: agent.performance\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 863,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-slate-400\",\n                                                                children: agent.avgExecutionTime\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 864,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mt-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-slate-500\",\n                                                                        children: \"Power:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                        lineNumber: 866,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs font-bold text-green-400\",\n                                                                        children: [\n                                                                            agent.powerLevel,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                        lineNumber: 867,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 865,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 862,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 853,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 851,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                lineNumber: 849,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                        lineNumber: 782,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                lineNumber: 471,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n        lineNumber: 436,\n        columnNumber: 5\n    }, this);\n}\n_s(InvincibleContentWriter, \"ET5b6b78G4JwAwqymrc0p4X4jLw=\");\n_c = InvincibleContentWriter;\nvar _c;\n$RefreshReg$(_c, \"InvincibleContentWriter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/content-writer/page.tsx\n"));

/***/ })

});