"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/content-writer/page",{

/***/ "(app-pages-browser)/./src/app/content-writer/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/content-writer/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ InvincibleContentWriter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-tool.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// Enhanced team analytics with Invincible theme\nconst getTeamAnalytics = ()=>({\n        agents: [\n            {\n                name: 'Research Agent',\n                specialization: 'Web Research & Data Collection',\n                tools: [\n                    'Tavily Search',\n                    'SERP Analysis'\n                ],\n                performance: 'Excellent',\n                avgExecutionTime: '45s',\n                powerLevel: 95,\n                specialAbility: 'Deep Web Intelligence'\n            },\n            {\n                name: 'SEO Strategist',\n                specialization: 'Keyword Research & Strategy',\n                tools: [\n                    'Keyword Analysis',\n                    'Competition Research'\n                ],\n                performance: 'Very Good',\n                avgExecutionTime: '30s',\n                powerLevel: 88,\n                specialAbility: 'Search Domination'\n            },\n            {\n                name: 'Content Architect',\n                specialization: 'Content Structure & Planning',\n                tools: [\n                    'Outline Generator',\n                    'Content Planner'\n                ],\n                performance: 'Excellent',\n                avgExecutionTime: '25s',\n                powerLevel: 92,\n                specialAbility: 'Structure Mastery'\n            },\n            {\n                name: 'Content Creator',\n                specialization: 'AI-Powered Writing',\n                tools: [\n                    'Kimi K2 Model',\n                    'Content Generator'\n                ],\n                performance: 'Outstanding',\n                avgExecutionTime: '2m',\n                powerLevel: 98,\n                specialAbility: 'Creative Genesis'\n            },\n            {\n                name: 'Quality Editor',\n                specialization: 'Content Review & Optimization',\n                tools: [\n                    'Grammar Check',\n                    'SEO Optimization'\n                ],\n                performance: 'Excellent',\n                avgExecutionTime: '40s',\n                powerLevel: 90,\n                specialAbility: 'Perfection Protocol'\n            }\n        ],\n        capabilities: {\n            content_types: [\n                'Blog Posts',\n                'Articles',\n                'Landing Pages',\n                'Social Media',\n                'Email Campaigns',\n                'Product Descriptions'\n            ],\n            word_count_range: '500-5000',\n            seo_optimization: 'Real-time SEO scoring and recommendations',\n            fact_checking: 'Automated fact-checking with source verification',\n            brand_voice: 'Adaptive brand voice matching'\n        },\n        workflows: [\n            {\n                name: 'Standard Content Creation',\n                agents: 5,\n                tasks: 5,\n                estimatedTime: '4-6 minutes',\n                costEstimate: '$0.40-0.80',\n                success_rate: '98%'\n            },\n            {\n                name: 'SEO-Focused Content',\n                agents: 5,\n                tasks: 7,\n                estimatedTime: '6-8 minutes',\n                costEstimate: '$0.60-1.00',\n                success_rate: '96%'\n            },\n            {\n                name: 'Technical Content',\n                agents: 5,\n                tasks: 6,\n                estimatedTime: '8-12 minutes',\n                costEstimate: '$0.80-1.20',\n                success_rate: '94%'\n            }\n        ]\n    });\nconst validateTeamConfiguration = ()=>({\n        isValid: true,\n        issues: []\n    });\nfunction InvincibleContentWriter() {\n    _s();\n    const [projects, setProjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentProject, setCurrentProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [newProjectTopic, setNewProjectTopic] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [targetKeywords, setTargetKeywords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [contentType, setContentType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('blog-post');\n    const [wordCount, setWordCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(2000);\n    const [isRunning, setIsRunning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [totalCost, setTotalCost] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [previewContent, setPreviewContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [teamAnalytics, setTeamAnalytics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(getTeamAnalytics());\n    const [workflowError, setWorkflowError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('command-center');\n    const [topic, setTopic] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [keywords, setKeywords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [targetLength, setTargetLength] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(2000);\n    // Enhanced UI states\n    const [systemStatus, setSystemStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('READY');\n    const [powerLevel, setPowerLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(100);\n    // Mock current task data\n    const getOverallProgress = ()=>{\n        if (!currentProject || !currentProject.steps || currentProject.steps.length === 0) return 0;\n        const completedSteps = currentProject.steps.filter((s)=>s.status === 'completed').length;\n        return completedSteps / currentProject.steps.length * 100;\n    };\n    const [currentTask] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        topic: (currentProject === null || currentProject === void 0 ? void 0 : currentProject.topic) || '',\n        status: (currentProject === null || currentProject === void 0 ? void 0 : currentProject.status) || 'pending',\n        progress: currentProject ? getOverallProgress() : 0\n    });\n    // Enhanced agents data with power levels\n    const [agents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 'Research Agent',\n            name: 'Research Agent',\n            status: 'idle',\n            progress: 0,\n            currentTask: '',\n            cost: 0,\n            powerLevel: 95,\n            specialAbility: 'Deep Web Intelligence'\n        },\n        {\n            id: 'SEO Strategist',\n            name: 'SEO Strategist',\n            status: 'idle',\n            progress: 0,\n            currentTask: '',\n            cost: 0,\n            powerLevel: 88,\n            specialAbility: 'Search Domination'\n        },\n        {\n            id: 'Content Architect',\n            name: 'Content Architect',\n            status: 'idle',\n            progress: 0,\n            currentTask: '',\n            cost: 0,\n            powerLevel: 92,\n            specialAbility: 'Structure Mastery'\n        },\n        {\n            id: 'Content Creator',\n            name: 'Content Creator',\n            status: 'idle',\n            progress: 0,\n            currentTask: '',\n            cost: 0,\n            powerLevel: 98,\n            specialAbility: 'Creative Genesis'\n        },\n        {\n            id: 'Quality Editor',\n            name: 'Quality Editor',\n            status: 'idle',\n            progress: 0,\n            currentTask: '',\n            cost: 0,\n            powerLevel: 90,\n            specialAbility: 'Perfection Protocol'\n        }\n    ]);\n    const agentIcons = {\n        'Research Agent': _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        'SEO Strategist': _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        'Content Architect': _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        'Content Creator': _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        'Quality Editor': _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    };\n    // Placeholder functions\n    const stopGeneration = ()=>{\n        setIsRunning(false);\n        setSystemStatus('STOPPED');\n    };\n    const resetWorkflow = ()=>{\n        setIsRunning(false);\n        setCurrentProject(null);\n        setTotalCost(0);\n        setPreviewContent('');\n        setWorkflowError(null);\n        setSystemStatus('READY');\n        setPowerLevel(100);\n    };\n    const startContentGeneration = async ()=>{\n        if (!topic.trim()) {\n            alert('Please enter a topic first');\n            return;\n        }\n        setSystemStatus('INITIALIZING');\n        setPowerLevel(95);\n        // Create a new project if none exists\n        if (!currentProject) {\n            const project = {\n                id: Date.now().toString(),\n                topic: topic,\n                targetKeywords: keywords.split(',').map((k)=>k.trim()).filter(Boolean),\n                contentType,\n                wordCount: targetLength,\n                status: 'draft',\n                createdAt: new Date(),\n                steps: [\n                    {\n                        id: '1',\n                        name: 'Research & Data Collection',\n                        status: 'pending',\n                        progress: 0,\n                        agent: 'Research Agent'\n                    },\n                    {\n                        id: '2',\n                        name: 'SEO Strategy & Keywords',\n                        status: 'pending',\n                        progress: 0,\n                        agent: 'SEO Strategist'\n                    },\n                    {\n                        id: '3',\n                        name: 'Content Architecture',\n                        status: 'pending',\n                        progress: 0,\n                        agent: 'Content Architect'\n                    },\n                    {\n                        id: '4',\n                        name: 'Content Creation',\n                        status: 'pending',\n                        progress: 0,\n                        agent: 'Content Creator'\n                    },\n                    {\n                        id: '5',\n                        name: 'Quality Review & Optimization',\n                        status: 'pending',\n                        progress: 0,\n                        agent: 'Quality Editor'\n                    }\n                ]\n            };\n            setCurrentProject(project);\n            setProjects((prev)=>[\n                    project,\n                    ...prev\n                ]);\n        }\n        setIsRunning(true);\n        setSystemStatus('ACTIVE');\n        setWorkflowError(null);\n        try {\n            const validation = validateTeamConfiguration();\n            if (!validation.isValid) {\n                throw new Error(\"Configuration errors: \".concat(validation.issues.join(', ')));\n            }\n            const updatedProject = {\n                ...currentProject,\n                status: 'in_progress'\n            };\n            setCurrentProject(updatedProject);\n            // Simulate realistic workflow execution with enhanced UI updates\n            for(let i = 0; i < updatedProject.steps.length; i++){\n                const step = updatedProject.steps[i];\n                step.status = 'running';\n                setSystemStatus(\"EXECUTING: \".concat(step.agent.toUpperCase()));\n                setCurrentProject({\n                    ...updatedProject\n                });\n                const executionTimes = {\n                    'Research Agent': 45000,\n                    'SEO Strategist': 30000,\n                    'Content Architect': 25000,\n                    'Content Creator': 120000,\n                    'Quality Editor': 40000\n                };\n                const stepTime = executionTimes[step.agent] || 30000;\n                const progressSteps = 20;\n                const intervalTime = stepTime / progressSteps;\n                for(let progress = 0; progress <= 100; progress += 5){\n                    step.progress = progress;\n                    setPowerLevel(Math.max(70, 100 - progress * 0.3));\n                    setCurrentProject({\n                        ...updatedProject\n                    });\n                    await new Promise((resolve)=>setTimeout(resolve, intervalTime / 20));\n                }\n                step.status = 'completed';\n                const costs = {\n                    'Research Agent': 0.08,\n                    'SEO Strategist': 0.05,\n                    'Content Architect': 0.04,\n                    'Content Creator': 0.25,\n                    'Quality Editor': 0.06\n                };\n                step.cost = costs[step.agent] || 0.05;\n                step.duration = stepTime / 1000;\n                if (step.agent === 'Content Creator') {\n                    step.output = generateSampleContent(updatedProject.topic);\n                    setPreviewContent(step.output);\n                }\n                setTotalCost((prev)=>prev + (step.cost || 0));\n                setPowerLevel(100);\n            }\n            updatedProject.status = 'completed';\n            setCurrentProject(updatedProject);\n            setProjects((prev)=>prev.map((p)=>p.id === updatedProject.id ? updatedProject : p));\n            setSystemStatus('COMPLETED');\n        } catch (error) {\n            console.error('Workflow execution failed:', error);\n            setWorkflowError(error instanceof Error ? error.message : 'Unknown error occurred');\n            setSystemStatus('ERROR');\n        } finally{\n            setIsRunning(false);\n        }\n    };\n    const generateSampleContent = (topic)=>{\n        return \"# \".concat(topic, \"\\n\\n## Introduction\\n\\nThis comprehensive guide explores \").concat(topic, \" and provides valuable insights for readers seeking to understand this important subject.\\n\\n## Key Points\\n\\n- **Research-backed insights**: Our analysis is based on the latest industry research and expert opinions\\n- **Practical applications**: Real-world examples and actionable strategies\\n- **Future trends**: What to expect in the evolving landscape\\n\\n## Detailed Analysis\\n\\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.\\n\\n### Section 1: Understanding the Basics\\n\\nDetailed explanation of fundamental concepts and principles.\\n\\n### Section 2: Advanced Strategies  \\n\\nMore sophisticated approaches and methodologies.\\n\\n## Conclusion\\n\\nThis content has been optimized for search engines while maintaining high readability and engagement for human readers.\\n\\n*Generated by Invincible AI Content Team - Powered by KaibanJS & Kimi K2*\");\n    };\n    const exportContent = (format)=>{\n        if (!previewContent) {\n            alert('No content to export. Please generate content first.');\n            return;\n        }\n        let content = '';\n        let filename = '';\n        let mimeType = '';\n        switch(format){\n            case 'md':\n                content = previewContent;\n                filename = \"\".concat((topic === null || topic === void 0 ? void 0 : topic.replace(/\\s+/g, '-').toLowerCase()) || 'content', \".md\");\n                mimeType = 'text/markdown';\n                break;\n            case 'html':\n                content = '<!DOCTYPE html>\\n<html lang=\"en\">\\n<head>\\n    <meta charset=\"UTF-8\">\\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\\n    <title>'.concat(topic || 'Generated Content', \"</title>\\n    <style>\\n        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; max-width: 800px; margin: 0 auto; padding: 20px; }\\n        h1, h2, h3 { color: #333; }\\n        p { margin-bottom: 16px; }\\n        ul, ol { margin-bottom: 16px; padding-left: 20px; }\\n    </style>\\n</head>\\n<body>\\n    \").concat(previewContent.replace(/\\n/g, '<br>'), \"\\n</body>\\n</html>\");\n                filename = \"\".concat((topic === null || topic === void 0 ? void 0 : topic.replace(/\\s+/g, '-').toLowerCase()) || 'content', \".html\");\n                mimeType = 'text/html';\n                break;\n            case 'txt':\n                content = previewContent.replace(/[#*`]/g, '').replace(/\\n+/g, '\\n');\n                filename = \"\".concat((topic === null || topic === void 0 ? void 0 : topic.replace(/\\s+/g, '-').toLowerCase()) || 'content', \".txt\");\n                mimeType = 'text/plain';\n                break;\n        }\n        const blob = new Blob([\n            content\n        ], {\n            type: mimeType\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = filename;\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n    };\n    const copyToClipboard = async ()=>{\n        if (!previewContent) return;\n        try {\n            await navigator.clipboard.writeText(previewContent);\n            alert('Content copied to clipboard!');\n        } catch (err) {\n            console.error('Failed to copy content:', err);\n            alert('Failed to copy content to clipboard');\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-blue-500/30 bg-slate-900/80 backdrop-blur-lg sticky top-0 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-6 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-10 w-10 text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 445,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 446,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl font-bold bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent\",\n                                                children: \"INVINCIBLE AI WRITER\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 449,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-slate-400\",\n                                                children: \"Multi-Agent Content Generation System\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 452,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                lineNumber: 443,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium text-blue-400\",\n                                                children: \"SYSTEM STATUS\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-slate-300\",\n                                                children: systemStatus\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative w-16 h-16\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 rounded-full bg-gradient-to-r from-blue-500 to-cyan-500 animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-1 rounded-full bg-slate-900 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-bold text-cyan-400\",\n                                                    children: [\n                                                        powerLevel,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 462,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                lineNumber: 457,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                        lineNumber: 442,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                    lineNumber: 441,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                lineNumber: 440,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-6 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-1 bg-slate-800/50 p-2 rounded-xl mb-8 backdrop-blur-sm\",\n                        children: [\n                            {\n                                id: 'command-center',\n                                label: 'Command Center',\n                                icon: _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n                            },\n                            {\n                                id: 'mission-config',\n                                label: 'Mission Config',\n                                icon: _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                            },\n                            {\n                                id: 'content-preview',\n                                label: 'Content Preview',\n                                icon: _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                            },\n                            {\n                                id: 'analytics',\n                                label: 'Analytics',\n                                icon: _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                            }\n                        ].map((param)=>{\n                            let { id, label, icon: Icon } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(id),\n                                className: \"flex items-center space-x-2 px-4 py-3 rounded-lg font-medium transition-all duration-200 \".concat(activeTab === id ? 'bg-gradient-to-r from-blue-500 to-cyan-500 text-white shadow-lg' : 'text-slate-400 hover:text-white hover:bg-slate-700/50'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: label\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 492,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, id, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                lineNumber: 482,\n                                columnNumber: 13\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                        lineNumber: 475,\n                        columnNumber: 9\n                    }, this),\n                    activeTab === 'command-center' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-slate-800/50 to-slate-900/50 rounded-2xl border border-blue-500/20 backdrop-blur-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 border-b border-slate-700/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-6 w-6 text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 504,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl font-bold\",\n                                                        children: \"Mission Control\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 505,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 503,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: startContentGeneration,\n                                                        disabled: isRunning || !topic.trim(),\n                                                        className: \"flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 disabled:from-slate-600 disabled:to-slate-700 disabled:cursor-not-allowed rounded-lg font-medium transition-all duration-200 shadow-lg hover:shadow-xl\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 513,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: isRunning ? 'EXECUTING...' : 'INITIATE MISSION'\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 514,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 508,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: stopGeneration,\n                                                        disabled: !isRunning,\n                                                        className: \"flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-red-500 to-rose-500 hover:from-red-600 hover:to-rose-600 disabled:from-slate-600 disabled:to-slate-700 disabled:cursor-not-allowed rounded-lg font-medium transition-all duration-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 521,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"ABORT\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 522,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 516,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: resetWorkflow,\n                                                        className: \"flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 rounded-lg font-medium transition-all duration-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 528,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"RESET\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 529,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 524,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 507,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 502,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg font-medium\",\n                                                        children: topic || 'No active mission'\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 537,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"px-3 py-1 rounded-full text-sm font-medium \".concat(currentTask.status === 'completed' ? 'bg-green-500/20 text-green-400' : currentTask.status === 'in_progress' ? 'bg-blue-500/20 text-blue-400' : 'bg-slate-500/20 text-slate-400'),\n                                                        children: currentTask.status.toUpperCase()\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 540,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 536,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative h-3 bg-slate-700 rounded-full overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-y-0 left-0 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full transition-all duration-500\",\n                                                        style: {\n                                                            width: \"\".concat(currentTask.progress, \"%\")\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 550,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 554,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 549,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-sm text-slate-400 mt-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            Math.round(currentTask.progress),\n                                                            \"% Complete\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 558,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Mission Cost: $\",\n                                                            totalCost.toFixed(3)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 559,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 557,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 535,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                lineNumber: 501,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-6 w-6 text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 567,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-bold\",\n                                                children: \"Agent Squadron Status\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 568,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 566,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                        children: agents.map((agent)=>{\n                                            const Icon = agentIcons[agent.id];\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-br from-slate-800/50 to-slate-900/50 rounded-xl border border-slate-700/50 p-6 backdrop-blur-sm hover:border-blue-500/30 transition-all duration-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3 mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 rounded-lg \".concat(agent.status === 'running' ? 'bg-blue-500/20 text-blue-400' : agent.status === 'completed' ? 'bg-green-500/20 text-green-400' : agent.status === 'error' ? 'bg-red-500/20 text-red-400' : 'bg-slate-500/20 text-slate-400'),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                    className: \"h-6 w-6\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 583,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 577,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-bold text-sm\",\n                                                                        children: agent.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                        lineNumber: 586,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-slate-400\",\n                                                                        children: agent.specialAbility\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                        lineNumber: 587,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 585,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 576,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between text-xs text-slate-400 mb-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Power Level\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                        lineNumber: 594,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            agent.powerLevel,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                        lineNumber: 595,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 593,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-2 bg-slate-700 rounded-full overflow-hidden\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-full rounded-full transition-all duration-500 \".concat(agent.powerLevel >= 95 ? 'bg-gradient-to-r from-green-400 to-emerald-400' : agent.powerLevel >= 85 ? 'bg-gradient-to-r from-blue-400 to-cyan-400' : 'bg-gradient-to-r from-yellow-400 to-orange-400'),\n                                                                    style: {\n                                                                        width: \"\".concat(agent.powerLevel, \"%\")\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 598,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 597,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 592,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-1 bg-slate-700 rounded-full overflow-hidden mb-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-full rounded-full transition-all duration-300 \".concat(agent.status === 'completed' ? 'bg-green-400' : agent.status === 'running' ? 'bg-blue-400 animate-pulse' : 'bg-slate-600'),\n                                                            style: {\n                                                                width: \"\".concat(agent.progress, \"%\")\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 611,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 610,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-xs\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 rounded-full \".concat(agent.status === 'running' ? 'bg-blue-500/20 text-blue-400' : agent.status === 'completed' ? 'bg-green-500/20 text-green-400' : agent.status === 'error' ? 'bg-red-500/20 text-red-400' : 'bg-slate-500/20 text-slate-400'),\n                                                                children: agent.status.toUpperCase()\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 622,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            agent.cost > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-slate-400\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    agent.cost.toFixed(3)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 630,\n                                                                columnNumber: 44\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 621,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, agent.id, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 575,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 571,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                lineNumber: 565,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                        lineNumber: 499,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'mission-config' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-br from-slate-800/50 to-slate-900/50 rounded-2xl border border-blue-500/20 backdrop-blur-sm p-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-6 w-6 text-blue-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                            lineNumber: 645,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-bold\",\n                                            children: \"Mission Configuration\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                            lineNumber: 646,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                    lineNumber: 644,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-slate-300 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                    className: \"h-4 w-4 inline mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 653,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Primary Target *\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 652,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: topic,\n                                                            onChange: (e)=>setTopic(e.target.value),\n                                                            placeholder: \"Enter your content mission objective...\",\n                                                            className: \"w-full px-4 py-3 bg-slate-800/50 border border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 656,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                    lineNumber: 651,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-slate-300 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"h-4 w-4 inline mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 667,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"SEO Keywords\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 666,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: keywords,\n                                                            onChange: (e)=>setKeywords(e.target.value),\n                                                            placeholder: \"keyword1, keyword2, keyword3...\",\n                                                            className: \"w-full px-4 py-3 bg-slate-800/50 border border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 670,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                    lineNumber: 665,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                            lineNumber: 650,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-slate-300 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    className: \"h-4 w-4 inline mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 683,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Mission Type\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 682,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: contentType,\n                                                            onChange: (e)=>setContentType(e.target.value),\n                                                            className: \"w-full px-4 py-3 bg-slate-800/50 border border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"blog-post\",\n                                                                    children: \"Blog Post\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 691,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"article\",\n                                                                    children: \"Article\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 692,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"landing-page\",\n                                                                    children: \"Landing Page\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 693,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"product-description\",\n                                                                    children: \"Product Description\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 694,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"social-media\",\n                                                                    children: \"Social Media Series\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 695,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 686,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                    lineNumber: 681,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-slate-300 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-4 w-4 inline mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 701,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Target Scope: \",\n                                                                targetLength,\n                                                                \" words\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 700,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"range\",\n                                                            min: \"500\",\n                                                            max: \"5000\",\n                                                            step: \"100\",\n                                                            value: targetLength,\n                                                            onChange: (e)=>setTargetLength(parseInt(e.target.value)),\n                                                            className: \"w-full h-2 bg-slate-700 rounded-lg appearance-none cursor-pointer slider\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 704,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-xs text-slate-400 mt-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 714,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"5000\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 715,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 713,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                    lineNumber: 699,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                            lineNumber: 680,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                    lineNumber: 649,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                            lineNumber: 643,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                        lineNumber: 642,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'content-preview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-br from-slate-800/50 to-slate-900/50 rounded-2xl border border-blue-500/20 backdrop-blur-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border-b border-slate-700/50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-6 w-6 text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 731,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl font-bold\",\n                                                        children: \"Generated Content\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 732,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 730,\n                                                columnNumber: 19\n                                            }, this),\n                                            previewContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>exportContent('md'),\n                                                        className: \"px-4 py-2 bg-slate-700 hover:bg-slate-600 border border-slate-600 rounded-lg text-sm font-medium transition-colors flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 740,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"MD\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 741,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 736,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>exportContent('html'),\n                                                        className: \"px-4 py-2 bg-slate-700 hover:bg-slate-600 border border-slate-600 rounded-lg text-sm font-medium transition-colors flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 747,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"HTML\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 748,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 743,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: copyToClipboard,\n                                                        className: \"px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg text-sm font-medium transition-colors\",\n                                                        children: \"Copy\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 750,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 735,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 729,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                    lineNumber: 728,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-slate-900/50 rounded-xl border border-slate-700/50 p-6 min-h-[400px]\",\n                                        children: previewContent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"prose prose-invert prose-blue max-w-none\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_22__.Markdown, {\n                                                children: previewContent\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 765,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                            lineNumber: 764,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center h-full text-slate-500\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-16 w-16 mx-auto mb-4 opacity-50\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 770,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg\",\n                                                        children: \"Content will materialize here after mission execution\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 771,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm mt-2\",\n                                                        children: \"Configure your mission and initiate the content generation process\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 772,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 769,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                            lineNumber: 768,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 762,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                    lineNumber: 761,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                            lineNumber: 727,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                        lineNumber: 726,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'analytics' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            workflowError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-500/10 border border-red-500/30 rounded-xl p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center text-red-400\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"h-5 w-5 mr-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                            lineNumber: 789,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: \"Mission Error: \"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                            lineNumber: 790,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2\",\n                                            children: workflowError\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                            lineNumber: 791,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                    lineNumber: 788,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                lineNumber: 787,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                className: \"h-6 w-6 text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 799,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-bold\",\n                                                children: \"Performance Metrics\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 800,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 798,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-br from-green-500/10 to-emerald-500/10 border border-green-500/20 rounded-xl p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                            className: \"h-10 w-10 text-green-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 806,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-slate-400\",\n                                                                    children: \"Mission Cost\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 808,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-2xl font-bold text-green-400\",\n                                                                    children: [\n                                                                        \"$\",\n                                                                        totalCost.toFixed(3)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 809,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-green-500\",\n                                                                    children: \"85-95% savings\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 810,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 807,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                    lineNumber: 805,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 804,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-br from-blue-500/10 to-cyan-500/10 border border-blue-500/20 rounded-xl p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                            className: \"h-10 w-10 text-blue-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 817,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-slate-400\",\n                                                                    children: \"Avg. Generation\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 819,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-2xl font-bold text-blue-400\",\n                                                                    children: \"4-6m\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 820,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-blue-500\",\n                                                                    children: \"Per mission\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 821,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 818,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                    lineNumber: 816,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 815,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-br from-purple-500/10 to-pink-500/10 border border-purple-500/20 rounded-xl p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-10 w-10 text-purple-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 828,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-slate-400\",\n                                                                    children: \"Quality Score\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 830,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-2xl font-bold text-purple-400\",\n                                                                    children: \"9.2/10\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 831,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-purple-500\",\n                                                                    children: \"EQ-Bench rating\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 832,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 829,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                    lineNumber: 827,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 826,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-br from-cyan-500/10 to-teal-500/10 border border-cyan-500/20 rounded-xl p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"h-10 w-10 text-cyan-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 839,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-slate-400\",\n                                                                    children: \"Success Rate\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 841,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-2xl font-bold text-cyan-400\",\n                                                                    children: \"98%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 842,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-cyan-500\",\n                                                                    children: \"Mission completion\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 843,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 840,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                    lineNumber: 838,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 837,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 803,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                lineNumber: 797,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-slate-800/50 to-slate-900/50 rounded-2xl border border-blue-500/20 backdrop-blur-sm p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold mb-6\",\n                                        children: \"Agent Squadron Analytics\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 852,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: teamAnalytics.agents.map((agent, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-6 bg-slate-800/30 border border-slate-700/50 rounded-xl\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-3 h-3 rounded-full bg-green-400 animate-pulse\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 857,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-bold text-white\",\n                                                                        children: agent.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                        lineNumber: 859,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-slate-400\",\n                                                                        children: agent.specialization\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                        lineNumber: 860,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-slate-500\",\n                                                                        children: [\n                                                                            \"Tools: \",\n                                                                            agent.tools.join(', ')\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                        lineNumber: 861,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 858,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 856,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium text-blue-400\",\n                                                                children: agent.performance\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 865,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-slate-400\",\n                                                                children: agent.avgExecutionTime\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 866,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mt-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-slate-500\",\n                                                                        children: \"Power:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                        lineNumber: 868,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs font-bold text-green-400\",\n                                                                        children: [\n                                                                            agent.powerLevel,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                        lineNumber: 869,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 867,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 864,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 855,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 853,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                lineNumber: 851,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                        lineNumber: 784,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                lineNumber: 473,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n        lineNumber: 438,\n        columnNumber: 5\n    }, this);\n}\n_s(InvincibleContentWriter, \"bvADBM56VRRngwAvyny6/Xt5prg=\");\n_c = InvincibleContentWriter;\nvar _c;\n$RefreshReg$(_c, \"InvincibleContentWriter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvY29udGVudC13cml0ZXIvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUVtRDtBQTBCN0I7QUFDcUI7QUF3QjNDLGdEQUFnRDtBQUNoRCxNQUFNMkIsbUJBQW1CLElBQU87UUFDOUJDLFFBQVE7WUFDTjtnQkFDRUMsTUFBTTtnQkFDTkMsZ0JBQWdCO2dCQUNoQkMsT0FBTztvQkFBQztvQkFBaUI7aUJBQWdCO2dCQUN6Q0MsYUFBYTtnQkFDYkMsa0JBQWtCO2dCQUNsQkMsWUFBWTtnQkFDWkMsZ0JBQWdCO1lBQ2xCO1lBQ0E7Z0JBQ0VOLE1BQU07Z0JBQ05DLGdCQUFnQjtnQkFDaEJDLE9BQU87b0JBQUM7b0JBQW9CO2lCQUF1QjtnQkFDbkRDLGFBQWE7Z0JBQ2JDLGtCQUFrQjtnQkFDbEJDLFlBQVk7Z0JBQ1pDLGdCQUFnQjtZQUNsQjtZQUNBO2dCQUNFTixNQUFNO2dCQUNOQyxnQkFBZ0I7Z0JBQ2hCQyxPQUFPO29CQUFDO29CQUFxQjtpQkFBa0I7Z0JBQy9DQyxhQUFhO2dCQUNiQyxrQkFBa0I7Z0JBQ2xCQyxZQUFZO2dCQUNaQyxnQkFBZ0I7WUFDbEI7WUFDQTtnQkFDRU4sTUFBTTtnQkFDTkMsZ0JBQWdCO2dCQUNoQkMsT0FBTztvQkFBQztvQkFBaUI7aUJBQW9CO2dCQUM3Q0MsYUFBYTtnQkFDYkMsa0JBQWtCO2dCQUNsQkMsWUFBWTtnQkFDWkMsZ0JBQWdCO1lBQ2xCO1lBQ0E7Z0JBQ0VOLE1BQU07Z0JBQ05DLGdCQUFnQjtnQkFDaEJDLE9BQU87b0JBQUM7b0JBQWlCO2lCQUFtQjtnQkFDNUNDLGFBQWE7Z0JBQ2JDLGtCQUFrQjtnQkFDbEJDLFlBQVk7Z0JBQ1pDLGdCQUFnQjtZQUNsQjtTQUNEO1FBQ0RDLGNBQWM7WUFDWkMsZUFBZTtnQkFBQztnQkFBYztnQkFBWTtnQkFBaUI7Z0JBQWdCO2dCQUFtQjthQUF1QjtZQUNySEMsa0JBQWtCO1lBQ2xCQyxrQkFBa0I7WUFDbEJDLGVBQWU7WUFDZkMsYUFBYTtRQUNmO1FBQ0FDLFdBQVc7WUFDVDtnQkFBRWIsTUFBTTtnQkFBNkJELFFBQVE7Z0JBQUdlLE9BQU87Z0JBQUdDLGVBQWU7Z0JBQWVDLGNBQWM7Z0JBQWNDLGNBQWM7WUFBTTtZQUN4STtnQkFBRWpCLE1BQU07Z0JBQXVCRCxRQUFRO2dCQUFHZSxPQUFPO2dCQUFHQyxlQUFlO2dCQUFlQyxjQUFjO2dCQUFjQyxjQUFjO1lBQU07WUFDbEk7Z0JBQUVqQixNQUFNO2dCQUFxQkQsUUFBUTtnQkFBR2UsT0FBTztnQkFBR0MsZUFBZTtnQkFBZ0JDLGNBQWM7Z0JBQWNDLGNBQWM7WUFBTTtTQUNsSTtJQUNIO0FBRUEsTUFBTUMsNEJBQTRCLElBQU87UUFBRUMsU0FBUztRQUFNQyxRQUFRLEVBQUU7SUFBQztBQUV0RCxTQUFTQzs7SUFDdEIsTUFBTSxDQUFDQyxVQUFVQyxZQUFZLEdBQUduRCwrQ0FBUUEsQ0FBbUIsRUFBRTtJQUM3RCxNQUFNLENBQUNvRCxnQkFBZ0JDLGtCQUFrQixHQUFHckQsK0NBQVFBLENBQXdCO0lBQzVFLE1BQU0sQ0FBQ3NELGlCQUFpQkMsbUJBQW1CLEdBQUd2RCwrQ0FBUUEsQ0FBQztJQUN2RCxNQUFNLENBQUN3RCxnQkFBZ0JDLGtCQUFrQixHQUFHekQsK0NBQVFBLENBQUM7SUFDckQsTUFBTSxDQUFDMEQsYUFBYUMsZUFBZSxHQUFHM0QsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDNEQsV0FBV0MsYUFBYSxHQUFHN0QsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDOEQsV0FBV0MsYUFBYSxHQUFHL0QsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDZ0UsV0FBV0MsYUFBYSxHQUFHakUsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDa0UsZ0JBQWdCQyxrQkFBa0IsR0FBR25FLCtDQUFRQSxDQUFDO0lBQ3JELE1BQU0sQ0FBQ29FLGVBQWVDLGlCQUFpQixHQUFHckUsK0NBQVFBLENBQUMwQjtJQUNuRCxNQUFNLENBQUM0QyxlQUFlQyxpQkFBaUIsR0FBR3ZFLCtDQUFRQSxDQUFnQjtJQUNsRSxNQUFNLENBQUN3RSxXQUFXQyxhQUFhLEdBQUd6RSwrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUMwRSxPQUFPQyxTQUFTLEdBQUczRSwrQ0FBUUEsQ0FBQztJQUNuQyxNQUFNLENBQUM0RSxVQUFVQyxZQUFZLEdBQUc3RSwrQ0FBUUEsQ0FBQztJQUN6QyxNQUFNLENBQUM4RSxjQUFjQyxnQkFBZ0IsR0FBRy9FLCtDQUFRQSxDQUFDO0lBRWpELHFCQUFxQjtJQUNyQixNQUFNLENBQUNnRixjQUFjQyxnQkFBZ0IsR0FBR2pGLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQ2lDLFlBQVlpRCxjQUFjLEdBQUdsRiwrQ0FBUUEsQ0FBQztJQUU3Qyx5QkFBeUI7SUFDekIsTUFBTW1GLHFCQUFxQjtRQUN6QixJQUFJLENBQUMvQixrQkFBa0IsQ0FBQ0EsZUFBZWdDLEtBQUssSUFBSWhDLGVBQWVnQyxLQUFLLENBQUNDLE1BQU0sS0FBSyxHQUFHLE9BQU87UUFDMUYsTUFBTUMsaUJBQWlCbEMsZUFBZWdDLEtBQUssQ0FBQ0csTUFBTSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFQyxNQUFNLEtBQUssYUFBYUosTUFBTTtRQUN4RixPQUFPLGlCQUFrQmpDLGVBQWVnQyxLQUFLLENBQUNDLE1BQU0sR0FBSTtJQUMxRDtJQUVBLE1BQU0sQ0FBQ0ssWUFBWSxHQUFHMUYsK0NBQVFBLENBQUM7UUFDN0IwRSxPQUFPdEIsQ0FBQUEsMkJBQUFBLHFDQUFBQSxlQUFnQnNCLEtBQUssS0FBSTtRQUNoQ2UsUUFBUXJDLENBQUFBLDJCQUFBQSxxQ0FBQUEsZUFBZ0JxQyxNQUFNLEtBQUk7UUFDbENFLFVBQVV2QyxpQkFBaUIrQix1QkFBdUI7SUFDcEQ7SUFFQSx5Q0FBeUM7SUFDekMsTUFBTSxDQUFDeEQsT0FBTyxHQUFHM0IsK0NBQVFBLENBQUM7UUFDeEI7WUFDRTRGLElBQUk7WUFDSmhFLE1BQU07WUFDTjZELFFBQVE7WUFDUkUsVUFBVTtZQUNWRCxhQUFhO1lBQ2JHLE1BQU07WUFDTjVELFlBQVk7WUFDWkMsZ0JBQWdCO1FBQ2xCO1FBQ0E7WUFDRTBELElBQUk7WUFDSmhFLE1BQU07WUFDTjZELFFBQVE7WUFDUkUsVUFBVTtZQUNWRCxhQUFhO1lBQ2JHLE1BQU07WUFDTjVELFlBQVk7WUFDWkMsZ0JBQWdCO1FBQ2xCO1FBQ0E7WUFDRTBELElBQUk7WUFDSmhFLE1BQU07WUFDTjZELFFBQVE7WUFDUkUsVUFBVTtZQUNWRCxhQUFhO1lBQ2JHLE1BQU07WUFDTjVELFlBQVk7WUFDWkMsZ0JBQWdCO1FBQ2xCO1FBQ0E7WUFDRTBELElBQUk7WUFDSmhFLE1BQU07WUFDTjZELFFBQVE7WUFDUkUsVUFBVTtZQUNWRCxhQUFhO1lBQ2JHLE1BQU07WUFDTjVELFlBQVk7WUFDWkMsZ0JBQWdCO1FBQ2xCO1FBQ0E7WUFDRTBELElBQUk7WUFDSmhFLE1BQU07WUFDTjZELFFBQVE7WUFDUkUsVUFBVTtZQUNWRCxhQUFhO1lBQ2JHLE1BQU07WUFDTjVELFlBQVk7WUFDWkMsZ0JBQWdCO1FBQ2xCO0tBQ0Q7SUFFRCxNQUFNNEQsYUFBa0M7UUFDdEMsa0JBQWtCdEYsaVFBQU1BO1FBQ3hCLGtCQUFrQkMsaVFBQU1BO1FBQ3hCLHFCQUFxQkYsaVFBQVFBO1FBQzdCLG1CQUFtQkcsaVFBQU9BO1FBQzFCLGtCQUFrQkMsaVFBQVdBO0lBQy9CO0lBRUEsd0JBQXdCO0lBQ3hCLE1BQU1vRixpQkFBaUI7UUFDckJoQyxhQUFhO1FBQ2JrQixnQkFBZ0I7SUFDbEI7SUFFQSxNQUFNZSxnQkFBZ0I7UUFDcEJqQyxhQUFhO1FBQ2JWLGtCQUFrQjtRQUNsQlksYUFBYTtRQUNiRSxrQkFBa0I7UUFDbEJJLGlCQUFpQjtRQUNqQlUsZ0JBQWdCO1FBQ2hCQyxjQUFjO0lBQ2hCO0lBRUEsTUFBTWUseUJBQXlCO1FBQzdCLElBQUksQ0FBQ3ZCLE1BQU13QixJQUFJLElBQUk7WUFDakJDLE1BQU07WUFDTjtRQUNGO1FBRUFsQixnQkFBZ0I7UUFDaEJDLGNBQWM7UUFFZCxzQ0FBc0M7UUFDdEMsSUFBSSxDQUFDOUIsZ0JBQWdCO1lBQ25CLE1BQU1nRCxVQUEwQjtnQkFDOUJSLElBQUlTLEtBQUtDLEdBQUcsR0FBR0MsUUFBUTtnQkFDdkI3QixPQUFPQTtnQkFDUGxCLGdCQUFnQm9CLFNBQVM0QixLQUFLLENBQUMsS0FBS0MsR0FBRyxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFUixJQUFJLElBQUlYLE1BQU0sQ0FBQ29CO2dCQUM5RGpEO2dCQUNBRSxXQUFXa0I7Z0JBQ1hXLFFBQVE7Z0JBQ1JtQixXQUFXLElBQUlQO2dCQUNmakIsT0FBTztvQkFDTDt3QkFBRVEsSUFBSTt3QkFBS2hFLE1BQU07d0JBQThCNkQsUUFBUTt3QkFBV0UsVUFBVTt3QkFBR2tCLE9BQU87b0JBQWlCO29CQUN2Rzt3QkFBRWpCLElBQUk7d0JBQUtoRSxNQUFNO3dCQUEyQjZELFFBQVE7d0JBQVdFLFVBQVU7d0JBQUdrQixPQUFPO29CQUFpQjtvQkFDcEc7d0JBQUVqQixJQUFJO3dCQUFLaEUsTUFBTTt3QkFBd0I2RCxRQUFRO3dCQUFXRSxVQUFVO3dCQUFHa0IsT0FBTztvQkFBb0I7b0JBQ3BHO3dCQUFFakIsSUFBSTt3QkFBS2hFLE1BQU07d0JBQW9CNkQsUUFBUTt3QkFBV0UsVUFBVTt3QkFBR2tCLE9BQU87b0JBQWtCO29CQUM5Rjt3QkFBRWpCLElBQUk7d0JBQUtoRSxNQUFNO3dCQUFpQzZELFFBQVE7d0JBQVdFLFVBQVU7d0JBQUdrQixPQUFPO29CQUFpQjtpQkFDM0c7WUFDSDtZQUNBeEQsa0JBQWtCK0M7WUFDbEJqRCxZQUFZMkQsQ0FBQUEsT0FBUTtvQkFBQ1Y7dUJBQVlVO2lCQUFLO1FBQ3hDO1FBRUEvQyxhQUFhO1FBQ2JrQixnQkFBZ0I7UUFDaEJWLGlCQUFpQjtRQUVqQixJQUFJO1lBQ0YsTUFBTXdDLGFBQWFqRTtZQUNuQixJQUFJLENBQUNpRSxXQUFXaEUsT0FBTyxFQUFFO2dCQUN2QixNQUFNLElBQUlpRSxNQUFNLHlCQUFzRCxPQUE3QkQsV0FBVy9ELE1BQU0sQ0FBQ2lFLElBQUksQ0FBQztZQUNsRTtZQUVBLE1BQU1DLGlCQUFpQjtnQkFBRSxHQUFHOUQsY0FBYztnQkFBR3FDLFFBQVE7WUFBdUI7WUFDNUVwQyxrQkFBa0I2RDtZQUVsQixpRUFBaUU7WUFDakUsSUFBSyxJQUFJQyxJQUFJLEdBQUdBLElBQUlELGVBQWU5QixLQUFLLENBQUNDLE1BQU0sRUFBRThCLElBQUs7Z0JBQ3BELE1BQU1DLE9BQU9GLGVBQWU5QixLQUFLLENBQUMrQixFQUFFO2dCQUNwQ0MsS0FBSzNCLE1BQU0sR0FBRztnQkFDZFIsZ0JBQWdCLGNBQXVDLE9BQXpCbUMsS0FBS1AsS0FBSyxDQUFDUSxXQUFXO2dCQUVwRGhFLGtCQUFrQjtvQkFBRSxHQUFHNkQsY0FBYztnQkFBQztnQkFFdEMsTUFBTUksaUJBQWlCO29CQUNyQixrQkFBa0I7b0JBQ2xCLGtCQUFrQjtvQkFDbEIscUJBQXFCO29CQUNyQixtQkFBbUI7b0JBQ25CLGtCQUFrQjtnQkFDcEI7Z0JBRUEsTUFBTUMsV0FBV0QsY0FBYyxDQUFDRixLQUFLUCxLQUFLLENBQWdDLElBQUk7Z0JBQzlFLE1BQU1XLGdCQUFnQjtnQkFDdEIsTUFBTUMsZUFBZUYsV0FBV0M7Z0JBRWhDLElBQUssSUFBSTdCLFdBQVcsR0FBR0EsWUFBWSxLQUFLQSxZQUFZLEVBQUc7b0JBQ3JEeUIsS0FBS3pCLFFBQVEsR0FBR0E7b0JBQ2hCVCxjQUFjd0MsS0FBS0MsR0FBRyxDQUFDLElBQUksTUFBT2hDLFdBQVc7b0JBQzdDdEMsa0JBQWtCO3dCQUFFLEdBQUc2RCxjQUFjO29CQUFDO29CQUN0QyxNQUFNLElBQUlVLFFBQVFDLENBQUFBLFVBQVdDLFdBQVdELFNBQVNKLGVBQWU7Z0JBQ2xFO2dCQUVBTCxLQUFLM0IsTUFBTSxHQUFHO2dCQUVkLE1BQU1zQyxRQUFRO29CQUNaLGtCQUFrQjtvQkFDbEIsa0JBQWtCO29CQUNsQixxQkFBcUI7b0JBQ3JCLG1CQUFtQjtvQkFDbkIsa0JBQWtCO2dCQUNwQjtnQkFFQVgsS0FBS3ZCLElBQUksR0FBR2tDLEtBQUssQ0FBQ1gsS0FBS1AsS0FBSyxDQUF1QixJQUFJO2dCQUN2RE8sS0FBS1ksUUFBUSxHQUFHVCxXQUFXO2dCQUUzQixJQUFJSCxLQUFLUCxLQUFLLEtBQUssbUJBQW1CO29CQUNwQ08sS0FBS2EsTUFBTSxHQUFHQyxzQkFBc0JoQixlQUFleEMsS0FBSztvQkFDeERQLGtCQUFrQmlELEtBQUthLE1BQU07Z0JBQy9CO2dCQUVBaEUsYUFBYTZDLENBQUFBLE9BQVFBLE9BQVFNLENBQUFBLEtBQUt2QixJQUFJLElBQUk7Z0JBQzFDWCxjQUFjO1lBQ2hCO1lBRUFnQyxlQUFlekIsTUFBTSxHQUFHO1lBQ3hCcEMsa0JBQWtCNkQ7WUFDbEIvRCxZQUFZMkQsQ0FBQUEsT0FBUUEsS0FBS0wsR0FBRyxDQUFDMEIsQ0FBQUEsSUFBS0EsRUFBRXZDLEVBQUUsS0FBS3NCLGVBQWV0QixFQUFFLEdBQUdzQixpQkFBaUJpQjtZQUNoRmxELGdCQUFnQjtRQUVsQixFQUFFLE9BQU9tRCxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyw4QkFBOEJBO1lBQzVDN0QsaUJBQWlCNkQsaUJBQWlCcEIsUUFBUW9CLE1BQU1FLE9BQU8sR0FBRztZQUMxRHJELGdCQUFnQjtRQUNsQixTQUFVO1lBQ1JsQixhQUFhO1FBQ2Y7SUFDRjtJQUVBLE1BQU1tRSx3QkFBd0IsQ0FBQ3hEO1FBQzdCLE9BQU8sS0FJeUJBLE9BSnBCQSxPQUFNLDZEQUlvQixPQUFOQSxPQUFNO0lBeUJ4QztJQUVBLE1BQU02RCxnQkFBZ0IsQ0FBQ0M7UUFDckIsSUFBSSxDQUFDdEUsZ0JBQWdCO1lBQ25CaUMsTUFBTTtZQUNOO1FBQ0Y7UUFFQSxJQUFJc0MsVUFBVTtRQUNkLElBQUlDLFdBQVc7UUFDZixJQUFJQyxXQUFXO1FBRWYsT0FBUUg7WUFDTixLQUFLO2dCQUNIQyxVQUFVdkU7Z0JBQ1Z3RSxXQUFXLEdBQTBELE9BQXZEaEUsQ0FBQUEsa0JBQUFBLDRCQUFBQSxNQUFPa0UsT0FBTyxDQUFDLFFBQVEsS0FBS0MsV0FBVyxPQUFNLFdBQVU7Z0JBQ3JFRixXQUFXO2dCQUNYO1lBQ0YsS0FBSztnQkFDSEYsVUFBVSxpS0FjWnZFLE9BVE9RLFNBQVMscUJBQW9CLHlXQVNFLE9BQXRDUixlQUFlMEUsT0FBTyxDQUFDLE9BQU8sU0FBUTtnQkFHcENGLFdBQVcsR0FBMEQsT0FBdkRoRSxDQUFBQSxrQkFBQUEsNEJBQUFBLE1BQU9rRSxPQUFPLENBQUMsUUFBUSxLQUFLQyxXQUFXLE9BQU0sV0FBVTtnQkFDckVGLFdBQVc7Z0JBQ1g7WUFDRixLQUFLO2dCQUNIRixVQUFVdkUsZUFBZTBFLE9BQU8sQ0FBQyxVQUFVLElBQUlBLE9BQU8sQ0FBQyxRQUFRO2dCQUMvREYsV0FBVyxHQUEwRCxPQUF2RGhFLENBQUFBLGtCQUFBQSw0QkFBQUEsTUFBT2tFLE9BQU8sQ0FBQyxRQUFRLEtBQUtDLFdBQVcsT0FBTSxXQUFVO2dCQUNyRUYsV0FBVztnQkFDWDtRQUNKO1FBRUEsTUFBTUcsT0FBTyxJQUFJQyxLQUFLO1lBQUNOO1NBQVEsRUFBRTtZQUFFTyxNQUFNTDtRQUFTO1FBQ2xELE1BQU1NLE1BQU1DLElBQUlDLGVBQWUsQ0FBQ0w7UUFDaEMsTUFBTU0sSUFBSUMsU0FBU0MsYUFBYSxDQUFDO1FBQ2pDRixFQUFFRyxJQUFJLEdBQUdOO1FBQ1RHLEVBQUVJLFFBQVEsR0FBR2Q7UUFDYlcsU0FBU0ksSUFBSSxDQUFDQyxXQUFXLENBQUNOO1FBQzFCQSxFQUFFTyxLQUFLO1FBQ1BOLFNBQVNJLElBQUksQ0FBQ0csV0FBVyxDQUFDUjtRQUMxQkYsSUFBSVcsZUFBZSxDQUFDWjtJQUN0QjtJQUVBLE1BQU1hLGtCQUFrQjtRQUN0QixJQUFJLENBQUM1RixnQkFBZ0I7UUFFckIsSUFBSTtZQUNGLE1BQU02RixVQUFVQyxTQUFTLENBQUNDLFNBQVMsQ0FBQy9GO1lBQ3BDaUMsTUFBTTtRQUNSLEVBQUUsT0FBTytELEtBQUs7WUFDWjdCLFFBQVFELEtBQUssQ0FBQywyQkFBMkI4QjtZQUN6Qy9ELE1BQU07UUFDUjtJQUNGO0lBRUEscUJBQ0UsOERBQUNnRTtRQUFJQyxXQUFVOzswQkFFYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDcEosaVFBQU1BO2dEQUFDb0osV0FBVTs7Ozs7OzBEQUNsQiw4REFBQ0Q7Z0RBQUlDLFdBQVU7Ozs7Ozs7Ozs7OztrREFFakIsOERBQUNEOzswREFDQyw4REFBQ0U7Z0RBQUdELFdBQVU7MERBQThGOzs7Ozs7MERBRzVHLDhEQUFDakM7Z0RBQUVpQyxXQUFVOzBEQUF5Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQUsxQyw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUFvQzs7Ozs7OzBEQUNuRCw4REFBQ0Q7Z0RBQUlDLFdBQVU7MERBQTBCcEY7Ozs7Ozs7Ozs7OztrREFFM0MsOERBQUNtRjt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOzs7Ozs7MERBQ2YsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDRTtvREFBS0YsV0FBVTs7d0RBQW1Dbkk7d0RBQVc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUTFFLDhEQUFDa0k7Z0JBQUlDLFdBQVU7O2tDQUViLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FDWjs0QkFDQztnQ0FBRXhFLElBQUk7Z0NBQWtCMkUsT0FBTztnQ0FBa0JDLE1BQU0xSixpUUFBR0E7NEJBQUM7NEJBQzNEO2dDQUFFOEUsSUFBSTtnQ0FBa0IyRSxPQUFPO2dDQUFrQkMsTUFBTW5LLGlRQUFRQTs0QkFBQzs0QkFDaEU7Z0NBQUV1RixJQUFJO2dDQUFtQjJFLE9BQU87Z0NBQW1CQyxNQUFNcEssa1FBQUdBOzRCQUFDOzRCQUM3RDtnQ0FBRXdGLElBQUk7Z0NBQWEyRSxPQUFPO2dDQUFhQyxNQUFNbEssa1FBQVNBOzRCQUFDO3lCQUN4RCxDQUFDbUcsR0FBRyxDQUFDO2dDQUFDLEVBQUViLEVBQUUsRUFBRTJFLEtBQUssRUFBRUMsTUFBTUMsSUFBSSxFQUFFO2lEQUM5Qiw4REFBQ0M7Z0NBRUNDLFNBQVMsSUFBTWxHLGFBQWFtQjtnQ0FDNUJ3RSxXQUFXLDRGQUlWLE9BSEM1RixjQUFjb0IsS0FDVixvRUFDQTs7a0RBR04sOERBQUM2RTt3Q0FBS0wsV0FBVTs7Ozs7O2tEQUNoQiw4REFBQ0U7a0RBQU1DOzs7Ozs7OytCQVRGM0U7Ozs7Ozs7Ozs7O29CQWVWcEIsY0FBYyxrQ0FDYiw4REFBQzJGO3dCQUFJQyxXQUFVOzswQ0FFYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ25KLGtRQUFHQTt3REFBQ21KLFdBQVU7Ozs7OztrRUFDZiw4REFBQ1E7d0RBQUdSLFdBQVU7a0VBQW9COzs7Ozs7Ozs7Ozs7MERBRXBDLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNNO3dEQUNDQyxTQUFTMUU7d0RBQ1Q0RSxVQUFVL0csYUFBYSxDQUFDWSxNQUFNd0IsSUFBSTt3REFDbENrRSxXQUFVOzswRUFFViw4REFBQ25LLGtRQUFJQTtnRUFBQ21LLFdBQVU7Ozs7OzswRUFDaEIsOERBQUNFOzBFQUFNeEcsWUFBWSxpQkFBaUI7Ozs7Ozs7Ozs7OztrRUFFdEMsOERBQUM0Rzt3REFDQ0MsU0FBUzVFO3dEQUNUOEUsVUFBVSxDQUFDL0c7d0RBQ1hzRyxXQUFVOzswRUFFViw4REFBQ2xLLGtRQUFNQTtnRUFBQ2tLLFdBQVU7Ozs7OzswRUFDbEIsOERBQUNFOzBFQUFLOzs7Ozs7Ozs7Ozs7a0VBRVIsOERBQUNJO3dEQUNDQyxTQUFTM0U7d0RBQ1RvRSxXQUFVOzswRUFFViw4REFBQ3JKLGtRQUFTQTtnRUFBQ3FKLFdBQVU7Ozs7OzswRUFDckIsOERBQUNFOzBFQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBTVosOERBQUNIO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRTt3REFBS0YsV0FBVTtrRUFDYjFGLFNBQVM7Ozs7OztrRUFFWiw4REFBQ3lGO3dEQUFJQyxXQUFXLDhDQUlmLE9BSEMxRSxZQUFZRCxNQUFNLEtBQUssY0FBYyxtQ0FDckNDLFlBQVlELE1BQU0sS0FBSyxnQkFBZ0IsaUNBQ3ZDO2tFQUVDQyxZQUFZRCxNQUFNLENBQUM0QixXQUFXOzs7Ozs7Ozs7Ozs7MERBSW5DLDhEQUFDOEM7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRDt3REFDQ0MsV0FBVTt3REFDVlUsT0FBTzs0REFBRUMsT0FBTyxHQUF3QixPQUFyQnJGLFlBQVlDLFFBQVEsRUFBQzt3REFBRzs7Ozs7O2tFQUU3Qyw4REFBQ3dFO3dEQUFJQyxXQUFVOzs7Ozs7Ozs7Ozs7MERBR2pCLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNFOzs0REFBTTVDLEtBQUtzRCxLQUFLLENBQUN0RixZQUFZQyxRQUFROzREQUFFOzs7Ozs7O2tFQUN4Qyw4REFBQzJFOzs0REFBSzs0REFBZ0J0RyxVQUFVaUgsT0FBTyxDQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQU05Qyw4REFBQ2Q7O2tEQUNDLDhEQUFDQTt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUM5SSxrUUFBS0E7Z0RBQUM4SSxXQUFVOzs7Ozs7MERBQ2pCLDhEQUFDUTtnREFBR1IsV0FBVTswREFBb0I7Ozs7Ozs7Ozs7OztrREFHcEMsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUNaekksT0FBTzhFLEdBQUcsQ0FBQyxDQUFDSTs0Q0FDWCxNQUFNNEQsT0FBTzNFLFVBQVUsQ0FBQ2UsTUFBTWpCLEVBQUUsQ0FBQzs0Q0FDakMscUJBQ0UsOERBQUN1RTtnREFBbUJDLFdBQVU7O2tFQUM1Qiw4REFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDRDtnRUFBSUMsV0FBVyxrQkFLZixPQUpDdkQsTUFBTXBCLE1BQU0sS0FBSyxZQUFZLGlDQUM3Qm9CLE1BQU1wQixNQUFNLEtBQUssY0FBYyxtQ0FDL0JvQixNQUFNcEIsTUFBTSxLQUFLLFVBQVUsK0JBQzNCOzBFQUVBLDRFQUFDZ0Y7b0VBQUtMLFdBQVU7Ozs7Ozs7Ozs7OzBFQUVsQiw4REFBQ0Q7Z0VBQUlDLFdBQVU7O2tGQUNiLDhEQUFDYzt3RUFBR2QsV0FBVTtrRkFBcUJ2RCxNQUFNakYsSUFBSTs7Ozs7O2tGQUM3Qyw4REFBQ3VHO3dFQUFFaUMsV0FBVTtrRkFBMEJ2RCxNQUFNM0UsY0FBYzs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tFQUsvRCw4REFBQ2lJO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7Z0VBQUlDLFdBQVU7O2tGQUNiLDhEQUFDRTtrRkFBSzs7Ozs7O2tGQUNOLDhEQUFDQTs7NEVBQU16RCxNQUFNNUUsVUFBVTs0RUFBQzs7Ozs7Ozs7Ozs7OzswRUFFMUIsOERBQUNrSTtnRUFBSUMsV0FBVTswRUFDYiw0RUFBQ0Q7b0VBQ0NDLFdBQVcsbURBSVYsT0FIQ3ZELE1BQU01RSxVQUFVLElBQUksS0FBSyxtREFDekI0RSxNQUFNNUUsVUFBVSxJQUFJLEtBQUssK0NBQ3pCO29FQUVGNkksT0FBTzt3RUFBRUMsT0FBTyxHQUFvQixPQUFqQmxFLE1BQU01RSxVQUFVLEVBQUM7b0VBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7O2tFQU03Qyw4REFBQ2tJO3dEQUFJQyxXQUFVO2tFQUNiLDRFQUFDRDs0REFDQ0MsV0FBVyxtREFJVixPQUhDdkQsTUFBTXBCLE1BQU0sS0FBSyxjQUFjLGlCQUMvQm9CLE1BQU1wQixNQUFNLEtBQUssWUFBWSw4QkFDN0I7NERBRUZxRixPQUFPO2dFQUFFQyxPQUFPLEdBQWtCLE9BQWZsRSxNQUFNbEIsUUFBUSxFQUFDOzREQUFHOzs7Ozs7Ozs7OztrRUFJekMsOERBQUN3RTt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNFO2dFQUFLRixXQUFXLDBCQUtoQixPQUpDdkQsTUFBTXBCLE1BQU0sS0FBSyxZQUFZLGlDQUM3Qm9CLE1BQU1wQixNQUFNLEtBQUssY0FBYyxtQ0FDL0JvQixNQUFNcEIsTUFBTSxLQUFLLFVBQVUsK0JBQzNCOzBFQUVDb0IsTUFBTXBCLE1BQU0sQ0FBQzRCLFdBQVc7Ozs7Ozs0REFFMUJSLE1BQU1oQixJQUFJLEdBQUcsbUJBQUssOERBQUN5RTtnRUFBS0YsV0FBVTs7b0VBQWlCO29FQUFFdkQsTUFBTWhCLElBQUksQ0FBQ29GLE9BQU8sQ0FBQzs7Ozs7Ozs7Ozs7Ozs7K0NBdkRuRXBFLE1BQU1qQixFQUFFOzs7Ozt3Q0EyRHRCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7b0JBT1BwQixjQUFjLGtDQUNiLDhEQUFDMkY7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDbEosa1FBQUtBOzRDQUFDa0osV0FBVTs7Ozs7O3NEQUNqQiw4REFBQ1E7NENBQUdSLFdBQVU7c0RBQW9COzs7Ozs7Ozs7Ozs7OENBR3BDLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7O3NFQUNDLDhEQUFDSTs0REFBTUgsV0FBVTs7OEVBQ2YsOERBQUMzSixpUUFBTUE7b0VBQUMySixXQUFVOzs7Ozs7Z0VBQXdCOzs7Ozs7O3NFQUc1Qyw4REFBQ2U7NERBQ0NuQyxNQUFLOzREQUNMb0MsT0FBTzFHOzREQUNQMkcsVUFBVSxDQUFDQyxJQUFNM0csU0FBUzJHLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSzs0REFDeENJLGFBQVk7NERBQ1pwQixXQUFVOzs7Ozs7Ozs7Ozs7OERBSWQsOERBQUNEOztzRUFDQyw4REFBQ0k7NERBQU1ILFdBQVU7OzhFQUNmLDhEQUFDakosa1FBQUtBO29FQUFDaUosV0FBVTs7Ozs7O2dFQUF3Qjs7Ozs7OztzRUFHM0MsOERBQUNlOzREQUNDbkMsTUFBSzs0REFDTG9DLE9BQU94Rzs0REFDUHlHLFVBQVUsQ0FBQ0MsSUFBTXpHLFlBQVl5RyxFQUFFQyxNQUFNLENBQUNILEtBQUs7NERBQzNDSSxhQUFZOzREQUNacEIsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQUtoQiw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDs7c0VBQ0MsOERBQUNJOzREQUFNSCxXQUFVOzs4RUFDZiw4REFBQzdKLGlRQUFRQTtvRUFBQzZKLFdBQVU7Ozs7OztnRUFBd0I7Ozs7Ozs7c0VBRzlDLDhEQUFDcUI7NERBQ0NMLE9BQU8xSDs0REFDUDJILFVBQVUsQ0FBQ0MsSUFBTTNILGVBQWUySCxFQUFFQyxNQUFNLENBQUNILEtBQUs7NERBQzlDaEIsV0FBVTs7OEVBRVYsOERBQUNzQjtvRUFBT04sT0FBTTs4RUFBWTs7Ozs7OzhFQUMxQiw4REFBQ007b0VBQU9OLE9BQU07OEVBQVU7Ozs7Ozs4RUFDeEIsOERBQUNNO29FQUFPTixPQUFNOzhFQUFlOzs7Ozs7OEVBQzdCLDhEQUFDTTtvRUFBT04sT0FBTTs4RUFBc0I7Ozs7Ozs4RUFDcEMsOERBQUNNO29FQUFPTixPQUFNOzhFQUFlOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBSWpDLDhEQUFDakI7O3NFQUNDLDhEQUFDSTs0REFBTUgsV0FBVTs7OEVBQ2YsOERBQUNoSixrUUFBVUE7b0VBQUNnSixXQUFVOzs7Ozs7Z0VBQXdCO2dFQUMvQnRGO2dFQUFhOzs7Ozs7O3NFQUU5Qiw4REFBQ3FHOzREQUNDbkMsTUFBSzs0REFDTDJDLEtBQUk7NERBQ0poRSxLQUFJOzREQUNKUCxNQUFLOzREQUNMZ0UsT0FBT3RHOzREQUNQdUcsVUFBVSxDQUFDQyxJQUFNdkcsZ0JBQWdCNkcsU0FBU04sRUFBRUMsTUFBTSxDQUFDSCxLQUFLOzREQUN4RGhCLFdBQVU7Ozs7OztzRUFFWiw4REFBQ0Q7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDRTs4RUFBSzs7Ozs7OzhFQUNOLDhEQUFDQTs4RUFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7b0JBVW5COUYsY0FBYyxtQ0FDYiw4REFBQzJGO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQy9JLGtRQUFRQTt3REFBQytJLFdBQVU7Ozs7OztrRUFDcEIsOERBQUNRO3dEQUFHUixXQUFVO2tFQUFvQjs7Ozs7Ozs7Ozs7OzRDQUVuQ2xHLGdDQUNDLDhEQUFDaUc7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDTTt3REFDQ0MsU0FBUyxJQUFNcEMsY0FBYzt3REFDN0I2QixXQUFVOzswRUFFViw4REFBQ2pLLGtRQUFRQTtnRUFBQ2lLLFdBQVU7Ozs7OzswRUFDcEIsOERBQUNFOzBFQUFLOzs7Ozs7Ozs7Ozs7a0VBRVIsOERBQUNJO3dEQUNDQyxTQUFTLElBQU1wQyxjQUFjO3dEQUM3QjZCLFdBQVU7OzBFQUVWLDhEQUFDakssa1FBQVFBO2dFQUFDaUssV0FBVTs7Ozs7OzBFQUNwQiw4REFBQ0U7MEVBQUs7Ozs7Ozs7Ozs7OztrRUFFUiw4REFBQ0k7d0RBQ0NDLFNBQVNiO3dEQUNUTSxXQUFVO2tFQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FRVCw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUNEO3dDQUFJQyxXQUFVO2tEQUNabEcsK0JBQ0MsOERBQUNpRzs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQzNJLHFEQUFhQTswREFBRXlDOzs7Ozs7Ozs7O2lFQUdsQiw4REFBQ2lHOzRDQUFJQyxXQUFVO3NEQUNiLDRFQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNoSyxrUUFBR0E7d0RBQUNnSyxXQUFVOzs7Ozs7a0VBQ2YsOERBQUNqQzt3REFBRWlDLFdBQVU7a0VBQVU7Ozs7OztrRUFDdkIsOERBQUNqQzt3REFBRWlDLFdBQVU7a0VBQWU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O29CQVczQzVGLGNBQWMsNkJBQ2IsOERBQUMyRjt3QkFBSUMsV0FBVTs7NEJBRVo5RiwrQkFDQyw4REFBQzZGO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUM1SSxrUUFBUUE7NENBQUM0SSxXQUFVOzs7Ozs7c0RBQ3BCLDhEQUFDRTs0Q0FBS0YsV0FBVTtzREFBYzs7Ozs7O3NEQUM5Qiw4REFBQ0U7NENBQUtGLFdBQVU7c0RBQVE5Rjs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBTTlCLDhEQUFDNkY7O2tEQUNDLDhEQUFDQTt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUM3SSxrUUFBS0E7Z0RBQUM2SSxXQUFVOzs7Ozs7MERBQ2pCLDhEQUFDUTtnREFBR1IsV0FBVTswREFBb0I7Ozs7Ozs7Ozs7OztrREFHcEMsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7MERBQ2IsNEVBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ3ZKLGtRQUFVQTs0REFBQ3VKLFdBQVU7Ozs7OztzRUFDdEIsOERBQUNEOzs4RUFDQyw4REFBQ2hDO29FQUFFaUMsV0FBVTs4RUFBeUI7Ozs7Ozs4RUFDdEMsOERBQUNqQztvRUFBRWlDLFdBQVU7O3dFQUFvQzt3RUFBRXBHLFVBQVVpSCxPQUFPLENBQUM7Ozs7Ozs7OEVBQ3JFLDhEQUFDOUM7b0VBQUVpQyxXQUFVOzhFQUF5Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBSzVDLDhEQUFDRDtnREFBSUMsV0FBVTswREFDYiw0RUFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDeEosa1FBQUtBOzREQUFDd0osV0FBVTs7Ozs7O3NFQUNqQiw4REFBQ0Q7OzhFQUNDLDhEQUFDaEM7b0VBQUVpQyxXQUFVOzhFQUF5Qjs7Ozs7OzhFQUN0Qyw4REFBQ2pDO29FQUFFaUMsV0FBVTs4RUFBbUM7Ozs7Ozs4RUFDaEQsOERBQUNqQztvRUFBRWlDLFdBQVU7OEVBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFLM0MsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUM5SixrUUFBU0E7NERBQUM4SixXQUFVOzs7Ozs7c0VBQ3JCLDhEQUFDRDs7OEVBQ0MsOERBQUNoQztvRUFBRWlDLFdBQVU7OEVBQXlCOzs7Ozs7OEVBQ3RDLDhEQUFDakM7b0VBQUVpQyxXQUFVOzhFQUFxQzs7Ozs7OzhFQUNsRCw4REFBQ2pDO29FQUFFaUMsV0FBVTs4RUFBMEI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQUs3Qyw4REFBQ0Q7Z0RBQUlDLFdBQVU7MERBQ2IsNEVBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ3pKLGlRQUFXQTs0REFBQ3lKLFdBQVU7Ozs7OztzRUFDdkIsOERBQUNEOzs4RUFDQyw4REFBQ2hDO29FQUFFaUMsV0FBVTs4RUFBeUI7Ozs7Ozs4RUFDdEMsOERBQUNqQztvRUFBRWlDLFdBQVU7OEVBQW1DOzs7Ozs7OEVBQ2hELDhEQUFDakM7b0VBQUVpQyxXQUFVOzhFQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBUS9DLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNRO3dDQUFHUixXQUFVO2tEQUF5Qjs7Ozs7O2tEQUN2Qyw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ1poRyxjQUFjekMsTUFBTSxDQUFDOEUsR0FBRyxDQUFDLENBQUNJLE9BQU9nRixzQkFDaEMsOERBQUMxQjtnREFBZ0JDLFdBQVU7O2tFQUN6Qiw4REFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDRDtnRUFBSUMsV0FBVTs7Ozs7OzBFQUNmLDhEQUFDRDs7a0ZBQ0MsOERBQUNlO3dFQUFHZCxXQUFVO2tGQUF3QnZELE1BQU1qRixJQUFJOzs7Ozs7a0ZBQ2hELDhEQUFDdUc7d0VBQUVpQyxXQUFVO2tGQUEwQnZELE1BQU1oRixjQUFjOzs7Ozs7a0ZBQzNELDhEQUFDc0c7d0VBQUVpQyxXQUFVOzs0RUFBeUI7NEVBQVF2RCxNQUFNL0UsS0FBSyxDQUFDbUYsSUFBSSxDQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tFQUduRSw4REFBQ2tEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ2pDO2dFQUFFaUMsV0FBVTswRUFBcUN2RCxNQUFNOUUsV0FBVzs7Ozs7OzBFQUNuRSw4REFBQ29HO2dFQUFFaUMsV0FBVTswRUFBMEJ2RCxNQUFNN0UsZ0JBQWdCOzs7Ozs7MEVBQzdELDhEQUFDbUk7Z0VBQUlDLFdBQVU7O2tGQUNiLDhEQUFDRTt3RUFBS0YsV0FBVTtrRkFBeUI7Ozs7OztrRkFDekMsOERBQUNFO3dFQUFLRixXQUFVOzs0RUFBb0N2RCxNQUFNNUUsVUFBVTs0RUFBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7K0NBZGpFNEo7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUEwQjVCO0dBMXZCd0I1STtLQUFBQSIsInNvdXJjZXMiOlsiL1VzZXJzL2FheXVzaG1pc2hyYS9EZXNrdG9wL29sZCBpbnZpbmNpYmxlIHdpdGggZGVlcHJlc2VhcmNoL3NyYy9hcHAvY29udGVudC13cml0ZXIvcGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IFxuICBQbGF5LCBcbiAgU3F1YXJlLCBcbiAgRG93bmxvYWQsIFxuICBFeWUsIFxuICBTZXR0aW5ncywgXG4gIEJhckNoYXJ0MywgXG4gIEZpbGVUZXh0LCBcbiAgU2VhcmNoLCBcbiAgVGFyZ2V0LCBcbiAgUGVuVG9vbCwgXG4gIENoZWNrQ2lyY2xlLCBcbiAgQ2xvY2ssIFxuICBEb2xsYXJTaWduLCBcbiAgWmFwLCBcbiAgUmVmcmVzaEN3LFxuICBTaGllbGQsXG4gIENwdSxcbiAgQnJhaW4sXG4gIEdsb2JlLFxuICBUcmVuZGluZ1VwLFxuICBTcGFya2xlcyxcbiAgVXNlcnMsXG4gIEF3YXJkLFxuICBBY3Rpdml0eVxufSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuaW1wb3J0IFJlYWN0TWFya2Rvd24gZnJvbSAncmVhY3QtbWFya2Rvd24nO1xuXG5pbnRlcmZhY2UgV29ya2Zsb3dTdGVwIHtcbiAgaWQ6IHN0cmluZztcbiAgbmFtZTogc3RyaW5nO1xuICBzdGF0dXM6ICdwZW5kaW5nJyB8ICdydW5uaW5nJyB8ICdjb21wbGV0ZWQnIHwgJ2Vycm9yJztcbiAgcHJvZ3Jlc3M6IG51bWJlcjtcbiAgYWdlbnQ6IHN0cmluZztcbiAgb3V0cHV0Pzogc3RyaW5nO1xuICBjb3N0PzogbnVtYmVyO1xuICBkdXJhdGlvbj86IG51bWJlcjtcbn1cblxuaW50ZXJmYWNlIENvbnRlbnRQcm9qZWN0IHtcbiAgaWQ6IHN0cmluZztcbiAgdG9waWM6IHN0cmluZztcbiAgdGFyZ2V0S2V5d29yZHM6IHN0cmluZ1tdO1xuICBjb250ZW50VHlwZTogc3RyaW5nO1xuICB3b3JkQ291bnQ6IG51bWJlcjtcbiAgc3RhdHVzOiAnZHJhZnQnIHwgJ2luX3Byb2dyZXNzJyB8ICdjb21wbGV0ZWQnO1xuICBjcmVhdGVkQXQ6IERhdGU7XG4gIHN0ZXBzOiBXb3JrZmxvd1N0ZXBbXTtcbn1cblxuLy8gRW5oYW5jZWQgdGVhbSBhbmFseXRpY3Mgd2l0aCBJbnZpbmNpYmxlIHRoZW1lXG5jb25zdCBnZXRUZWFtQW5hbHl0aWNzID0gKCkgPT4gKHtcbiAgYWdlbnRzOiBbXG4gICAgeyBcbiAgICAgIG5hbWU6ICdSZXNlYXJjaCBBZ2VudCcsIFxuICAgICAgc3BlY2lhbGl6YXRpb246ICdXZWIgUmVzZWFyY2ggJiBEYXRhIENvbGxlY3Rpb24nLCBcbiAgICAgIHRvb2xzOiBbJ1RhdmlseSBTZWFyY2gnLCAnU0VSUCBBbmFseXNpcyddLCBcbiAgICAgIHBlcmZvcm1hbmNlOiAnRXhjZWxsZW50JywgXG4gICAgICBhdmdFeGVjdXRpb25UaW1lOiAnNDVzJyxcbiAgICAgIHBvd2VyTGV2ZWw6IDk1LFxuICAgICAgc3BlY2lhbEFiaWxpdHk6ICdEZWVwIFdlYiBJbnRlbGxpZ2VuY2UnXG4gICAgfSxcbiAgICB7IFxuICAgICAgbmFtZTogJ1NFTyBTdHJhdGVnaXN0JywgXG4gICAgICBzcGVjaWFsaXphdGlvbjogJ0tleXdvcmQgUmVzZWFyY2ggJiBTdHJhdGVneScsIFxuICAgICAgdG9vbHM6IFsnS2V5d29yZCBBbmFseXNpcycsICdDb21wZXRpdGlvbiBSZXNlYXJjaCddLCBcbiAgICAgIHBlcmZvcm1hbmNlOiAnVmVyeSBHb29kJywgXG4gICAgICBhdmdFeGVjdXRpb25UaW1lOiAnMzBzJyxcbiAgICAgIHBvd2VyTGV2ZWw6IDg4LFxuICAgICAgc3BlY2lhbEFiaWxpdHk6ICdTZWFyY2ggRG9taW5hdGlvbidcbiAgICB9LFxuICAgIHsgXG4gICAgICBuYW1lOiAnQ29udGVudCBBcmNoaXRlY3QnLCBcbiAgICAgIHNwZWNpYWxpemF0aW9uOiAnQ29udGVudCBTdHJ1Y3R1cmUgJiBQbGFubmluZycsIFxuICAgICAgdG9vbHM6IFsnT3V0bGluZSBHZW5lcmF0b3InLCAnQ29udGVudCBQbGFubmVyJ10sIFxuICAgICAgcGVyZm9ybWFuY2U6ICdFeGNlbGxlbnQnLCBcbiAgICAgIGF2Z0V4ZWN1dGlvblRpbWU6ICcyNXMnLFxuICAgICAgcG93ZXJMZXZlbDogOTIsXG4gICAgICBzcGVjaWFsQWJpbGl0eTogJ1N0cnVjdHVyZSBNYXN0ZXJ5J1xuICAgIH0sXG4gICAgeyBcbiAgICAgIG5hbWU6ICdDb250ZW50IENyZWF0b3InLCBcbiAgICAgIHNwZWNpYWxpemF0aW9uOiAnQUktUG93ZXJlZCBXcml0aW5nJywgXG4gICAgICB0b29sczogWydLaW1pIEsyIE1vZGVsJywgJ0NvbnRlbnQgR2VuZXJhdG9yJ10sIFxuICAgICAgcGVyZm9ybWFuY2U6ICdPdXRzdGFuZGluZycsIFxuICAgICAgYXZnRXhlY3V0aW9uVGltZTogJzJtJyxcbiAgICAgIHBvd2VyTGV2ZWw6IDk4LFxuICAgICAgc3BlY2lhbEFiaWxpdHk6ICdDcmVhdGl2ZSBHZW5lc2lzJ1xuICAgIH0sXG4gICAgeyBcbiAgICAgIG5hbWU6ICdRdWFsaXR5IEVkaXRvcicsIFxuICAgICAgc3BlY2lhbGl6YXRpb246ICdDb250ZW50IFJldmlldyAmIE9wdGltaXphdGlvbicsIFxuICAgICAgdG9vbHM6IFsnR3JhbW1hciBDaGVjaycsICdTRU8gT3B0aW1pemF0aW9uJ10sIFxuICAgICAgcGVyZm9ybWFuY2U6ICdFeGNlbGxlbnQnLCBcbiAgICAgIGF2Z0V4ZWN1dGlvblRpbWU6ICc0MHMnLFxuICAgICAgcG93ZXJMZXZlbDogOTAsXG4gICAgICBzcGVjaWFsQWJpbGl0eTogJ1BlcmZlY3Rpb24gUHJvdG9jb2wnXG4gICAgfVxuICBdLFxuICBjYXBhYmlsaXRpZXM6IHtcbiAgICBjb250ZW50X3R5cGVzOiBbJ0Jsb2cgUG9zdHMnLCAnQXJ0aWNsZXMnLCAnTGFuZGluZyBQYWdlcycsICdTb2NpYWwgTWVkaWEnLCAnRW1haWwgQ2FtcGFpZ25zJywgJ1Byb2R1Y3QgRGVzY3JpcHRpb25zJ10sXG4gICAgd29yZF9jb3VudF9yYW5nZTogJzUwMC01MDAwJyxcbiAgICBzZW9fb3B0aW1pemF0aW9uOiAnUmVhbC10aW1lIFNFTyBzY29yaW5nIGFuZCByZWNvbW1lbmRhdGlvbnMnLFxuICAgIGZhY3RfY2hlY2tpbmc6ICdBdXRvbWF0ZWQgZmFjdC1jaGVja2luZyB3aXRoIHNvdXJjZSB2ZXJpZmljYXRpb24nLFxuICAgIGJyYW5kX3ZvaWNlOiAnQWRhcHRpdmUgYnJhbmQgdm9pY2UgbWF0Y2hpbmcnXG4gIH0sXG4gIHdvcmtmbG93czogW1xuICAgIHsgbmFtZTogJ1N0YW5kYXJkIENvbnRlbnQgQ3JlYXRpb24nLCBhZ2VudHM6IDUsIHRhc2tzOiA1LCBlc3RpbWF0ZWRUaW1lOiAnNC02IG1pbnV0ZXMnLCBjb3N0RXN0aW1hdGU6ICckMC40MC0wLjgwJywgc3VjY2Vzc19yYXRlOiAnOTglJyB9LFxuICAgIHsgbmFtZTogJ1NFTy1Gb2N1c2VkIENvbnRlbnQnLCBhZ2VudHM6IDUsIHRhc2tzOiA3LCBlc3RpbWF0ZWRUaW1lOiAnNi04IG1pbnV0ZXMnLCBjb3N0RXN0aW1hdGU6ICckMC42MC0xLjAwJywgc3VjY2Vzc19yYXRlOiAnOTYlJyB9LFxuICAgIHsgbmFtZTogJ1RlY2huaWNhbCBDb250ZW50JywgYWdlbnRzOiA1LCB0YXNrczogNiwgZXN0aW1hdGVkVGltZTogJzgtMTIgbWludXRlcycsIGNvc3RFc3RpbWF0ZTogJyQwLjgwLTEuMjAnLCBzdWNjZXNzX3JhdGU6ICc5NCUnIH1cbiAgXVxufSk7XG5cbmNvbnN0IHZhbGlkYXRlVGVhbUNvbmZpZ3VyYXRpb24gPSAoKSA9PiAoeyBpc1ZhbGlkOiB0cnVlLCBpc3N1ZXM6IFtdIH0pO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBJbnZpbmNpYmxlQ29udGVudFdyaXRlcigpIHtcbiAgY29uc3QgW3Byb2plY3RzLCBzZXRQcm9qZWN0c10gPSB1c2VTdGF0ZTxDb250ZW50UHJvamVjdFtdPihbXSk7XG4gIGNvbnN0IFtjdXJyZW50UHJvamVjdCwgc2V0Q3VycmVudFByb2plY3RdID0gdXNlU3RhdGU8Q29udGVudFByb2plY3QgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW25ld1Byb2plY3RUb3BpYywgc2V0TmV3UHJvamVjdFRvcGljXSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW3RhcmdldEtleXdvcmRzLCBzZXRUYXJnZXRLZXl3b3Jkc10gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFtjb250ZW50VHlwZSwgc2V0Q29udGVudFR5cGVdID0gdXNlU3RhdGUoJ2Jsb2ctcG9zdCcpO1xuICBjb25zdCBbd29yZENvdW50LCBzZXRXb3JkQ291bnRdID0gdXNlU3RhdGUoMjAwMCk7XG4gIGNvbnN0IFtpc1J1bm5pbmcsIHNldElzUnVubmluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFt0b3RhbENvc3QsIHNldFRvdGFsQ29zdF0gPSB1c2VTdGF0ZSgwKTtcbiAgY29uc3QgW3ByZXZpZXdDb250ZW50LCBzZXRQcmV2aWV3Q29udGVudF0gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFt0ZWFtQW5hbHl0aWNzLCBzZXRUZWFtQW5hbHl0aWNzXSA9IHVzZVN0YXRlKGdldFRlYW1BbmFseXRpY3MoKSk7XG4gIGNvbnN0IFt3b3JrZmxvd0Vycm9yLCBzZXRXb3JrZmxvd0Vycm9yXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbYWN0aXZlVGFiLCBzZXRBY3RpdmVUYWJdID0gdXNlU3RhdGUoJ2NvbW1hbmQtY2VudGVyJyk7XG4gIGNvbnN0IFt0b3BpYywgc2V0VG9waWNdID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBba2V5d29yZHMsIHNldEtleXdvcmRzXSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW3RhcmdldExlbmd0aCwgc2V0VGFyZ2V0TGVuZ3RoXSA9IHVzZVN0YXRlKDIwMDApO1xuICBcbiAgLy8gRW5oYW5jZWQgVUkgc3RhdGVzXG4gIGNvbnN0IFtzeXN0ZW1TdGF0dXMsIHNldFN5c3RlbVN0YXR1c10gPSB1c2VTdGF0ZSgnUkVBRFknKTtcbiAgY29uc3QgW3Bvd2VyTGV2ZWwsIHNldFBvd2VyTGV2ZWxdID0gdXNlU3RhdGUoMTAwKTtcbiAgXG4gIC8vIE1vY2sgY3VycmVudCB0YXNrIGRhdGFcbiAgY29uc3QgZ2V0T3ZlcmFsbFByb2dyZXNzID0gKCkgPT4ge1xuICAgIGlmICghY3VycmVudFByb2plY3QgfHwgIWN1cnJlbnRQcm9qZWN0LnN0ZXBzIHx8IGN1cnJlbnRQcm9qZWN0LnN0ZXBzLmxlbmd0aCA9PT0gMCkgcmV0dXJuIDA7XG4gICAgY29uc3QgY29tcGxldGVkU3RlcHMgPSBjdXJyZW50UHJvamVjdC5zdGVwcy5maWx0ZXIocyA9PiBzLnN0YXR1cyA9PT0gJ2NvbXBsZXRlZCcpLmxlbmd0aDtcbiAgICByZXR1cm4gKGNvbXBsZXRlZFN0ZXBzIC8gY3VycmVudFByb2plY3Quc3RlcHMubGVuZ3RoKSAqIDEwMDtcbiAgfTtcbiAgXG4gIGNvbnN0IFtjdXJyZW50VGFza10gPSB1c2VTdGF0ZSh7XG4gICAgdG9waWM6IGN1cnJlbnRQcm9qZWN0Py50b3BpYyB8fCAnJyxcbiAgICBzdGF0dXM6IGN1cnJlbnRQcm9qZWN0Py5zdGF0dXMgfHwgJ3BlbmRpbmcnLFxuICAgIHByb2dyZXNzOiBjdXJyZW50UHJvamVjdCA/IGdldE92ZXJhbGxQcm9ncmVzcygpIDogMFxuICB9KTtcbiAgXG4gIC8vIEVuaGFuY2VkIGFnZW50cyBkYXRhIHdpdGggcG93ZXIgbGV2ZWxzXG4gIGNvbnN0IFthZ2VudHNdID0gdXNlU3RhdGUoW1xuICAgIHsgXG4gICAgICBpZDogJ1Jlc2VhcmNoIEFnZW50JywgXG4gICAgICBuYW1lOiAnUmVzZWFyY2ggQWdlbnQnLCBcbiAgICAgIHN0YXR1czogJ2lkbGUnIGFzIGNvbnN0LCBcbiAgICAgIHByb2dyZXNzOiAwLCBcbiAgICAgIGN1cnJlbnRUYXNrOiAnJywgXG4gICAgICBjb3N0OiAwLFxuICAgICAgcG93ZXJMZXZlbDogOTUsXG4gICAgICBzcGVjaWFsQWJpbGl0eTogJ0RlZXAgV2ViIEludGVsbGlnZW5jZSdcbiAgICB9LFxuICAgIHsgXG4gICAgICBpZDogJ1NFTyBTdHJhdGVnaXN0JywgXG4gICAgICBuYW1lOiAnU0VPIFN0cmF0ZWdpc3QnLCBcbiAgICAgIHN0YXR1czogJ2lkbGUnIGFzIGNvbnN0LCBcbiAgICAgIHByb2dyZXNzOiAwLCBcbiAgICAgIGN1cnJlbnRUYXNrOiAnJywgXG4gICAgICBjb3N0OiAwLFxuICAgICAgcG93ZXJMZXZlbDogODgsXG4gICAgICBzcGVjaWFsQWJpbGl0eTogJ1NlYXJjaCBEb21pbmF0aW9uJ1xuICAgIH0sXG4gICAgeyBcbiAgICAgIGlkOiAnQ29udGVudCBBcmNoaXRlY3QnLCBcbiAgICAgIG5hbWU6ICdDb250ZW50IEFyY2hpdGVjdCcsIFxuICAgICAgc3RhdHVzOiAnaWRsZScgYXMgY29uc3QsIFxuICAgICAgcHJvZ3Jlc3M6IDAsIFxuICAgICAgY3VycmVudFRhc2s6ICcnLCBcbiAgICAgIGNvc3Q6IDAsXG4gICAgICBwb3dlckxldmVsOiA5MixcbiAgICAgIHNwZWNpYWxBYmlsaXR5OiAnU3RydWN0dXJlIE1hc3RlcnknXG4gICAgfSxcbiAgICB7IFxuICAgICAgaWQ6ICdDb250ZW50IENyZWF0b3InLCBcbiAgICAgIG5hbWU6ICdDb250ZW50IENyZWF0b3InLCBcbiAgICAgIHN0YXR1czogJ2lkbGUnIGFzIGNvbnN0LCBcbiAgICAgIHByb2dyZXNzOiAwLCBcbiAgICAgIGN1cnJlbnRUYXNrOiAnJywgXG4gICAgICBjb3N0OiAwLFxuICAgICAgcG93ZXJMZXZlbDogOTgsXG4gICAgICBzcGVjaWFsQWJpbGl0eTogJ0NyZWF0aXZlIEdlbmVzaXMnXG4gICAgfSxcbiAgICB7IFxuICAgICAgaWQ6ICdRdWFsaXR5IEVkaXRvcicsIFxuICAgICAgbmFtZTogJ1F1YWxpdHkgRWRpdG9yJywgXG4gICAgICBzdGF0dXM6ICdpZGxlJyBhcyBjb25zdCwgXG4gICAgICBwcm9ncmVzczogMCwgXG4gICAgICBjdXJyZW50VGFzazogJycsIFxuICAgICAgY29zdDogMCxcbiAgICAgIHBvd2VyTGV2ZWw6IDkwLFxuICAgICAgc3BlY2lhbEFiaWxpdHk6ICdQZXJmZWN0aW9uIFByb3RvY29sJ1xuICAgIH1cbiAgXSk7XG5cbiAgY29uc3QgYWdlbnRJY29uczogUmVjb3JkPHN0cmluZywgYW55PiA9IHtcbiAgICAnUmVzZWFyY2ggQWdlbnQnOiBTZWFyY2gsXG4gICAgJ1NFTyBTdHJhdGVnaXN0JzogVGFyZ2V0LFxuICAgICdDb250ZW50IEFyY2hpdGVjdCc6IEZpbGVUZXh0LFxuICAgICdDb250ZW50IENyZWF0b3InOiBQZW5Ub29sLFxuICAgICdRdWFsaXR5IEVkaXRvcic6IENoZWNrQ2lyY2xlXG4gIH07XG4gIFxuICAvLyBQbGFjZWhvbGRlciBmdW5jdGlvbnNcbiAgY29uc3Qgc3RvcEdlbmVyYXRpb24gPSAoKSA9PiB7XG4gICAgc2V0SXNSdW5uaW5nKGZhbHNlKTtcbiAgICBzZXRTeXN0ZW1TdGF0dXMoJ1NUT1BQRUQnKTtcbiAgfTtcbiAgXG4gIGNvbnN0IHJlc2V0V29ya2Zsb3cgPSAoKSA9PiB7XG4gICAgc2V0SXNSdW5uaW5nKGZhbHNlKTtcbiAgICBzZXRDdXJyZW50UHJvamVjdChudWxsKTtcbiAgICBzZXRUb3RhbENvc3QoMCk7XG4gICAgc2V0UHJldmlld0NvbnRlbnQoJycpO1xuICAgIHNldFdvcmtmbG93RXJyb3IobnVsbCk7XG4gICAgc2V0U3lzdGVtU3RhdHVzKCdSRUFEWScpO1xuICAgIHNldFBvd2VyTGV2ZWwoMTAwKTtcbiAgfTtcblxuICBjb25zdCBzdGFydENvbnRlbnRHZW5lcmF0aW9uID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmICghdG9waWMudHJpbSgpKSB7XG4gICAgICBhbGVydCgnUGxlYXNlIGVudGVyIGEgdG9waWMgZmlyc3QnKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBzZXRTeXN0ZW1TdGF0dXMoJ0lOSVRJQUxJWklORycpO1xuICAgIHNldFBvd2VyTGV2ZWwoOTUpO1xuXG4gICAgLy8gQ3JlYXRlIGEgbmV3IHByb2plY3QgaWYgbm9uZSBleGlzdHNcbiAgICBpZiAoIWN1cnJlbnRQcm9qZWN0KSB7XG4gICAgICBjb25zdCBwcm9qZWN0OiBDb250ZW50UHJvamVjdCA9IHtcbiAgICAgICAgaWQ6IERhdGUubm93KCkudG9TdHJpbmcoKSxcbiAgICAgICAgdG9waWM6IHRvcGljLFxuICAgICAgICB0YXJnZXRLZXl3b3Jkczoga2V5d29yZHMuc3BsaXQoJywnKS5tYXAoayA9PiBrLnRyaW0oKSkuZmlsdGVyKEJvb2xlYW4pLFxuICAgICAgICBjb250ZW50VHlwZSxcbiAgICAgICAgd29yZENvdW50OiB0YXJnZXRMZW5ndGgsXG4gICAgICAgIHN0YXR1czogJ2RyYWZ0JyxcbiAgICAgICAgY3JlYXRlZEF0OiBuZXcgRGF0ZSgpLFxuICAgICAgICBzdGVwczogW1xuICAgICAgICAgIHsgaWQ6ICcxJywgbmFtZTogJ1Jlc2VhcmNoICYgRGF0YSBDb2xsZWN0aW9uJywgc3RhdHVzOiAncGVuZGluZycsIHByb2dyZXNzOiAwLCBhZ2VudDogJ1Jlc2VhcmNoIEFnZW50JyB9LFxuICAgICAgICAgIHsgaWQ6ICcyJywgbmFtZTogJ1NFTyBTdHJhdGVneSAmIEtleXdvcmRzJywgc3RhdHVzOiAncGVuZGluZycsIHByb2dyZXNzOiAwLCBhZ2VudDogJ1NFTyBTdHJhdGVnaXN0JyB9LFxuICAgICAgICAgIHsgaWQ6ICczJywgbmFtZTogJ0NvbnRlbnQgQXJjaGl0ZWN0dXJlJywgc3RhdHVzOiAncGVuZGluZycsIHByb2dyZXNzOiAwLCBhZ2VudDogJ0NvbnRlbnQgQXJjaGl0ZWN0JyB9LFxuICAgICAgICAgIHsgaWQ6ICc0JywgbmFtZTogJ0NvbnRlbnQgQ3JlYXRpb24nLCBzdGF0dXM6ICdwZW5kaW5nJywgcHJvZ3Jlc3M6IDAsIGFnZW50OiAnQ29udGVudCBDcmVhdG9yJyB9LFxuICAgICAgICAgIHsgaWQ6ICc1JywgbmFtZTogJ1F1YWxpdHkgUmV2aWV3ICYgT3B0aW1pemF0aW9uJywgc3RhdHVzOiAncGVuZGluZycsIHByb2dyZXNzOiAwLCBhZ2VudDogJ1F1YWxpdHkgRWRpdG9yJyB9XG4gICAgICAgIF1cbiAgICAgIH07XG4gICAgICBzZXRDdXJyZW50UHJvamVjdChwcm9qZWN0KTtcbiAgICAgIHNldFByb2plY3RzKHByZXYgPT4gW3Byb2plY3QsIC4uLnByZXZdKTtcbiAgICB9XG5cbiAgICBzZXRJc1J1bm5pbmcodHJ1ZSk7XG4gICAgc2V0U3lzdGVtU3RhdHVzKCdBQ1RJVkUnKTtcbiAgICBzZXRXb3JrZmxvd0Vycm9yKG51bGwpO1xuICAgIFxuICAgIHRyeSB7XG4gICAgICBjb25zdCB2YWxpZGF0aW9uID0gdmFsaWRhdGVUZWFtQ29uZmlndXJhdGlvbigpO1xuICAgICAgaWYgKCF2YWxpZGF0aW9uLmlzVmFsaWQpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBDb25maWd1cmF0aW9uIGVycm9yczogJHt2YWxpZGF0aW9uLmlzc3Vlcy5qb2luKCcsICcpfWApO1xuICAgICAgfVxuXG4gICAgICBjb25zdCB1cGRhdGVkUHJvamVjdCA9IHsgLi4uY3VycmVudFByb2plY3QhLCBzdGF0dXM6ICdpbl9wcm9ncmVzcycgYXMgY29uc3QgfTtcbiAgICAgIHNldEN1cnJlbnRQcm9qZWN0KHVwZGF0ZWRQcm9qZWN0KTtcblxuICAgICAgLy8gU2ltdWxhdGUgcmVhbGlzdGljIHdvcmtmbG93IGV4ZWN1dGlvbiB3aXRoIGVuaGFuY2VkIFVJIHVwZGF0ZXNcbiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdXBkYXRlZFByb2plY3Quc3RlcHMubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgY29uc3Qgc3RlcCA9IHVwZGF0ZWRQcm9qZWN0LnN0ZXBzW2ldO1xuICAgICAgICBzdGVwLnN0YXR1cyA9ICdydW5uaW5nJztcbiAgICAgICAgc2V0U3lzdGVtU3RhdHVzKGBFWEVDVVRJTkc6ICR7c3RlcC5hZ2VudC50b1VwcGVyQ2FzZSgpfWApO1xuICAgICAgICBcbiAgICAgICAgc2V0Q3VycmVudFByb2plY3QoeyAuLi51cGRhdGVkUHJvamVjdCB9KTtcblxuICAgICAgICBjb25zdCBleGVjdXRpb25UaW1lcyA9IHtcbiAgICAgICAgICAnUmVzZWFyY2ggQWdlbnQnOiA0NTAwMCxcbiAgICAgICAgICAnU0VPIFN0cmF0ZWdpc3QnOiAzMDAwMCxcbiAgICAgICAgICAnQ29udGVudCBBcmNoaXRlY3QnOiAyNTAwMCxcbiAgICAgICAgICAnQ29udGVudCBDcmVhdG9yJzogMTIwMDAwLFxuICAgICAgICAgICdRdWFsaXR5IEVkaXRvcic6IDQwMDAwXG4gICAgICAgIH07XG5cbiAgICAgICAgY29uc3Qgc3RlcFRpbWUgPSBleGVjdXRpb25UaW1lc1tzdGVwLmFnZW50IGFzIGtleW9mIHR5cGVvZiBleGVjdXRpb25UaW1lc10gfHwgMzAwMDA7XG4gICAgICAgIGNvbnN0IHByb2dyZXNzU3RlcHMgPSAyMDtcbiAgICAgICAgY29uc3QgaW50ZXJ2YWxUaW1lID0gc3RlcFRpbWUgLyBwcm9ncmVzc1N0ZXBzO1xuXG4gICAgICAgIGZvciAobGV0IHByb2dyZXNzID0gMDsgcHJvZ3Jlc3MgPD0gMTAwOyBwcm9ncmVzcyArPSA1KSB7XG4gICAgICAgICAgc3RlcC5wcm9ncmVzcyA9IHByb2dyZXNzO1xuICAgICAgICAgIHNldFBvd2VyTGV2ZWwoTWF0aC5tYXgoNzAsIDEwMCAtIChwcm9ncmVzcyAqIDAuMykpKTtcbiAgICAgICAgICBzZXRDdXJyZW50UHJvamVjdCh7IC4uLnVwZGF0ZWRQcm9qZWN0IH0pO1xuICAgICAgICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCBpbnRlcnZhbFRpbWUgLyAyMCkpO1xuICAgICAgICB9XG5cbiAgICAgICAgc3RlcC5zdGF0dXMgPSAnY29tcGxldGVkJztcbiAgICAgICAgXG4gICAgICAgIGNvbnN0IGNvc3RzID0ge1xuICAgICAgICAgICdSZXNlYXJjaCBBZ2VudCc6IDAuMDgsXG4gICAgICAgICAgJ1NFTyBTdHJhdGVnaXN0JzogMC4wNSxcbiAgICAgICAgICAnQ29udGVudCBBcmNoaXRlY3QnOiAwLjA0LFxuICAgICAgICAgICdDb250ZW50IENyZWF0b3InOiAwLjI1LFxuICAgICAgICAgICdRdWFsaXR5IEVkaXRvcic6IDAuMDZcbiAgICAgICAgfTtcbiAgICAgICAgXG4gICAgICAgIHN0ZXAuY29zdCA9IGNvc3RzW3N0ZXAuYWdlbnQgYXMga2V5b2YgdHlwZW9mIGNvc3RzXSB8fCAwLjA1O1xuICAgICAgICBzdGVwLmR1cmF0aW9uID0gc3RlcFRpbWUgLyAxMDAwO1xuICAgICAgICBcbiAgICAgICAgaWYgKHN0ZXAuYWdlbnQgPT09ICdDb250ZW50IENyZWF0b3InKSB7XG4gICAgICAgICAgc3RlcC5vdXRwdXQgPSBnZW5lcmF0ZVNhbXBsZUNvbnRlbnQodXBkYXRlZFByb2plY3QudG9waWMpO1xuICAgICAgICAgIHNldFByZXZpZXdDb250ZW50KHN0ZXAub3V0cHV0KTtcbiAgICAgICAgfVxuICAgICAgICBcbiAgICAgICAgc2V0VG90YWxDb3N0KHByZXYgPT4gcHJldiArIChzdGVwLmNvc3QgfHwgMCkpO1xuICAgICAgICBzZXRQb3dlckxldmVsKDEwMCk7XG4gICAgICB9XG5cbiAgICAgIHVwZGF0ZWRQcm9qZWN0LnN0YXR1cyA9ICdjb21wbGV0ZWQnO1xuICAgICAgc2V0Q3VycmVudFByb2plY3QodXBkYXRlZFByb2plY3QpO1xuICAgICAgc2V0UHJvamVjdHMocHJldiA9PiBwcmV2Lm1hcChwID0+IHAuaWQgPT09IHVwZGF0ZWRQcm9qZWN0LmlkID8gdXBkYXRlZFByb2plY3QgOiBwKSk7XG4gICAgICBzZXRTeXN0ZW1TdGF0dXMoJ0NPTVBMRVRFRCcpO1xuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1dvcmtmbG93IGV4ZWN1dGlvbiBmYWlsZWQ6JywgZXJyb3IpO1xuICAgICAgc2V0V29ya2Zsb3dFcnJvcihlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICdVbmtub3duIGVycm9yIG9jY3VycmVkJyk7XG4gICAgICBzZXRTeXN0ZW1TdGF0dXMoJ0VSUk9SJyk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzUnVubmluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGdlbmVyYXRlU2FtcGxlQ29udGVudCA9ICh0b3BpYzogc3RyaW5nKSA9PiB7XG4gICAgcmV0dXJuIGAjICR7dG9waWN9XG5cbiMjIEludHJvZHVjdGlvblxuXG5UaGlzIGNvbXByZWhlbnNpdmUgZ3VpZGUgZXhwbG9yZXMgJHt0b3BpY30gYW5kIHByb3ZpZGVzIHZhbHVhYmxlIGluc2lnaHRzIGZvciByZWFkZXJzIHNlZWtpbmcgdG8gdW5kZXJzdGFuZCB0aGlzIGltcG9ydGFudCBzdWJqZWN0LlxuXG4jIyBLZXkgUG9pbnRzXG5cbi0gKipSZXNlYXJjaC1iYWNrZWQgaW5zaWdodHMqKjogT3VyIGFuYWx5c2lzIGlzIGJhc2VkIG9uIHRoZSBsYXRlc3QgaW5kdXN0cnkgcmVzZWFyY2ggYW5kIGV4cGVydCBvcGluaW9uc1xuLSAqKlByYWN0aWNhbCBhcHBsaWNhdGlvbnMqKjogUmVhbC13b3JsZCBleGFtcGxlcyBhbmQgYWN0aW9uYWJsZSBzdHJhdGVnaWVzXG4tICoqRnV0dXJlIHRyZW5kcyoqOiBXaGF0IHRvIGV4cGVjdCBpbiB0aGUgZXZvbHZpbmcgbGFuZHNjYXBlXG5cbiMjIERldGFpbGVkIEFuYWx5c2lzXG5cbkxvcmVtIGlwc3VtIGRvbG9yIHNpdCBhbWV0LCBjb25zZWN0ZXR1ciBhZGlwaXNjaW5nIGVsaXQuIFNlZCBkbyBlaXVzbW9kIHRlbXBvciBpbmNpZGlkdW50IHV0IGxhYm9yZSBldCBkb2xvcmUgbWFnbmEgYWxpcXVhLiBVdCBlbmltIGFkIG1pbmltIHZlbmlhbSwgcXVpcyBub3N0cnVkIGV4ZXJjaXRhdGlvbiB1bGxhbWNvIGxhYm9yaXMuXG5cbiMjIyBTZWN0aW9uIDE6IFVuZGVyc3RhbmRpbmcgdGhlIEJhc2ljc1xuXG5EZXRhaWxlZCBleHBsYW5hdGlvbiBvZiBmdW5kYW1lbnRhbCBjb25jZXB0cyBhbmQgcHJpbmNpcGxlcy5cblxuIyMjIFNlY3Rpb24gMjogQWR2YW5jZWQgU3RyYXRlZ2llcyAgXG5cbk1vcmUgc29waGlzdGljYXRlZCBhcHByb2FjaGVzIGFuZCBtZXRob2RvbG9naWVzLlxuXG4jIyBDb25jbHVzaW9uXG5cblRoaXMgY29udGVudCBoYXMgYmVlbiBvcHRpbWl6ZWQgZm9yIHNlYXJjaCBlbmdpbmVzIHdoaWxlIG1haW50YWluaW5nIGhpZ2ggcmVhZGFiaWxpdHkgYW5kIGVuZ2FnZW1lbnQgZm9yIGh1bWFuIHJlYWRlcnMuXG5cbipHZW5lcmF0ZWQgYnkgSW52aW5jaWJsZSBBSSBDb250ZW50IFRlYW0gLSBQb3dlcmVkIGJ5IEthaWJhbkpTICYgS2ltaSBLMipgO1xuICB9O1xuXG4gIGNvbnN0IGV4cG9ydENvbnRlbnQgPSAoZm9ybWF0OiAnbWQnIHwgJ2h0bWwnIHwgJ3R4dCcpID0+IHtcbiAgICBpZiAoIXByZXZpZXdDb250ZW50KSB7XG4gICAgICBhbGVydCgnTm8gY29udGVudCB0byBleHBvcnQuIFBsZWFzZSBnZW5lcmF0ZSBjb250ZW50IGZpcnN0LicpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIGxldCBjb250ZW50ID0gJyc7XG4gICAgbGV0IGZpbGVuYW1lID0gJyc7XG4gICAgbGV0IG1pbWVUeXBlID0gJyc7XG5cbiAgICBzd2l0Y2ggKGZvcm1hdCkge1xuICAgICAgY2FzZSAnbWQnOlxuICAgICAgICBjb250ZW50ID0gcHJldmlld0NvbnRlbnQ7XG4gICAgICAgIGZpbGVuYW1lID0gYCR7dG9waWM/LnJlcGxhY2UoL1xccysvZywgJy0nKS50b0xvd2VyQ2FzZSgpIHx8ICdjb250ZW50J30ubWRgO1xuICAgICAgICBtaW1lVHlwZSA9ICd0ZXh0L21hcmtkb3duJztcbiAgICAgICAgYnJlYWs7XG4gICAgICBjYXNlICdodG1sJzpcbiAgICAgICAgY29udGVudCA9IGA8IURPQ1RZUEUgaHRtbD5cbjxodG1sIGxhbmc9XCJlblwiPlxuPGhlYWQ+XG4gICAgPG1ldGEgY2hhcnNldD1cIlVURi04XCI+XG4gICAgPG1ldGEgbmFtZT1cInZpZXdwb3J0XCIgY29udGVudD1cIndpZHRoPWRldmljZS13aWR0aCwgaW5pdGlhbC1zY2FsZT0xLjBcIj5cbiAgICA8dGl0bGU+JHt0b3BpYyB8fCAnR2VuZXJhdGVkIENvbnRlbnQnfTwvdGl0bGU+XG4gICAgPHN0eWxlPlxuICAgICAgICBib2R5IHsgZm9udC1mYW1pbHk6IC1hcHBsZS1zeXN0ZW0sIEJsaW5rTWFjU3lzdGVtRm9udCwgJ1NlZ29lIFVJJywgUm9ib3RvLCBzYW5zLXNlcmlmOyBsaW5lLWhlaWdodDogMS42OyBtYXgtd2lkdGg6IDgwMHB4OyBtYXJnaW46IDAgYXV0bzsgcGFkZGluZzogMjBweDsgfVxuICAgICAgICBoMSwgaDIsIGgzIHsgY29sb3I6ICMzMzM7IH1cbiAgICAgICAgcCB7IG1hcmdpbi1ib3R0b206IDE2cHg7IH1cbiAgICAgICAgdWwsIG9sIHsgbWFyZ2luLWJvdHRvbTogMTZweDsgcGFkZGluZy1sZWZ0OiAyMHB4OyB9XG4gICAgPC9zdHlsZT5cbjwvaGVhZD5cbjxib2R5PlxuICAgICR7cHJldmlld0NvbnRlbnQucmVwbGFjZSgvXFxuL2csICc8YnI+Jyl9XG48L2JvZHk+XG48L2h0bWw+YDtcbiAgICAgICAgZmlsZW5hbWUgPSBgJHt0b3BpYz8ucmVwbGFjZSgvXFxzKy9nLCAnLScpLnRvTG93ZXJDYXNlKCkgfHwgJ2NvbnRlbnQnfS5odG1sYDtcbiAgICAgICAgbWltZVR5cGUgPSAndGV4dC9odG1sJztcbiAgICAgICAgYnJlYWs7XG4gICAgICBjYXNlICd0eHQnOlxuICAgICAgICBjb250ZW50ID0gcHJldmlld0NvbnRlbnQucmVwbGFjZSgvWyMqYF0vZywgJycpLnJlcGxhY2UoL1xcbisvZywgJ1xcbicpO1xuICAgICAgICBmaWxlbmFtZSA9IGAke3RvcGljPy5yZXBsYWNlKC9cXHMrL2csICctJykudG9Mb3dlckNhc2UoKSB8fCAnY29udGVudCd9LnR4dGA7XG4gICAgICAgIG1pbWVUeXBlID0gJ3RleHQvcGxhaW4nO1xuICAgICAgICBicmVhaztcbiAgICB9XG5cbiAgICBjb25zdCBibG9iID0gbmV3IEJsb2IoW2NvbnRlbnRdLCB7IHR5cGU6IG1pbWVUeXBlIH0pO1xuICAgIGNvbnN0IHVybCA9IFVSTC5jcmVhdGVPYmplY3RVUkwoYmxvYik7XG4gICAgY29uc3QgYSA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2EnKTtcbiAgICBhLmhyZWYgPSB1cmw7XG4gICAgYS5kb3dubG9hZCA9IGZpbGVuYW1lO1xuICAgIGRvY3VtZW50LmJvZHkuYXBwZW5kQ2hpbGQoYSk7XG4gICAgYS5jbGljaygpO1xuICAgIGRvY3VtZW50LmJvZHkucmVtb3ZlQ2hpbGQoYSk7XG4gICAgVVJMLnJldm9rZU9iamVjdFVSTCh1cmwpO1xuICB9O1xuXG4gIGNvbnN0IGNvcHlUb0NsaXBib2FyZCA9IGFzeW5jICgpID0+IHtcbiAgICBpZiAoIXByZXZpZXdDb250ZW50KSByZXR1cm47XG4gICAgXG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IG5hdmlnYXRvci5jbGlwYm9hcmQud3JpdGVUZXh0KHByZXZpZXdDb250ZW50KTtcbiAgICAgIGFsZXJ0KCdDb250ZW50IGNvcGllZCB0byBjbGlwYm9hcmQhJyk7XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gY29weSBjb250ZW50OicsIGVycik7XG4gICAgICBhbGVydCgnRmFpbGVkIHRvIGNvcHkgY29udGVudCB0byBjbGlwYm9hcmQnKTtcbiAgICB9XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmFkaWVudC10by1iciBmcm9tLXNsYXRlLTkwMCB2aWEtYmx1ZS05MDAgdG8tc2xhdGUtOTAwIHRleHQtd2hpdGVcIj5cbiAgICAgIHsvKiBFbmhhbmNlZCBIZWFkZXIgd2l0aCBJbnZpbmNpYmxlIFRoZW1lICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJib3JkZXItYiBib3JkZXItYmx1ZS01MDAvMzAgYmctc2xhdGUtOTAwLzgwIGJhY2tkcm9wLWJsdXItbGcgc3RpY2t5IHRvcC0wIHotNTBcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0byBweC02IHB5LTRcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICAgIDxTaGllbGQgY2xhc3NOYW1lPVwiaC0xMCB3LTEwIHRleHQtYmx1ZS00MDBcIiAvPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgLXRvcC0xIC1yaWdodC0xIHctNCBoLTQgYmctZ3JlZW4tNDAwIHJvdW5kZWQtZnVsbCBhbmltYXRlLXB1bHNlXCI+PC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsdWUtNDAwIHRvLWN5YW4tNDAwIGJnLWNsaXAtdGV4dCB0ZXh0LXRyYW5zcGFyZW50XCI+XG4gICAgICAgICAgICAgICAgICBJTlZJTkNJQkxFIEFJIFdSSVRFUlxuICAgICAgICAgICAgICAgIDwvaDE+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXNsYXRlLTQwMFwiPk11bHRpLUFnZW50IENvbnRlbnQgR2VuZXJhdGlvbiBTeXN0ZW08L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIHsvKiBTeXN0ZW0gU3RhdHVzICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtYmx1ZS00MDBcIj5TWVNURU0gU1RBVFVTPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtc2xhdGUtMzAwXCI+e3N5c3RlbVN0YXR1c308L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgdy0xNiBoLTE2XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIHJvdW5kZWQtZnVsbCBiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS01MDAgdG8tY3lhbi01MDAgYW5pbWF0ZS1wdWxzZVwiPjwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMSByb3VuZGVkLWZ1bGwgYmctc2xhdGUtOTAwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIGZvbnQtYm9sZCB0ZXh0LWN5YW4tNDAwXCI+e3Bvd2VyTGV2ZWx9JTwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0byBweC02IHB5LThcIj5cbiAgICAgICAgey8qIEVuaGFuY2VkIFRhYiBOYXZpZ2F0aW9uICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0xIGJnLXNsYXRlLTgwMC81MCBwLTIgcm91bmRlZC14bCBtYi04IGJhY2tkcm9wLWJsdXItc21cIj5cbiAgICAgICAgICB7W1xuICAgICAgICAgICAgeyBpZDogJ2NvbW1hbmQtY2VudGVyJywgbGFiZWw6ICdDb21tYW5kIENlbnRlcicsIGljb246IFphcCB9LFxuICAgICAgICAgICAgeyBpZDogJ21pc3Npb24tY29uZmlnJywgbGFiZWw6ICdNaXNzaW9uIENvbmZpZycsIGljb246IFNldHRpbmdzIH0sXG4gICAgICAgICAgICB7IGlkOiAnY29udGVudC1wcmV2aWV3JywgbGFiZWw6ICdDb250ZW50IFByZXZpZXcnLCBpY29uOiBFeWUgfSxcbiAgICAgICAgICAgIHsgaWQ6ICdhbmFseXRpY3MnLCBsYWJlbDogJ0FuYWx5dGljcycsIGljb246IEJhckNoYXJ0MyB9XG4gICAgICAgICAgXS5tYXAoKHsgaWQsIGxhYmVsLCBpY29uOiBJY29uIH0pID0+IChcbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAga2V5PXtpZH1cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0QWN0aXZlVGFiKGlkKX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIHB4LTQgcHktMyByb3VuZGVkLWxnIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCAke1xuICAgICAgICAgICAgICAgIGFjdGl2ZVRhYiA9PT0gaWRcbiAgICAgICAgICAgICAgICAgID8gJ2JnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTUwMCB0by1jeWFuLTUwMCB0ZXh0LXdoaXRlIHNoYWRvdy1sZydcbiAgICAgICAgICAgICAgICAgIDogJ3RleHQtc2xhdGUtNDAwIGhvdmVyOnRleHQtd2hpdGUgaG92ZXI6Ymctc2xhdGUtNzAwLzUwJ1xuICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPEljb24gY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgIDxzcGFuPntsYWJlbH08L3NwYW4+XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICApKX1cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIENvbW1hbmQgQ2VudGVyIFRhYiAqL31cbiAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ2NvbW1hbmQtY2VudGVyJyAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LThcIj5cbiAgICAgICAgICAgIHsvKiBNaXNzaW9uIENvbnRyb2wgUGFuZWwgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLWJyIGZyb20tc2xhdGUtODAwLzUwIHRvLXNsYXRlLTkwMC81MCByb3VuZGVkLTJ4bCBib3JkZXIgYm9yZGVyLWJsdWUtNTAwLzIwIGJhY2tkcm9wLWJsdXItc21cIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTYgYm9yZGVyLWIgYm9yZGVyLXNsYXRlLTcwMC81MFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zIG1iLTRcIj5cbiAgICAgICAgICAgICAgICAgIDxDcHUgY2xhc3NOYW1lPVwiaC02IHctNiB0ZXh0LWJsdWUtNDAwXCIgLz5cbiAgICAgICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZFwiPk1pc3Npb24gQ29udHJvbDwvaDI+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17c3RhcnRDb250ZW50R2VuZXJhdGlvbn1cbiAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzUnVubmluZyB8fCAhdG9waWMudHJpbSgpfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgcHgtNiBweS0zIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1ncmVlbi01MDAgdG8tZW1lcmFsZC01MDAgaG92ZXI6ZnJvbS1ncmVlbi02MDAgaG92ZXI6dG8tZW1lcmFsZC02MDAgZGlzYWJsZWQ6ZnJvbS1zbGF0ZS02MDAgZGlzYWJsZWQ6dG8tc2xhdGUtNzAwIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCByb3VuZGVkLWxnIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBzaGFkb3ctbGcgaG92ZXI6c2hhZG93LXhsXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPFBsYXkgY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuPntpc1J1bm5pbmcgPyAnRVhFQ1VUSU5HLi4uJyA6ICdJTklUSUFURSBNSVNTSU9OJ308L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIDxidXR0b24gXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e3N0b3BHZW5lcmF0aW9ufVxuICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17IWlzUnVubmluZ31cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIHB4LTYgcHktMyBiZy1ncmFkaWVudC10by1yIGZyb20tcmVkLTUwMCB0by1yb3NlLTUwMCBob3Zlcjpmcm9tLXJlZC02MDAgaG92ZXI6dG8tcm9zZS02MDAgZGlzYWJsZWQ6ZnJvbS1zbGF0ZS02MDAgZGlzYWJsZWQ6dG8tc2xhdGUtNzAwIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCByb3VuZGVkLWxnIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMFwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxTcXVhcmUgY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuPkFCT1JUPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICA8YnV0dG9uIFxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtyZXNldFdvcmtmbG93fVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgcHgtNiBweS0zIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTUwMCB0by1jeWFuLTUwMCBob3Zlcjpmcm9tLWJsdWUtNjAwIGhvdmVyOnRvLWN5YW4tNjAwIHJvdW5kZWQtbGcgZm9udC1tZWRpdW0gdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPFJlZnJlc2hDdyBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4+UkVTRVQ8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICB7LyogTWlzc2lvbiBQcm9ncmVzcyAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTZcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi00XCI+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICAgIHt0b3BpYyB8fCAnTm8gYWN0aXZlIG1pc3Npb24nfVxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BweC0zIHB5LTEgcm91bmRlZC1mdWxsIHRleHQtc20gZm9udC1tZWRpdW0gJHtcbiAgICAgICAgICAgICAgICAgICAgY3VycmVudFRhc2suc3RhdHVzID09PSAnY29tcGxldGVkJyA/ICdiZy1ncmVlbi01MDAvMjAgdGV4dC1ncmVlbi00MDAnIDogXG4gICAgICAgICAgICAgICAgICAgIGN1cnJlbnRUYXNrLnN0YXR1cyA9PT0gJ2luX3Byb2dyZXNzJyA/ICdiZy1ibHVlLTUwMC8yMCB0ZXh0LWJsdWUtNDAwJyA6XG4gICAgICAgICAgICAgICAgICAgICdiZy1zbGF0ZS01MDAvMjAgdGV4dC1zbGF0ZS00MDAnXG4gICAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICAgIHtjdXJyZW50VGFzay5zdGF0dXMudG9VcHBlckNhc2UoKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgaC0zIGJnLXNsYXRlLTcwMCByb3VuZGVkLWZ1bGwgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IFxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC15LTAgbGVmdC0wIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTUwMCB0by1jeWFuLTUwMCByb3VuZGVkLWZ1bGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tNTAwXCJcbiAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgd2lkdGg6IGAke2N1cnJlbnRUYXNrLnByb2dyZXNzfSVgIH19XG4gICAgICAgICAgICAgICAgICA+PC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctZ3JhZGllbnQtdG8tciBmcm9tLXRyYW5zcGFyZW50IHZpYS13aGl0ZS8xMCB0by10cmFuc3BhcmVudCBhbmltYXRlLXB1bHNlXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiB0ZXh0LXNtIHRleHQtc2xhdGUtNDAwIG10LTJcIj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuPntNYXRoLnJvdW5kKGN1cnJlbnRUYXNrLnByb2dyZXNzKX0lIENvbXBsZXRlPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPHNwYW4+TWlzc2lvbiBDb3N0OiAke3RvdGFsQ29zdC50b0ZpeGVkKDMpfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIEFnZW50IFN0YXR1cyBHcmlkICovfVxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTMgbWItNlwiPlxuICAgICAgICAgICAgICAgIDxVc2VycyBjbGFzc05hbWU9XCJoLTYgdy02IHRleHQtYmx1ZS00MDBcIiAvPlxuICAgICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZFwiPkFnZW50IFNxdWFkcm9uIFN0YXR1czwvaDI+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy0zIGdhcC02XCI+XG4gICAgICAgICAgICAgICAge2FnZW50cy5tYXAoKGFnZW50KSA9PiB7XG4gICAgICAgICAgICAgICAgICBjb25zdCBJY29uID0gYWdlbnRJY29uc1thZ2VudC5pZF07XG4gICAgICAgICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGtleT17YWdlbnQuaWR9IGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLWJyIGZyb20tc2xhdGUtODAwLzUwIHRvLXNsYXRlLTkwMC81MCByb3VuZGVkLXhsIGJvcmRlciBib3JkZXItc2xhdGUtNzAwLzUwIHAtNiBiYWNrZHJvcC1ibHVyLXNtIGhvdmVyOmJvcmRlci1ibHVlLTUwMC8zMCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMyBtYi00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHAtMyByb3VuZGVkLWxnICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGFnZW50LnN0YXR1cyA9PT0gJ3J1bm5pbmcnID8gJ2JnLWJsdWUtNTAwLzIwIHRleHQtYmx1ZS00MDAnIDpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYWdlbnQuc3RhdHVzID09PSAnY29tcGxldGVkJyA/ICdiZy1ncmVlbi01MDAvMjAgdGV4dC1ncmVlbi00MDAnIDpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYWdlbnQuc3RhdHVzID09PSAnZXJyb3InID8gJ2JnLXJlZC01MDAvMjAgdGV4dC1yZWQtNDAwJyA6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICdiZy1zbGF0ZS01MDAvMjAgdGV4dC1zbGF0ZS00MDAnXG4gICAgICAgICAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxJY29uIGNsYXNzTmFtZT1cImgtNiB3LTZcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwiZm9udC1ib2xkIHRleHQtc21cIj57YWdlbnQubmFtZX08L2gzPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtc2xhdGUtNDAwXCI+e2FnZW50LnNwZWNpYWxBYmlsaXR5fTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgICAgIHsvKiBQb3dlciBMZXZlbCBCYXIgKi99XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIHRleHQteHMgdGV4dC1zbGF0ZS00MDAgbWItMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj5Qb3dlciBMZXZlbDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+e2FnZW50LnBvd2VyTGV2ZWx9JTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTIgYmctc2xhdGUtNzAwIHJvdW5kZWQtZnVsbCBvdmVyZmxvdy1oaWRkZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BoLWZ1bGwgcm91bmRlZC1mdWxsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTUwMCAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYWdlbnQucG93ZXJMZXZlbCA+PSA5NSA/ICdiZy1ncmFkaWVudC10by1yIGZyb20tZ3JlZW4tNDAwIHRvLWVtZXJhbGQtNDAwJyA6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhZ2VudC5wb3dlckxldmVsID49IDg1ID8gJ2JnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTQwMCB0by1jeWFuLTQwMCcgOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJ2JnLWdyYWRpZW50LXRvLXIgZnJvbS15ZWxsb3ctNDAwIHRvLW9yYW5nZS00MDAnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgd2lkdGg6IGAke2FnZW50LnBvd2VyTGV2ZWx9JWAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICAgICAgey8qIFByb2dyZXNzIEJhciAqL31cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtMSBiZy1zbGF0ZS03MDAgcm91bmRlZC1mdWxsIG92ZXJmbG93LWhpZGRlbiBtYi0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IFxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BoLWZ1bGwgcm91bmRlZC1mdWxsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFnZW50LnN0YXR1cyA9PT0gJ2NvbXBsZXRlZCcgPyAnYmctZ3JlZW4tNDAwJyA6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYWdlbnQuc3RhdHVzID09PSAncnVubmluZycgPyAnYmctYmx1ZS00MDAgYW5pbWF0ZS1wdWxzZScgOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICdiZy1zbGF0ZS02MDAnXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17eyB3aWR0aDogYCR7YWdlbnQucHJvZ3Jlc3N9JWAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgID48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIHRleHQteHNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YHB4LTIgcHktMSByb3VuZGVkLWZ1bGwgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYWdlbnQuc3RhdHVzID09PSAncnVubmluZycgPyAnYmctYmx1ZS01MDAvMjAgdGV4dC1ibHVlLTQwMCcgOlxuICAgICAgICAgICAgICAgICAgICAgICAgICBhZ2VudC5zdGF0dXMgPT09ICdjb21wbGV0ZWQnID8gJ2JnLWdyZWVuLTUwMC8yMCB0ZXh0LWdyZWVuLTQwMCcgOlxuICAgICAgICAgICAgICAgICAgICAgICAgICBhZ2VudC5zdGF0dXMgPT09ICdlcnJvcicgPyAnYmctcmVkLTUwMC8yMCB0ZXh0LXJlZC00MDAnIDpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgJ2JnLXNsYXRlLTUwMC8yMCB0ZXh0LXNsYXRlLTQwMCdcbiAgICAgICAgICAgICAgICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2FnZW50LnN0YXR1cy50b1VwcGVyQ2FzZSgpfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAge2FnZW50LmNvc3QgPiAwICYmIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc2xhdGUtNDAwXCI+JHthZ2VudC5jb3N0LnRvRml4ZWQoMyl9PC9zcGFuPn1cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgICAgIH0pfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuXG4gICAgICAgIHsvKiBNaXNzaW9uIENvbmZpZyBUYWIgKi99XG4gICAgICAgIHthY3RpdmVUYWIgPT09ICdtaXNzaW9uLWNvbmZpZycgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS04XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLWJyIGZyb20tc2xhdGUtODAwLzUwIHRvLXNsYXRlLTkwMC81MCByb3VuZGVkLTJ4bCBib3JkZXIgYm9yZGVyLWJsdWUtNTAwLzIwIGJhY2tkcm9wLWJsdXItc20gcC04XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zIG1iLTZcIj5cbiAgICAgICAgICAgICAgICA8QnJhaW4gY2xhc3NOYW1lPVwiaC02IHctNiB0ZXh0LWJsdWUtNDAwXCIgLz5cbiAgICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LWJvbGRcIj5NaXNzaW9uIENvbmZpZ3VyYXRpb248L2gyPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtOFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXNsYXRlLTMwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPFRhcmdldCBjbGFzc05hbWU9XCJoLTQgdy00IGlubGluZSBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICBQcmltYXJ5IFRhcmdldCAqXG4gICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDxpbnB1dCBcbiAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3RvcGljfVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0VG9waWMoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW50ZXIgeW91ciBjb250ZW50IG1pc3Npb24gb2JqZWN0aXZlLi4uXCJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtNCBweS0zIGJnLXNsYXRlLTgwMC81MCBib3JkZXIgYm9yZGVyLXNsYXRlLTYwMCByb3VuZGVkLWxnIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci1ibHVlLTUwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1zbGF0ZS0zMDAgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxHbG9iZSBjbGFzc05hbWU9XCJoLTQgdy00IGlubGluZSBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICBTRU8gS2V5d29yZHNcbiAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPGlucHV0IFxuICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17a2V5d29yZHN9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRLZXl3b3JkcyhlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJrZXl3b3JkMSwga2V5d29yZDIsIGtleXdvcmQzLi4uXCJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtNCBweS0zIGJnLXNsYXRlLTgwMC81MCBib3JkZXIgYm9yZGVyLXNsYXRlLTYwMCByb3VuZGVkLWxnIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci1ibHVlLTUwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1zbGF0ZS0zMDAgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxGaWxlVGV4dCBjbGFzc05hbWU9XCJoLTQgdy00IGlubGluZSBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICBNaXNzaW9uIFR5cGVcbiAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPHNlbGVjdCBcbiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Y29udGVudFR5cGV9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRDb250ZW50VHlwZShlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTQgcHktMyBiZy1zbGF0ZS04MDAvNTAgYm9yZGVyIGJvcmRlci1zbGF0ZS02MDAgcm91bmRlZC1sZyBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTUwMCBmb2N1czpib3JkZXItYmx1ZS01MDBcIlxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImJsb2ctcG9zdFwiPkJsb2cgUG9zdDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJhcnRpY2xlXCI+QXJ0aWNsZTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJsYW5kaW5nLXBhZ2VcIj5MYW5kaW5nIFBhZ2U8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwicHJvZHVjdC1kZXNjcmlwdGlvblwiPlByb2R1Y3QgRGVzY3JpcHRpb248L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwic29jaWFsLW1lZGlhXCI+U29jaWFsIE1lZGlhIFNlcmllczwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXNsYXRlLTMwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPFRyZW5kaW5nVXAgY2xhc3NOYW1lPVwiaC00IHctNCBpbmxpbmUgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgVGFyZ2V0IFNjb3BlOiB7dGFyZ2V0TGVuZ3RofSB3b3Jkc1xuICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwicmFuZ2VcIlxuICAgICAgICAgICAgICAgICAgICAgIG1pbj1cIjUwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgbWF4PVwiNTAwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgc3RlcD1cIjEwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3RhcmdldExlbmd0aH1cbiAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFRhcmdldExlbmd0aChwYXJzZUludChlLnRhcmdldC52YWx1ZSkpfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBoLTIgYmctc2xhdGUtNzAwIHJvdW5kZWQtbGcgYXBwZWFyYW5jZS1ub25lIGN1cnNvci1wb2ludGVyIHNsaWRlclwiXG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gdGV4dC14cyB0ZXh0LXNsYXRlLTQwMCBtdC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4+NTAwPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPjUwMDA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG5cbiAgICAgICAgey8qIENvbnRlbnQgUHJldmlldyBUYWIgKi99XG4gICAgICAgIHthY3RpdmVUYWIgPT09ICdjb250ZW50LXByZXZpZXcnICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktOFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmFkaWVudC10by1iciBmcm9tLXNsYXRlLTgwMC81MCB0by1zbGF0ZS05MDAvNTAgcm91bmRlZC0yeGwgYm9yZGVyIGJvcmRlci1ibHVlLTUwMC8yMCBiYWNrZHJvcC1ibHVyLXNtXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC02IGJvcmRlci1iIGJvcmRlci1zbGF0ZS03MDAvNTBcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgICAgICAgPFNwYXJrbGVzIGNsYXNzTmFtZT1cImgtNiB3LTYgdGV4dC1ibHVlLTQwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZFwiPkdlbmVyYXRlZCBDb250ZW50PC9oMj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAge3ByZXZpZXdDb250ZW50ICYmIChcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxidXR0b24gXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBleHBvcnRDb250ZW50KCdtZCcpfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNCBweS0yIGJnLXNsYXRlLTcwMCBob3ZlcjpiZy1zbGF0ZS02MDAgYm9yZGVyIGJvcmRlci1zbGF0ZS02MDAgcm91bmRlZC1sZyB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tY29sb3JzIGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiXG4gICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgPERvd25sb2FkIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+TUQ8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGV4cG9ydENvbnRlbnQoJ2h0bWwnKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTQgcHktMiBiZy1zbGF0ZS03MDAgaG92ZXI6Ymctc2xhdGUtNjAwIGJvcmRlciBib3JkZXItc2xhdGUtNjAwIHJvdW5kZWQtbGcgdGV4dC1zbSBmb250LW1lZGl1bSB0cmFuc2l0aW9uLWNvbG9ycyBmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIlxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxEb3dubG9hZCBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPkhUTUw8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2NvcHlUb0NsaXBib2FyZH1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTQgcHktMiBiZy1ibHVlLTYwMCBob3ZlcjpiZy1ibHVlLTcwMCByb3VuZGVkLWxnIHRleHQtc20gZm9udC1tZWRpdW0gdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIENvcHlcbiAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC02XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1zbGF0ZS05MDAvNTAgcm91bmRlZC14bCBib3JkZXIgYm9yZGVyLXNsYXRlLTcwMC81MCBwLTYgbWluLWgtWzQwMHB4XVwiPlxuICAgICAgICAgICAgICAgICAge3ByZXZpZXdDb250ZW50ID8gKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInByb3NlIHByb3NlLWludmVydCBwcm9zZS1ibHVlIG1heC13LW5vbmVcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8UmVhY3RNYXJrZG93bj57cHJldmlld0NvbnRlbnR9PC9SZWFjdE1hcmtkb3duPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgaC1mdWxsIHRleHQtc2xhdGUtNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPEV5ZSBjbGFzc05hbWU9XCJoLTE2IHctMTYgbXgtYXV0byBtYi00IG9wYWNpdHktNTBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1sZ1wiPkNvbnRlbnQgd2lsbCBtYXRlcmlhbGl6ZSBoZXJlIGFmdGVyIG1pc3Npb24gZXhlY3V0aW9uPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBtdC0yXCI+Q29uZmlndXJlIHlvdXIgbWlzc2lvbiBhbmQgaW5pdGlhdGUgdGhlIGNvbnRlbnQgZ2VuZXJhdGlvbiBwcm9jZXNzPC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG5cbiAgICAgICAgey8qIEFuYWx5dGljcyBUYWIgKi99XG4gICAgICAgIHthY3RpdmVUYWIgPT09ICdhbmFseXRpY3MnICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktOFwiPlxuICAgICAgICAgICAgey8qIEVycm9yIERpc3BsYXkgKi99XG4gICAgICAgICAgICB7d29ya2Zsb3dFcnJvciAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctcmVkLTUwMC8xMCBib3JkZXIgYm9yZGVyLXJlZC01MDAvMzAgcm91bmRlZC14bCBwLTZcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHRleHQtcmVkLTQwMFwiPlxuICAgICAgICAgICAgICAgICAgPEFjdGl2aXR5IGNsYXNzTmFtZT1cImgtNSB3LTUgbXItM1wiIC8+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPk1pc3Npb24gRXJyb3I6IDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTJcIj57d29ya2Zsb3dFcnJvcn08L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgey8qIFBlcmZvcm1hbmNlIE1ldHJpY3MgKi99XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMyBtYi02XCI+XG4gICAgICAgICAgICAgICAgPEF3YXJkIGNsYXNzTmFtZT1cImgtNiB3LTYgdGV4dC1ibHVlLTQwMFwiIC8+XG4gICAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1ib2xkXCI+UGVyZm9ybWFuY2UgTWV0cmljczwvaDI+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy00IGdhcC02XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmFkaWVudC10by1iciBmcm9tLWdyZWVuLTUwMC8xMCB0by1lbWVyYWxkLTUwMC8xMCBib3JkZXIgYm9yZGVyLWdyZWVuLTUwMC8yMCByb3VuZGVkLXhsIHAtNlwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgPERvbGxhclNpZ24gY2xhc3NOYW1lPVwiaC0xMCB3LTEwIHRleHQtZ3JlZW4tNDAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtc2xhdGUtNDAwXCI+TWlzc2lvbiBDb3N0PC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyZWVuLTQwMFwiPiR7dG90YWxDb3N0LnRvRml4ZWQoMyl9PC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmVlbi01MDBcIj44NS05NSUgc2F2aW5nczwvcD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLWJyIGZyb20tYmx1ZS01MDAvMTAgdG8tY3lhbi01MDAvMTAgYm9yZGVyIGJvcmRlci1ibHVlLTUwMC8yMCByb3VuZGVkLXhsIHAtNlwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgPENsb2NrIGNsYXNzTmFtZT1cImgtMTAgdy0xMCB0ZXh0LWJsdWUtNDAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtc2xhdGUtNDAwXCI+QXZnLiBHZW5lcmF0aW9uPC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWJsdWUtNDAwXCI+NC02bTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtYmx1ZS01MDBcIj5QZXIgbWlzc2lvbjwvcD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLWJyIGZyb20tcHVycGxlLTUwMC8xMCB0by1waW5rLTUwMC8xMCBib3JkZXIgYm9yZGVyLXB1cnBsZS01MDAvMjAgcm91bmRlZC14bCBwLTZcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxCYXJDaGFydDMgY2xhc3NOYW1lPVwiaC0xMCB3LTEwIHRleHQtcHVycGxlLTQwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXNsYXRlLTQwMFwiPlF1YWxpdHkgU2NvcmU8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtcHVycGxlLTQwMFwiPjkuMi8xMDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtcHVycGxlLTUwMFwiPkVRLUJlbmNoIHJhdGluZzwvcD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1jeWFuLTUwMC8xMCB0by10ZWFsLTUwMC8xMCBib3JkZXIgYm9yZGVyLWN5YW4tNTAwLzIwIHJvdW5kZWQteGwgcC02XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgICAgICAgICA8Q2hlY2tDaXJjbGUgY2xhc3NOYW1lPVwiaC0xMCB3LTEwIHRleHQtY3lhbi00MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1zbGF0ZS00MDBcIj5TdWNjZXNzIFJhdGU8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtY3lhbi00MDBcIj45OCU8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWN5YW4tNTAwXCI+TWlzc2lvbiBjb21wbGV0aW9uPC9wPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogVGVhbSBBbmFseXRpY3MgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLWJyIGZyb20tc2xhdGUtODAwLzUwIHRvLXNsYXRlLTkwMC81MCByb3VuZGVkLTJ4bCBib3JkZXIgYm9yZGVyLWJsdWUtNTAwLzIwIGJhY2tkcm9wLWJsdXItc20gcC04XCI+XG4gICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCBtYi02XCI+QWdlbnQgU3F1YWRyb24gQW5hbHl0aWNzPC9oMj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICB7dGVhbUFuYWx5dGljcy5hZ2VudHMubWFwKChhZ2VudCwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtpbmRleH0gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHAtNiBiZy1zbGF0ZS04MDAvMzAgYm9yZGVyIGJvcmRlci1zbGF0ZS03MDAvNTAgcm91bmRlZC14bFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0zIGgtMyByb3VuZGVkLWZ1bGwgYmctZ3JlZW4tNDAwIGFuaW1hdGUtcHVsc2VcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cImZvbnQtYm9sZCB0ZXh0LXdoaXRlXCI+e2FnZW50Lm5hbWV9PC9oMz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1zbGF0ZS00MDBcIj57YWdlbnQuc3BlY2lhbGl6YXRpb259PC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXNsYXRlLTUwMFwiPlRvb2xzOiB7YWdlbnQudG9vbHMuam9pbignLCAnKX08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtcmlnaHRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtYmx1ZS00MDBcIj57YWdlbnQucGVyZm9ybWFuY2V9PC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1zbGF0ZS00MDBcIj57YWdlbnQuYXZnRXhlY3V0aW9uVGltZX08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgbXQtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXNsYXRlLTUwMFwiPlBvd2VyOjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgZm9udC1ib2xkIHRleHQtZ3JlZW4tNDAwXCI+e2FnZW50LnBvd2VyTGV2ZWx9JTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufTsiXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsIlBsYXkiLCJTcXVhcmUiLCJEb3dubG9hZCIsIkV5ZSIsIlNldHRpbmdzIiwiQmFyQ2hhcnQzIiwiRmlsZVRleHQiLCJTZWFyY2giLCJUYXJnZXQiLCJQZW5Ub29sIiwiQ2hlY2tDaXJjbGUiLCJDbG9jayIsIkRvbGxhclNpZ24iLCJaYXAiLCJSZWZyZXNoQ3ciLCJTaGllbGQiLCJDcHUiLCJCcmFpbiIsIkdsb2JlIiwiVHJlbmRpbmdVcCIsIlNwYXJrbGVzIiwiVXNlcnMiLCJBd2FyZCIsIkFjdGl2aXR5IiwiUmVhY3RNYXJrZG93biIsImdldFRlYW1BbmFseXRpY3MiLCJhZ2VudHMiLCJuYW1lIiwic3BlY2lhbGl6YXRpb24iLCJ0b29scyIsInBlcmZvcm1hbmNlIiwiYXZnRXhlY3V0aW9uVGltZSIsInBvd2VyTGV2ZWwiLCJzcGVjaWFsQWJpbGl0eSIsImNhcGFiaWxpdGllcyIsImNvbnRlbnRfdHlwZXMiLCJ3b3JkX2NvdW50X3JhbmdlIiwic2VvX29wdGltaXphdGlvbiIsImZhY3RfY2hlY2tpbmciLCJicmFuZF92b2ljZSIsIndvcmtmbG93cyIsInRhc2tzIiwiZXN0aW1hdGVkVGltZSIsImNvc3RFc3RpbWF0ZSIsInN1Y2Nlc3NfcmF0ZSIsInZhbGlkYXRlVGVhbUNvbmZpZ3VyYXRpb24iLCJpc1ZhbGlkIiwiaXNzdWVzIiwiSW52aW5jaWJsZUNvbnRlbnRXcml0ZXIiLCJwcm9qZWN0cyIsInNldFByb2plY3RzIiwiY3VycmVudFByb2plY3QiLCJzZXRDdXJyZW50UHJvamVjdCIsIm5ld1Byb2plY3RUb3BpYyIsInNldE5ld1Byb2plY3RUb3BpYyIsInRhcmdldEtleXdvcmRzIiwic2V0VGFyZ2V0S2V5d29yZHMiLCJjb250ZW50VHlwZSIsInNldENvbnRlbnRUeXBlIiwid29yZENvdW50Iiwic2V0V29yZENvdW50IiwiaXNSdW5uaW5nIiwic2V0SXNSdW5uaW5nIiwidG90YWxDb3N0Iiwic2V0VG90YWxDb3N0IiwicHJldmlld0NvbnRlbnQiLCJzZXRQcmV2aWV3Q29udGVudCIsInRlYW1BbmFseXRpY3MiLCJzZXRUZWFtQW5hbHl0aWNzIiwid29ya2Zsb3dFcnJvciIsInNldFdvcmtmbG93RXJyb3IiLCJhY3RpdmVUYWIiLCJzZXRBY3RpdmVUYWIiLCJ0b3BpYyIsInNldFRvcGljIiwia2V5d29yZHMiLCJzZXRLZXl3b3JkcyIsInRhcmdldExlbmd0aCIsInNldFRhcmdldExlbmd0aCIsInN5c3RlbVN0YXR1cyIsInNldFN5c3RlbVN0YXR1cyIsInNldFBvd2VyTGV2ZWwiLCJnZXRPdmVyYWxsUHJvZ3Jlc3MiLCJzdGVwcyIsImxlbmd0aCIsImNvbXBsZXRlZFN0ZXBzIiwiZmlsdGVyIiwicyIsInN0YXR1cyIsImN1cnJlbnRUYXNrIiwicHJvZ3Jlc3MiLCJpZCIsImNvc3QiLCJhZ2VudEljb25zIiwic3RvcEdlbmVyYXRpb24iLCJyZXNldFdvcmtmbG93Iiwic3RhcnRDb250ZW50R2VuZXJhdGlvbiIsInRyaW0iLCJhbGVydCIsInByb2plY3QiLCJEYXRlIiwibm93IiwidG9TdHJpbmciLCJzcGxpdCIsIm1hcCIsImsiLCJCb29sZWFuIiwiY3JlYXRlZEF0IiwiYWdlbnQiLCJwcmV2IiwidmFsaWRhdGlvbiIsIkVycm9yIiwiam9pbiIsInVwZGF0ZWRQcm9qZWN0IiwiaSIsInN0ZXAiLCJ0b1VwcGVyQ2FzZSIsImV4ZWN1dGlvblRpbWVzIiwic3RlcFRpbWUiLCJwcm9ncmVzc1N0ZXBzIiwiaW50ZXJ2YWxUaW1lIiwiTWF0aCIsIm1heCIsIlByb21pc2UiLCJyZXNvbHZlIiwic2V0VGltZW91dCIsImNvc3RzIiwiZHVyYXRpb24iLCJvdXRwdXQiLCJnZW5lcmF0ZVNhbXBsZUNvbnRlbnQiLCJwIiwiZXJyb3IiLCJjb25zb2xlIiwibWVzc2FnZSIsImV4cG9ydENvbnRlbnQiLCJmb3JtYXQiLCJjb250ZW50IiwiZmlsZW5hbWUiLCJtaW1lVHlwZSIsInJlcGxhY2UiLCJ0b0xvd2VyQ2FzZSIsImJsb2IiLCJCbG9iIiwidHlwZSIsInVybCIsIlVSTCIsImNyZWF0ZU9iamVjdFVSTCIsImEiLCJkb2N1bWVudCIsImNyZWF0ZUVsZW1lbnQiLCJocmVmIiwiZG93bmxvYWQiLCJib2R5IiwiYXBwZW5kQ2hpbGQiLCJjbGljayIsInJlbW92ZUNoaWxkIiwicmV2b2tlT2JqZWN0VVJMIiwiY29weVRvQ2xpcGJvYXJkIiwibmF2aWdhdG9yIiwiY2xpcGJvYXJkIiwid3JpdGVUZXh0IiwiZXJyIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDEiLCJzcGFuIiwibGFiZWwiLCJpY29uIiwiSWNvbiIsImJ1dHRvbiIsIm9uQ2xpY2siLCJoMiIsImRpc2FibGVkIiwic3R5bGUiLCJ3aWR0aCIsInJvdW5kIiwidG9GaXhlZCIsImgzIiwiaW5wdXQiLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwiZSIsInRhcmdldCIsInBsYWNlaG9sZGVyIiwic2VsZWN0Iiwib3B0aW9uIiwibWluIiwicGFyc2VJbnQiLCJpbmRleCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/content-writer/page.tsx\n"));

/***/ })

});