"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/content-writer/page",{

/***/ "(app-pages-browser)/./src/app/content-writer/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/content-writer/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ InvincibleContentWriter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,CheckCircle,Clock,DollarSign,Download,Eye,FileText,PenTool,Play,Search,Settings,Sparkles,Square,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,CheckCircle,Clock,DollarSign,Download,Eye,FileText,PenTool,Play,Search,Settings,Sparkles,Square,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,CheckCircle,Clock,DollarSign,Download,Eye,FileText,PenTool,Play,Search,Settings,Sparkles,Square,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,CheckCircle,Clock,DollarSign,Download,Eye,FileText,PenTool,Play,Search,Settings,Sparkles,Square,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-tool.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,CheckCircle,Clock,DollarSign,Download,Eye,FileText,PenTool,Play,Search,Settings,Sparkles,Square,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,CheckCircle,Clock,DollarSign,Download,Eye,FileText,PenTool,Play,Search,Settings,Sparkles,Square,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,CheckCircle,Clock,DollarSign,Download,Eye,FileText,PenTool,Play,Search,Settings,Sparkles,Square,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,CheckCircle,Clock,DollarSign,Download,Eye,FileText,PenTool,Play,Search,Settings,Sparkles,Square,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,CheckCircle,Clock,DollarSign,Download,Eye,FileText,PenTool,Play,Search,Settings,Sparkles,Square,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,CheckCircle,Clock,DollarSign,Download,Eye,FileText,PenTool,Play,Search,Settings,Sparkles,Square,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,CheckCircle,Clock,DollarSign,Download,Eye,FileText,PenTool,Play,Search,Settings,Sparkles,Square,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,CheckCircle,Clock,DollarSign,Download,Eye,FileText,PenTool,Play,Search,Settings,Sparkles,Square,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,CheckCircle,Clock,DollarSign,Download,Eye,FileText,PenTool,Play,Search,Settings,Sparkles,Square,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,CheckCircle,Clock,DollarSign,Download,Eye,FileText,PenTool,Play,Search,Settings,Sparkles,Square,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,CheckCircle,Clock,DollarSign,Download,Eye,FileText,PenTool,Play,Search,Settings,Sparkles,Square,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,CheckCircle,Clock,DollarSign,Download,Eye,FileText,PenTool,Play,Search,Settings,Sparkles,Square,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,CheckCircle,Clock,DollarSign,Download,Eye,FileText,PenTool,Play,Search,Settings,Sparkles,Square,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,CheckCircle,Clock,DollarSign,Download,Eye,FileText,PenTool,Play,Search,Settings,Sparkles,Square,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// Enhanced team analytics with Invincible theme\nconst getTeamAnalytics = ()=>({\n        agents: [\n            {\n                name: 'Research Agent',\n                specialization: 'Web Research & Data Collection',\n                tools: [\n                    'Tavily Search',\n                    'SERP Analysis'\n                ],\n                performance: 'Excellent',\n                avgExecutionTime: '45s',\n                powerLevel: 95,\n                specialAbility: 'Deep Web Intelligence'\n            },\n            {\n                name: 'SEO Strategist',\n                specialization: 'Keyword Research & Strategy',\n                tools: [\n                    'Keyword Analysis',\n                    'Competition Research'\n                ],\n                performance: 'Very Good',\n                avgExecutionTime: '30s',\n                powerLevel: 88,\n                specialAbility: 'Search Domination'\n            },\n            {\n                name: 'Content Architect',\n                specialization: 'Content Structure & Planning',\n                tools: [\n                    'Outline Generator',\n                    'Content Planner'\n                ],\n                performance: 'Excellent',\n                avgExecutionTime: '25s',\n                powerLevel: 92,\n                specialAbility: 'Structure Mastery'\n            },\n            {\n                name: 'Content Creator',\n                specialization: 'AI-Powered Writing',\n                tools: [\n                    'Kimi K2 Model',\n                    'Content Generator'\n                ],\n                performance: 'Outstanding',\n                avgExecutionTime: '2m',\n                powerLevel: 98,\n                specialAbility: 'Creative Genesis'\n            },\n            {\n                name: 'Quality Editor',\n                specialization: 'Content Review & Optimization',\n                tools: [\n                    'Grammar Check',\n                    'SEO Optimization'\n                ],\n                performance: 'Excellent',\n                avgExecutionTime: '40s',\n                powerLevel: 90,\n                specialAbility: 'Perfection Protocol'\n            }\n        ],\n        capabilities: {\n            content_types: [\n                'Blog Posts',\n                'Articles',\n                'Landing Pages',\n                'Social Media',\n                'Email Campaigns',\n                'Product Descriptions'\n            ],\n            word_count_range: '500-5000',\n            seo_optimization: 'Real-time SEO scoring and recommendations',\n            fact_checking: 'Automated fact-checking with source verification',\n            brand_voice: 'Adaptive brand voice matching'\n        },\n        workflows: [\n            {\n                name: 'Standard Content Creation',\n                agents: 5,\n                tasks: 5,\n                estimatedTime: '4-6 minutes',\n                costEstimate: '$0.40-0.80',\n                success_rate: '98%'\n            },\n            {\n                name: 'SEO-Focused Content',\n                agents: 5,\n                tasks: 7,\n                estimatedTime: '6-8 minutes',\n                costEstimate: '$0.60-1.00',\n                success_rate: '96%'\n            },\n            {\n                name: 'Technical Content',\n                agents: 5,\n                tasks: 6,\n                estimatedTime: '8-12 minutes',\n                costEstimate: '$0.80-1.20',\n                success_rate: '94%'\n            }\n        ]\n    });\nconst validateTeamConfiguration = ()=>({\n        isValid: true,\n        issues: []\n    });\nfunction InvincibleContentWriter() {\n    _s();\n    const [projects, setProjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentProject, setCurrentProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [newProjectTopic, setNewProjectTopic] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [targetKeywords, setTargetKeywords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [contentType, setContentType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('blog-post');\n    const [wordCount, setWordCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(2000);\n    const [isRunning, setIsRunning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [totalCost, setTotalCost] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [previewContent, setPreviewContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [teamAnalytics, setTeamAnalytics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(getTeamAnalytics());\n    const [workflowError, setWorkflowError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('command-center');\n    const [topic, setTopic] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [keywords, setKeywords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [targetLength, setTargetLength] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(2000);\n    // Enhanced UI states\n    const [systemStatus, setSystemStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('READY');\n    const [powerLevel, setPowerLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(100);\n    // Mock current task data\n    const getOverallProgress = ()=>{\n        if (!currentProject || !currentProject.steps || currentProject.steps.length === 0) return 0;\n        const completedSteps = currentProject.steps.filter((s)=>s.status === 'completed').length;\n        return completedSteps / currentProject.steps.length * 100;\n    };\n    // Current task computed state\n    const currentTask = {\n        topic: (currentProject === null || currentProject === void 0 ? void 0 : currentProject.topic) || topic || '',\n        status: (currentProject === null || currentProject === void 0 ? void 0 : currentProject.status) || 'pending',\n        progress: getOverallProgress()\n    };\n    // Enhanced agents data with power levels\n    const [agents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 'Research Agent',\n            name: 'Research Agent',\n            status: 'idle',\n            progress: 0,\n            currentTask: '',\n            cost: 0,\n            powerLevel: 95,\n            specialAbility: 'Deep Web Intelligence'\n        },\n        {\n            id: 'SEO Strategist',\n            name: 'SEO Strategist',\n            status: 'idle',\n            progress: 0,\n            currentTask: '',\n            cost: 0,\n            powerLevel: 88,\n            specialAbility: 'Search Domination'\n        },\n        {\n            id: 'Content Architect',\n            name: 'Content Architect',\n            status: 'idle',\n            progress: 0,\n            currentTask: '',\n            cost: 0,\n            powerLevel: 92,\n            specialAbility: 'Structure Mastery'\n        },\n        {\n            id: 'Content Creator',\n            name: 'Content Creator',\n            status: 'idle',\n            progress: 0,\n            currentTask: '',\n            cost: 0,\n            powerLevel: 98,\n            specialAbility: 'Creative Genesis'\n        },\n        {\n            id: 'Quality Editor',\n            name: 'Quality Editor',\n            status: 'idle',\n            progress: 0,\n            currentTask: '',\n            cost: 0,\n            powerLevel: 90,\n            specialAbility: 'Perfection Protocol'\n        }\n    ]);\n    const agentIcons = {\n        'Research Agent': _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        'SEO Strategist': _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        'Content Architect': _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        'Content Creator': _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        'Quality Editor': _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    };\n    // Placeholder functions\n    const stopGeneration = ()=>{\n        setIsRunning(false);\n        setSystemStatus('STOPPED');\n    };\n    const resetWorkflow = ()=>{\n        setIsRunning(false);\n        setCurrentProject(null);\n        setTotalCost(0);\n        setPreviewContent('');\n        setWorkflowError(null);\n        setSystemStatus('READY');\n        setPowerLevel(100);\n    };\n    const startContentGeneration = async ()=>{\n        if (!topic.trim()) {\n            alert('Please enter a topic first');\n            return;\n        }\n        setSystemStatus('INITIALIZING');\n        setPowerLevel(95);\n        // Create a new project with steps\n        const project = {\n            id: Date.now().toString(),\n            topic: topic,\n            targetKeywords: keywords.split(',').map((k)=>k.trim()).filter(Boolean),\n            contentType,\n            wordCount: targetLength,\n            status: 'draft',\n            createdAt: new Date(),\n            steps: [\n                {\n                    id: '1',\n                    name: 'Research & Data Collection',\n                    status: 'pending',\n                    progress: 0,\n                    agent: 'Research Agent'\n                },\n                {\n                    id: '2',\n                    name: 'SEO Strategy & Keywords',\n                    status: 'pending',\n                    progress: 0,\n                    agent: 'SEO Strategist'\n                },\n                {\n                    id: '3',\n                    name: 'Content Architecture',\n                    status: 'pending',\n                    progress: 0,\n                    agent: 'Content Architect'\n                },\n                {\n                    id: '4',\n                    name: 'Content Creation',\n                    status: 'pending',\n                    progress: 0,\n                    agent: 'Content Creator'\n                },\n                {\n                    id: '5',\n                    name: 'Quality Review & Optimization',\n                    status: 'pending',\n                    progress: 0,\n                    agent: 'Quality Editor'\n                }\n            ]\n        };\n        setCurrentProject(project);\n        setProjects((prev)=>[\n                project,\n                ...prev\n            ]);\n        setIsRunning(true);\n        setSystemStatus('ACTIVE');\n        setWorkflowError(null);\n        try {\n            const validation = validateTeamConfiguration();\n            if (!validation.isValid) {\n                throw new Error(\"Configuration errors: \".concat(validation.issues.join(', ')));\n            }\n            const updatedProject = {\n                ...project,\n                status: 'in_progress'\n            };\n            setCurrentProject(updatedProject);\n            for(let i = 0; i < updatedProject.steps.length; i++){\n                const step = updatedProject.steps[i];\n                step.status = 'running';\n                setSystemStatus(\"EXECUTING: \".concat(step.agent.toUpperCase()));\n                setCurrentProject({\n                    ...updatedProject\n                });\n                const executionTimes = {\n                    'Research Agent': 45000,\n                    'SEO Strategist': 30000,\n                    'Content Architect': 25000,\n                    'Content Creator': 120000,\n                    'Quality Editor': 40000\n                };\n                const stepTime = executionTimes[step.agent] || 30000;\n                const progressSteps = 20;\n                const intervalTime = stepTime / progressSteps;\n                for(let progress = 0; progress <= 100; progress += 5){\n                    step.progress = progress;\n                    setPowerLevel(Math.max(70, 100 - progress * 0.3));\n                    setCurrentProject({\n                        ...updatedProject\n                    });\n                    await new Promise((resolve)=>setTimeout(resolve, intervalTime / 20));\n                }\n                step.status = 'completed';\n                const costs = {\n                    'Research Agent': 0.08,\n                    'SEO Strategist': 0.05,\n                    'Content Architect': 0.04,\n                    'Content Creator': 0.25,\n                    'Quality Editor': 0.06\n                };\n                step.cost = costs[step.agent] || 0.05;\n                step.duration = stepTime / 1000;\n                if (step.agent === 'Content Creator') {\n                    step.output = generateSampleContent(updatedProject.topic);\n                    setPreviewContent(step.output);\n                }\n                setTotalCost((prev)=>prev + (step.cost || 0));\n                setPowerLevel(100);\n            }\n            updatedProject.status = 'completed';\n            setCurrentProject(updatedProject);\n            setProjects((prev)=>prev.map((p)=>p.id === updatedProject.id ? updatedProject : p));\n            setSystemStatus('COMPLETED');\n        } catch (error) {\n            console.error('Workflow execution failed:', error);\n            setWorkflowError(error instanceof Error ? error.message : 'Unknown error occurred');\n            setSystemStatus('ERROR');\n        } finally{\n            setIsRunning(false);\n        }\n    };\n    const generateSampleContent = (topic)=>{\n        return \"# \".concat(topic, \"\\n\\n## Introduction\\n\\nThis comprehensive guide explores \").concat(topic, \" and provides valuable insights for readers seeking to understand this important subject.\\n\\n## Key Points\\n\\n- **Research-backed insights**: Our analysis is based on the latest industry research and expert opinions\\n- **Practical applications**: Real-world examples and actionable strategies\\n- **Future trends**: What to expect in the evolving landscape\\n\\n## Detailed Analysis\\n\\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.\\n\\n### Section 1: Understanding the Basics\\n\\nDetailed explanation of fundamental concepts and principles.\\n\\n### Section 2: Advanced Strategies  \\n\\nMore sophisticated approaches and methodologies.\\n\\n## Conclusion\\n\\nThis content has been optimized for search engines while maintaining high readability and engagement for human readers.\\n\\n*Generated by Invincible AI Content Team - Powered by KaibanJS & Kimi K2*\");\n    };\n    const exportContent = (format)=>{\n        if (!previewContent) {\n            alert('No content to export. Please generate content first.');\n            return;\n        }\n        let content = '';\n        let filename = '';\n        let mimeType = '';\n        switch(format){\n            case 'md':\n                content = previewContent;\n                filename = \"\".concat((topic === null || topic === void 0 ? void 0 : topic.replace(/\\s+/g, '-').toLowerCase()) || 'content', \".md\");\n                mimeType = 'text/markdown';\n                break;\n            case 'html':\n                content = '<!DOCTYPE html>\\n<html lang=\"en\">\\n<head>\\n    <meta charset=\"UTF-8\">\\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\\n    <title>'.concat(topic || 'Generated Content', \"</title>\\n    <style>\\n        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; max-width: 800px; margin: 0 auto; padding: 20px; }\\n        h1, h2, h3 { color: #333; }\\n        p { margin-bottom: 16px; }\\n        ul, ol { margin-bottom: 16px; padding-left: 20px; }\\n    </style>\\n</head>\\n<body>\\n    \").concat(previewContent.replace(/\\n/g, '<br>'), \"\\n</body>\\n</html>\");\n                filename = \"\".concat((topic === null || topic === void 0 ? void 0 : topic.replace(/\\s+/g, '-').toLowerCase()) || 'content', \".html\");\n                mimeType = 'text/html';\n                break;\n            case 'txt':\n                content = previewContent.replace(/[#*`]/g, '').replace(/\\n+/g, '\\n');\n                filename = \"\".concat((topic === null || topic === void 0 ? void 0 : topic.replace(/\\s+/g, '-').toLowerCase()) || 'content', \".txt\");\n                mimeType = 'text/plain';\n                break;\n        }\n        const blob = new Blob([\n            content\n        ], {\n            type: mimeType\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = filename;\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n    };\n    const copyToClipboard = async ()=>{\n        if (!previewContent) return;\n        try {\n            await navigator.clipboard.writeText(previewContent);\n            alert('Content copied to clipboard!');\n        } catch (err) {\n            console.error('Failed to copy content:', err);\n            alert('Failed to copy content to clipboard');\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-blue-500/30 bg-slate-900/80 backdrop-blur-lg sticky top-0 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-6 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Shield, {\n                                                className: \"h-10 w-10 text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 439,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 437,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl font-bold bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent\",\n                                                children: \"INVINCIBLE AI WRITER\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-slate-400\",\n                                                children: \"Multi-Agent Content Generation System\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 445,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                lineNumber: 436,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium text-blue-400\",\n                                                children: \"SYSTEM STATUS\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 452,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-slate-300\",\n                                                children: systemStatus\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 453,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative w-16 h-16\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 rounded-full bg-gradient-to-r from-blue-500 to-cyan-500 animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-1 rounded-full bg-slate-900 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-bold text-cyan-400\",\n                                                    children: [\n                                                        powerLevel,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                lineNumber: 450,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                        lineNumber: 435,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                    lineNumber: 434,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                lineNumber: 433,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-6 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-1 bg-slate-800/50 p-2 rounded-xl mb-8 backdrop-blur-sm\",\n                        children: [\n                            {\n                                id: 'command-center',\n                                label: 'Command Center',\n                                icon: _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n                            },\n                            {\n                                id: 'mission-config',\n                                label: 'Mission Config',\n                                icon: _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n                            },\n                            {\n                                id: 'content-preview',\n                                label: 'Content Preview',\n                                icon: _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                            },\n                            {\n                                id: 'analytics',\n                                label: 'Analytics',\n                                icon: _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                            }\n                        ].map((param)=>{\n                            let { id, label, icon: Icon } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(id),\n                                className: \"flex items-center space-x-2 px-4 py-3 rounded-lg font-medium transition-all duration-200 \".concat(activeTab === id ? 'bg-gradient-to-r from-blue-500 to-cyan-500 text-white shadow-lg' : 'text-slate-400 hover:text-white hover:bg-slate-700/50'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 484,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: label\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 485,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, id, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                lineNumber: 475,\n                                columnNumber: 13\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                        lineNumber: 468,\n                        columnNumber: 9\n                    }, this),\n                    activeTab === 'command-center' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-slate-800/50 to-slate-900/50 rounded-2xl border border-blue-500/20 backdrop-blur-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 border-b border-slate-700/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Cpu, {\n                                                        className: \"h-6 w-6 text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 497,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl font-bold\",\n                                                        children: \"Mission Control\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 498,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 496,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: startContentGeneration,\n                                                        disabled: isRunning || !topic.trim(),\n                                                        className: \"flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 disabled:from-slate-600 disabled:to-slate-700 disabled:cursor-not-allowed rounded-lg font-medium transition-all duration-200 shadow-lg hover:shadow-xl\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 506,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: isRunning ? 'EXECUTING...' : 'INITIATE MISSION'\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 507,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 501,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: stopGeneration,\n                                                        disabled: !isRunning,\n                                                        className: \"flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-red-500 to-rose-500 hover:from-red-600 hover:to-rose-600 disabled:from-slate-600 disabled:to-slate-700 disabled:cursor-not-allowed rounded-lg font-medium transition-all duration-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 514,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"ABORT\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 515,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 509,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: resetWorkflow,\n                                                        className: \"flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 rounded-lg font-medium transition-all duration-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RefreshCw, {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 521,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"RESET\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 522,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 517,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 500,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg font-medium\",\n                                                        children: topic || 'No active mission'\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 530,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"px-3 py-1 rounded-full text-sm font-medium \".concat(currentTask.status === 'completed' ? 'bg-green-500/20 text-green-400' : currentTask.status === 'in_progress' ? 'bg-blue-500/20 text-blue-400' : 'bg-slate-500/20 text-slate-400'),\n                                                        children: currentTask.status.toUpperCase()\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 533,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 529,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative h-3 bg-slate-700 rounded-full overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-y-0 left-0 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full transition-all duration-500\",\n                                                        style: {\n                                                            width: \"\".concat(currentTask.progress, \"%\")\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 543,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 547,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 542,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-sm text-slate-400 mt-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            Math.round(currentTask.progress),\n                                                            \"% Complete\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 551,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Mission Cost: $\",\n                                                            totalCost.toFixed(3)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 552,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 550,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 528,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                lineNumber: 494,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-6 w-6 text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-bold\",\n                                                children: \"Agent Squadron Status\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 561,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 559,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                        children: agents.map((agent)=>{\n                                            const Icon = agentIcons[agent.id];\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-br from-slate-800/50 to-slate-900/50 rounded-xl border border-slate-700/50 p-6 backdrop-blur-sm hover:border-blue-500/30 transition-all duration-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3 mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 rounded-lg \".concat(agent.status === 'running' ? 'bg-blue-500/20 text-blue-400' : agent.status === 'completed' ? 'bg-green-500/20 text-green-400' : agent.status === 'error' ? 'bg-red-500/20 text-red-400' : 'bg-slate-500/20 text-slate-400'),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                    className: \"h-6 w-6\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 576,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 570,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-bold text-sm\",\n                                                                        children: agent.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                        lineNumber: 579,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-slate-400\",\n                                                                        children: agent.specialAbility\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                        lineNumber: 580,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 578,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 569,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between text-xs text-slate-400 mb-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Power Level\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                        lineNumber: 587,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            agent.powerLevel,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                        lineNumber: 588,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 586,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-2 bg-slate-700 rounded-full overflow-hidden\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-full rounded-full transition-all duration-500 \".concat(agent.powerLevel >= 95 ? 'bg-gradient-to-r from-green-400 to-emerald-400' : agent.powerLevel >= 85 ? 'bg-gradient-to-r from-blue-400 to-cyan-400' : 'bg-gradient-to-r from-yellow-400 to-orange-400'),\n                                                                    style: {\n                                                                        width: \"\".concat(agent.powerLevel, \"%\")\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 591,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 590,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 585,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-1 bg-slate-700 rounded-full overflow-hidden mb-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-full rounded-full transition-all duration-300 \".concat(agent.status === 'completed' ? 'bg-green-400' : agent.status === 'running' ? 'bg-blue-400 animate-pulse' : 'bg-slate-600'),\n                                                            style: {\n                                                                width: \"\".concat(agent.progress, \"%\")\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 604,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 603,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-xs\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 rounded-full \".concat(agent.status === 'running' ? 'bg-blue-500/20 text-blue-400' : agent.status === 'completed' ? 'bg-green-500/20 text-green-400' : agent.status === 'error' ? 'bg-red-500/20 text-red-400' : 'bg-slate-500/20 text-slate-400'),\n                                                                children: agent.status.toUpperCase()\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 615,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            agent.cost > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-slate-400\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    agent.cost.toFixed(3)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 623,\n                                                                columnNumber: 44\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 614,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, agent.id, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 568,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 564,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                lineNumber: 558,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                        lineNumber: 492,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'mission-config' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-br from-slate-800/50 to-slate-900/50 rounded-2xl border border-blue-500/20 backdrop-blur-sm p-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Brain, {\n                                            className: \"h-6 w-6 text-blue-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                            lineNumber: 638,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-bold\",\n                                            children: \"Mission Configuration\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                            lineNumber: 639,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                    lineNumber: 637,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-slate-300 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                    className: \"h-4 w-4 inline mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 646,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Primary Target *\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 645,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: topic,\n                                                            onChange: (e)=>setTopic(e.target.value),\n                                                            placeholder: \"Enter your content mission objective...\",\n                                                            className: \"w-full px-4 py-3 bg-slate-800/50 border border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 649,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                    lineNumber: 644,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-slate-300 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Globe, {\n                                                                    className: \"h-4 w-4 inline mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 660,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"SEO Keywords\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 659,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: keywords,\n                                                            onChange: (e)=>setKeywords(e.target.value),\n                                                            placeholder: \"keyword1, keyword2, keyword3...\",\n                                                            className: \"w-full px-4 py-3 bg-slate-800/50 border border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 663,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                    lineNumber: 658,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                            lineNumber: 643,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-slate-300 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    className: \"h-4 w-4 inline mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 676,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Mission Type\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 675,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: contentType,\n                                                            onChange: (e)=>setContentType(e.target.value),\n                                                            className: \"w-full px-4 py-3 bg-slate-800/50 border border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"blog-post\",\n                                                                    children: \"Blog Post\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 684,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"article\",\n                                                                    children: \"Article\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 685,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"landing-page\",\n                                                                    children: \"Landing Page\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 686,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"product-description\",\n                                                                    children: \"Product Description\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 687,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"social-media\",\n                                                                    children: \"Social Media Series\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 688,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 679,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                    lineNumber: 674,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-slate-300 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TrendingUp, {\n                                                                    className: \"h-4 w-4 inline mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 694,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Target Scope: \",\n                                                                targetLength,\n                                                                \" words\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 693,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"range\",\n                                                            min: \"500\",\n                                                            max: \"5000\",\n                                                            step: \"100\",\n                                                            value: targetLength,\n                                                            onChange: (e)=>setTargetLength(parseInt(e.target.value)),\n                                                            className: \"w-full h-2 bg-slate-700 rounded-lg appearance-none cursor-pointer slider\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 697,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-xs text-slate-400 mt-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 707,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"5000\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 708,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 706,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                    lineNumber: 692,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                            lineNumber: 673,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                    lineNumber: 642,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                            lineNumber: 636,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                        lineNumber: 635,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'content-preview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-br from-slate-800/50 to-slate-900/50 rounded-2xl border border-blue-500/20 backdrop-blur-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border-b border-slate-700/50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-6 w-6 text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 724,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl font-bold\",\n                                                        children: \"Generated Content\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 725,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 723,\n                                                columnNumber: 19\n                                            }, this),\n                                            previewContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>exportContent('md'),\n                                                        className: \"px-4 py-2 bg-slate-700 hover:bg-slate-600 border border-slate-600 rounded-lg text-sm font-medium transition-colors flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 733,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"MD\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 734,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 729,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>exportContent('html'),\n                                                        className: \"px-4 py-2 bg-slate-700 hover:bg-slate-600 border border-slate-600 rounded-lg text-sm font-medium transition-colors flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 740,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"HTML\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 741,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 736,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: copyToClipboard,\n                                                        className: \"px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg text-sm font-medium transition-colors\",\n                                                        children: \"Copy\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 743,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 728,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 722,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                    lineNumber: 721,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-slate-900/50 rounded-xl border border-slate-700/50 p-6 min-h-[400px]\",\n                                        children: previewContent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"prose prose-invert prose-blue max-w-none\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_16__.Markdown, {\n                                                children: previewContent\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 758,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                            lineNumber: 757,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center h-full text-slate-500\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-16 w-16 mx-auto mb-4 opacity-50\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 763,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg\",\n                                                        children: \"Content will materialize here after mission execution\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 764,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm mt-2\",\n                                                        children: \"Configure your mission and initiate the content generation process\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 765,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 762,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                            lineNumber: 761,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 755,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                    lineNumber: 754,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                            lineNumber: 720,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                        lineNumber: 719,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'analytics' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            workflowError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-500/10 border border-red-500/30 rounded-xl p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center text-red-400\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-5 w-5 mr-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                            lineNumber: 782,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: \"Mission Error: \"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                            lineNumber: 783,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2\",\n                                            children: workflowError\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                            lineNumber: 784,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                    lineNumber: 781,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                lineNumber: 780,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-6 w-6 text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 792,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-bold\",\n                                                children: \"Performance Metrics\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 793,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 791,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-br from-green-500/10 to-emerald-500/10 border border-green-500/20 rounded-xl p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"h-10 w-10 text-green-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 799,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-slate-400\",\n                                                                    children: \"Mission Cost\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 801,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-2xl font-bold text-green-400\",\n                                                                    children: [\n                                                                        \"$\",\n                                                                        totalCost.toFixed(3)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 802,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-green-500\",\n                                                                    children: \"85-95% savings\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 803,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 800,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                    lineNumber: 798,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 797,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-br from-blue-500/10 to-cyan-500/10 border border-blue-500/20 rounded-xl p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            className: \"h-10 w-10 text-blue-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 810,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-slate-400\",\n                                                                    children: \"Avg. Generation\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 812,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-2xl font-bold text-blue-400\",\n                                                                    children: \"4-6m\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 813,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-blue-500\",\n                                                                    children: \"Per mission\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 814,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 811,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                    lineNumber: 809,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 808,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-br from-purple-500/10 to-pink-500/10 border border-purple-500/20 rounded-xl p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-10 w-10 text-purple-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 821,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-slate-400\",\n                                                                    children: \"Quality Score\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 823,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-2xl font-bold text-purple-400\",\n                                                                    children: \"9.2/10\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 824,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-purple-500\",\n                                                                    children: \"EQ-Bench rating\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 825,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 822,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                    lineNumber: 820,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 819,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-br from-cyan-500/10 to-teal-500/10 border border-cyan-500/20 rounded-xl p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"h-10 w-10 text-cyan-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 832,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-slate-400\",\n                                                                    children: \"Success Rate\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 834,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-2xl font-bold text-cyan-400\",\n                                                                    children: \"98%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 835,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-cyan-500\",\n                                                                    children: \"Mission completion\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 836,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 833,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                    lineNumber: 831,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 830,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 796,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                lineNumber: 790,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-slate-800/50 to-slate-900/50 rounded-2xl border border-blue-500/20 backdrop-blur-sm p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold mb-6\",\n                                        children: \"Agent Squadron Analytics\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 845,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: teamAnalytics.agents.map((agent, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-6 bg-slate-800/30 border border-slate-700/50 rounded-xl\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-3 h-3 rounded-full bg-green-400 animate-pulse\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 850,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-bold text-white\",\n                                                                        children: agent.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                        lineNumber: 852,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-slate-400\",\n                                                                        children: agent.specialization\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                        lineNumber: 853,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-slate-500\",\n                                                                        children: [\n                                                                            \"Tools: \",\n                                                                            agent.tools.join(', ')\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                        lineNumber: 854,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 851,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 849,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium text-blue-400\",\n                                                                children: agent.performance\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 858,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-slate-400\",\n                                                                children: agent.avgExecutionTime\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 859,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mt-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-slate-500\",\n                                                                        children: \"Power:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                        lineNumber: 861,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs font-bold text-green-400\",\n                                                                        children: [\n                                                                            agent.powerLevel,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                        lineNumber: 862,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 860,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 857,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 848,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 846,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                lineNumber: 844,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                        lineNumber: 777,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                lineNumber: 466,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n        lineNumber: 431,\n        columnNumber: 5\n    }, this);\n}\n_s(InvincibleContentWriter, \"ET5b6b78G4JwAwqymrc0p4X4jLw=\");\n_c = InvincibleContentWriter;\nvar _c;\n$RefreshReg$(_c, \"InvincibleContentWriter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/content-writer/page.tsx\n"));

/***/ })

});