/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/content-writer/page";
exports.ids = ["app/content-writer/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fcontent-writer%2Fpage&page=%2Fcontent-writer%2Fpage&appPaths=%2Fcontent-writer%2Fpage&pagePath=private-next-app-dir%2Fcontent-writer%2Fpage.tsx&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fcontent-writer%2Fpage&page=%2Fcontent-writer%2Fpage&appPaths=%2Fcontent-writer%2Fpage&pagePath=private-next-app-dir%2Fcontent-writer%2Fpage.tsx&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/content-writer/page.tsx */ \"(rsc)/./src/app/content-writer/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'content-writer',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/content-writer/page\",\n        pathname: \"/content-writer\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fcontent-writer%2Fpage&page=%2Fcontent-writer%2Fpage&appPaths=%2Fcontent-writer%2Fpage&pagePath=private-next-app-dir%2Fcontent-writer%2Fpage.tsx&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcomponents%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcomponents%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/SessionProvider.tsx */ \"(rsc)/./src/components/SessionProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmZvbnQlMkZnb29nbGUlMkZ0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMnNyYyUyRmFwcCUyRmxheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGc3JjJTJGYXBwJTJGZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZhYXl1c2htaXNocmElMkZEZXNrdG9wJTJGb2xkJTIwaW52aW5jaWJsZSUyMHdpdGglMjBkZWVwcmVzZWFyY2glMkZzcmMlMkZjb21wb25lbnRzJTJGU2Vzc2lvblByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9MQUFpSyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIi9Vc2Vycy9hYXl1c2htaXNocmEvRGVza3RvcC9vbGQgaW52aW5jaWJsZSB3aXRoIGRlZXByZXNlYXJjaC9zcmMvY29tcG9uZW50cy9TZXNzaW9uUHJvdmlkZXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcomponents%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fcontent-writer%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fcontent-writer%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/content-writer/page.tsx */ \"(rsc)/./src/app/content-writer/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGc3JjJTJGYXBwJTJGY29udGVudC13cml0ZXIlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOEtBQWlJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvYWF5dXNobWlzaHJhL0Rlc2t0b3Avb2xkIGludmluY2libGUgd2l0aCBkZWVwcmVzZWFyY2gvc3JjL2FwcC9jb250ZW50LXdyaXRlci9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fcontent-writer%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/content-writer/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/content-writer/page.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"333781e81268\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMvYWF5dXNobWlzaHJhL0Rlc2t0b3Avb2xkIGludmluY2libGUgd2l0aCBkZWVwcmVzZWFyY2gvc3JjL2FwcC9nbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjMzMzc4MWU4MTI2OFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_SessionProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/SessionProvider */ \"(rsc)/./src/components/SessionProvider.tsx\");\n\n\n\n\nconst metadata = {\n    title: 'Invincible - AI Content Generation Platform',\n    description: 'The ultimate content writing SaaS platform powered by advanced AI technology'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"dark\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className)} bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 min-h-screen`,\n            suppressHydrationWarning: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SessionProvider__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/SessionProvider.tsx":
/*!********************************************!*\
  !*** ./src/components/SessionProvider.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SessionProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SessionProvider.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGY2xpZW50LXBhZ2UuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZhYXl1c2htaXNocmElMkZEZXNrdG9wJTJGb2xkJTIwaW52aW5jaWJsZSUyMHdpdGglMjBkZWVwcmVzZWFyY2glMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZjbGllbnQtc2VnbWVudC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmFheXVzaG1pc2hyYSUyRkRlc2t0b3AlMkZvbGQlMjBpbnZpbmNpYmxlJTIwd2l0aCUyMGRlZXByZXNlYXJjaCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmVycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGaHR0cC1hY2Nlc3MtZmFsbGJhY2slMkZlcnJvci1ib3VuZGFyeS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmFheXVzaG1pc2hyYSUyRkRlc2t0b3AlMkZvbGQlMjBpbnZpbmNpYmxlJTIwd2l0aCUyMGRlZXByZXNlYXJjaCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmxheW91dC1yb3V0ZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZhYXl1c2htaXNocmElMkZEZXNrdG9wJTJGb2xkJTIwaW52aW5jaWJsZSUyMHdpdGglMjBkZWVwcmVzZWFyY2glMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZtZXRhZGF0YSUyRmFzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGbWV0YWRhdGElMkZtZXRhZGF0YS1ib3VuZGFyeS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmFheXVzaG1pc2hyYSUyRkRlc2t0b3AlMkZvbGQlMjBpbnZpbmNpYmxlJTIwd2l0aCUyMGRlZXByZXNlYXJjaCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRnJlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9PQUF5SjtBQUN6SjtBQUNBLDBPQUE0SjtBQUM1SjtBQUNBLDBPQUE0SjtBQUM1SjtBQUNBLG9SQUFpTDtBQUNqTDtBQUNBLHdPQUEySjtBQUMzSjtBQUNBLDRQQUFxSztBQUNySztBQUNBLGtRQUF3SztBQUN4SztBQUNBLHNRQUEwSyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2FheXVzaG1pc2hyYS9EZXNrdG9wL29sZCBpbnZpbmNpYmxlIHdpdGggZGVlcHJlc2VhcmNoL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvY2xpZW50LXBhZ2UuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9hYXl1c2htaXNocmEvRGVza3RvcC9vbGQgaW52aW5jaWJsZSB3aXRoIGRlZXByZXNlYXJjaC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2NsaWVudC1zZWdtZW50LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvYWF5dXNobWlzaHJhL0Rlc2t0b3Avb2xkIGludmluY2libGUgd2l0aCBkZWVwcmVzZWFyY2gvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9lcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2FheXVzaG1pc2hyYS9EZXNrdG9wL29sZCBpbnZpbmNpYmxlIHdpdGggZGVlcHJlc2VhcmNoL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvaHR0cC1hY2Nlc3MtZmFsbGJhY2svZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9hYXl1c2htaXNocmEvRGVza3RvcC9vbGQgaW52aW5jaWJsZSB3aXRoIGRlZXByZXNlYXJjaC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2xheW91dC1yb3V0ZXIuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9hYXl1c2htaXNocmEvRGVza3RvcC9vbGQgaW52aW5jaWJsZSB3aXRoIGRlZXByZXNlYXJjaC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL21ldGFkYXRhL2FzeW5jLW1ldGFkYXRhLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvYWF5dXNobWlzaHJhL0Rlc2t0b3Avb2xkIGludmluY2libGUgd2l0aCBkZWVwcmVzZWFyY2gvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9tZXRhZGF0YS9tZXRhZGF0YS1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2FheXVzaG1pc2hyYS9EZXNrdG9wL29sZCBpbnZpbmNpYmxlIHdpdGggZGVlcHJlc2VhcmNoL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcomponents%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcomponents%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/SessionProvider.tsx */ \"(ssr)/./src/components/SessionProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmZvbnQlMkZnb29nbGUlMkZ0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMnNyYyUyRmFwcCUyRmxheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGc3JjJTJGYXBwJTJGZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZhYXl1c2htaXNocmElMkZEZXNrdG9wJTJGb2xkJTIwaW52aW5jaWJsZSUyMHdpdGglMjBkZWVwcmVzZWFyY2glMkZzcmMlMkZjb21wb25lbnRzJTJGU2Vzc2lvblByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9MQUFpSyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIi9Vc2Vycy9hYXl1c2htaXNocmEvRGVza3RvcC9vbGQgaW52aW5jaWJsZSB3aXRoIGRlZXByZXNlYXJjaC9zcmMvY29tcG9uZW50cy9TZXNzaW9uUHJvdmlkZXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcomponents%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fcontent-writer%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fcontent-writer%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/content-writer/page.tsx */ \"(ssr)/./src/app/content-writer/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGc3JjJTJGYXBwJTJGY29udGVudC13cml0ZXIlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOEtBQWlJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvYWF5dXNobWlzaHJhL0Rlc2t0b3Avb2xkIGludmluY2libGUgd2l0aCBkZWVwcmVzZWFyY2gvc3JjL2FwcC9jb250ZW50LXdyaXRlci9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fcontent-writer%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/content-writer/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/content-writer/page.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ InvincibleContentWriter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,CheckCircle,Clock,DollarSign,Download,Eye,FileText,PenTool,Play,Search,Settings,Sparkles,Square,Target,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,CheckCircle,Clock,DollarSign,Download,Eye,FileText,PenTool,Play,Search,Settings,Sparkles,Square,Target,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,CheckCircle,Clock,DollarSign,Download,Eye,FileText,PenTool,Play,Search,Settings,Sparkles,Square,Target,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,CheckCircle,Clock,DollarSign,Download,Eye,FileText,PenTool,Play,Search,Settings,Sparkles,Square,Target,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/pen-tool.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,CheckCircle,Clock,DollarSign,Download,Eye,FileText,PenTool,Play,Search,Settings,Sparkles,Square,Target,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,CheckCircle,Clock,DollarSign,Download,Eye,FileText,PenTool,Play,Search,Settings,Sparkles,Square,Target,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,CheckCircle,Clock,DollarSign,Download,Eye,FileText,PenTool,Play,Search,Settings,Sparkles,Square,Target,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,CheckCircle,Clock,DollarSign,Download,Eye,FileText,PenTool,Play,Search,Settings,Sparkles,Square,Target,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,CheckCircle,Clock,DollarSign,Download,Eye,FileText,PenTool,Play,Search,Settings,Sparkles,Square,Target,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,CheckCircle,Clock,DollarSign,Download,Eye,FileText,PenTool,Play,Search,Settings,Sparkles,Square,Target,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,CheckCircle,Clock,DollarSign,Download,Eye,FileText,PenTool,Play,Search,Settings,Sparkles,Square,Target,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,CheckCircle,Clock,DollarSign,Download,Eye,FileText,PenTool,Play,Search,Settings,Sparkles,Square,Target,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,CheckCircle,Clock,DollarSign,Download,Eye,FileText,PenTool,Play,Search,Settings,Sparkles,Square,Target,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,CheckCircle,Clock,DollarSign,Download,Eye,FileText,PenTool,Play,Search,Settings,Sparkles,Square,Target,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,CheckCircle,Clock,DollarSign,Download,Eye,FileText,PenTool,Play,Search,Settings,Sparkles,Square,Target,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,CheckCircle,Clock,DollarSign,Download,Eye,FileText,PenTool,Play,Search,Settings,Sparkles,Square,Target,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,CheckCircle,Clock,DollarSign,Download,Eye,FileText,PenTool,Play,Search,Settings,Sparkles,Square,Target,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,CheckCircle,Clock,DollarSign,Download,Eye,FileText,PenTool,Play,Search,Settings,Sparkles,Square,Target,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! react-markdown */ \"(ssr)/./node_modules/react-markdown/lib/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n// Enhanced team analytics with Invincible theme\nconst getTeamAnalytics = ()=>({\n        agents: [\n            {\n                name: 'Research Agent',\n                specialization: 'Web Research & Data Collection',\n                tools: [\n                    'Tavily Search',\n                    'SERP Analysis'\n                ],\n                performance: 'Excellent',\n                avgExecutionTime: '45s',\n                powerLevel: 95,\n                specialAbility: 'Deep Web Intelligence'\n            },\n            {\n                name: 'SEO Strategist',\n                specialization: 'Keyword Research & Strategy',\n                tools: [\n                    'Keyword Analysis',\n                    'Competition Research'\n                ],\n                performance: 'Very Good',\n                avgExecutionTime: '30s',\n                powerLevel: 88,\n                specialAbility: 'Search Domination'\n            },\n            {\n                name: 'Content Architect',\n                specialization: 'Content Structure & Planning',\n                tools: [\n                    'Outline Generator',\n                    'Content Planner'\n                ],\n                performance: 'Excellent',\n                avgExecutionTime: '25s',\n                powerLevel: 92,\n                specialAbility: 'Structure Mastery'\n            },\n            {\n                name: 'Content Creator',\n                specialization: 'AI-Powered Writing',\n                tools: [\n                    'Kimi K2 Model',\n                    'Content Generator'\n                ],\n                performance: 'Outstanding',\n                avgExecutionTime: '2m',\n                powerLevel: 98,\n                specialAbility: 'Creative Genesis'\n            },\n            {\n                name: 'Quality Editor',\n                specialization: 'Content Review & Optimization',\n                tools: [\n                    'Grammar Check',\n                    'SEO Optimization'\n                ],\n                performance: 'Excellent',\n                avgExecutionTime: '40s',\n                powerLevel: 90,\n                specialAbility: 'Perfection Protocol'\n            }\n        ],\n        capabilities: {\n            content_types: [\n                'Blog Posts',\n                'Articles',\n                'Landing Pages',\n                'Social Media',\n                'Email Campaigns',\n                'Product Descriptions'\n            ],\n            word_count_range: '500-5000',\n            seo_optimization: 'Real-time SEO scoring and recommendations',\n            fact_checking: 'Automated fact-checking with source verification',\n            brand_voice: 'Adaptive brand voice matching'\n        },\n        workflows: [\n            {\n                name: 'Standard Content Creation',\n                agents: 5,\n                tasks: 5,\n                estimatedTime: '4-6 minutes',\n                costEstimate: '$0.40-0.80',\n                success_rate: '98%'\n            },\n            {\n                name: 'SEO-Focused Content',\n                agents: 5,\n                tasks: 7,\n                estimatedTime: '6-8 minutes',\n                costEstimate: '$0.60-1.00',\n                success_rate: '96%'\n            },\n            {\n                name: 'Technical Content',\n                agents: 5,\n                tasks: 6,\n                estimatedTime: '8-12 minutes',\n                costEstimate: '$0.80-1.20',\n                success_rate: '94%'\n            }\n        ]\n    });\nconst validateTeamConfiguration = ()=>({\n        isValid: true,\n        issues: []\n    });\nfunction InvincibleContentWriter() {\n    const [projects, setProjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentProject, setCurrentProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [newProjectTopic, setNewProjectTopic] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [targetKeywords, setTargetKeywords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [contentType, setContentType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('blog-post');\n    const [wordCount, setWordCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(2000);\n    const [isRunning, setIsRunning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [totalCost, setTotalCost] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [previewContent, setPreviewContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [teamAnalytics, setTeamAnalytics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(getTeamAnalytics());\n    const [workflowError, setWorkflowError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('command-center');\n    const [topic, setTopic] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [keywords, setKeywords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [targetLength, setTargetLength] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(2000);\n    // Enhanced UI states\n    const [systemStatus, setSystemStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('READY');\n    const [powerLevel, setPowerLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(100);\n    // Mock current task data\n    const getOverallProgress = ()=>{\n        if (!currentProject || !currentProject.steps || currentProject.steps.length === 0) return 0;\n        const completedSteps = currentProject.steps.filter((s)=>s.status === 'completed').length;\n        return completedSteps / currentProject.steps.length * 100;\n    };\n    // Current task computed state\n    const currentTask = {\n        topic: currentProject?.topic || topic || '',\n        status: currentProject?.status || 'pending',\n        progress: getOverallProgress()\n    };\n    // Enhanced agents data with power levels\n    const [agents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 'Research Agent',\n            name: 'Research Agent',\n            status: 'idle',\n            progress: 0,\n            currentTask: '',\n            cost: 0,\n            powerLevel: 95,\n            specialAbility: 'Deep Web Intelligence'\n        },\n        {\n            id: 'SEO Strategist',\n            name: 'SEO Strategist',\n            status: 'idle',\n            progress: 0,\n            currentTask: '',\n            cost: 0,\n            powerLevel: 88,\n            specialAbility: 'Search Domination'\n        },\n        {\n            id: 'Content Architect',\n            name: 'Content Architect',\n            status: 'idle',\n            progress: 0,\n            currentTask: '',\n            cost: 0,\n            powerLevel: 92,\n            specialAbility: 'Structure Mastery'\n        },\n        {\n            id: 'Content Creator',\n            name: 'Content Creator',\n            status: 'idle',\n            progress: 0,\n            currentTask: '',\n            cost: 0,\n            powerLevel: 98,\n            specialAbility: 'Creative Genesis'\n        },\n        {\n            id: 'Quality Editor',\n            name: 'Quality Editor',\n            status: 'idle',\n            progress: 0,\n            currentTask: '',\n            cost: 0,\n            powerLevel: 90,\n            specialAbility: 'Perfection Protocol'\n        }\n    ]);\n    const agentIcons = {\n        'Research Agent': _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        'SEO Strategist': _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        'Content Architect': _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        'Content Creator': _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        'Quality Editor': _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    };\n    // Placeholder functions\n    const stopGeneration = ()=>{\n        setIsRunning(false);\n        setSystemStatus('STOPPED');\n    };\n    const resetWorkflow = ()=>{\n        setIsRunning(false);\n        setCurrentProject(null);\n        setTotalCost(0);\n        setPreviewContent('');\n        setWorkflowError(null);\n        setSystemStatus('READY');\n        setPowerLevel(100);\n    };\n    const startContentGeneration = async ()=>{\n        if (!topic.trim()) {\n            alert('Please enter a topic first');\n            return;\n        }\n        setSystemStatus('INITIALIZING');\n        setPowerLevel(95);\n        // Create a new project with steps\n        const project = {\n            id: Date.now().toString(),\n            topic: topic,\n            targetKeywords: keywords.split(',').map((k)=>k.trim()).filter(Boolean),\n            contentType,\n            wordCount: targetLength,\n            status: 'draft',\n            createdAt: new Date(),\n            steps: [\n                {\n                    id: '1',\n                    name: 'Research & Data Collection',\n                    status: 'pending',\n                    progress: 0,\n                    agent: 'Research Agent'\n                },\n                {\n                    id: '2',\n                    name: 'SEO Strategy & Keywords',\n                    status: 'pending',\n                    progress: 0,\n                    agent: 'SEO Strategist'\n                },\n                {\n                    id: '3',\n                    name: 'Content Architecture',\n                    status: 'pending',\n                    progress: 0,\n                    agent: 'Content Architect'\n                },\n                {\n                    id: '4',\n                    name: 'Content Creation',\n                    status: 'pending',\n                    progress: 0,\n                    agent: 'Content Creator'\n                },\n                {\n                    id: '5',\n                    name: 'Quality Review & Optimization',\n                    status: 'pending',\n                    progress: 0,\n                    agent: 'Quality Editor'\n                }\n            ]\n        };\n        setCurrentProject(project);\n        setProjects((prev)=>[\n                project,\n                ...prev\n            ]);\n        setIsRunning(true);\n        setSystemStatus('ACTIVE');\n        setWorkflowError(null);\n        try {\n            const validation = validateTeamConfiguration();\n            if (!validation.isValid) {\n                throw new Error(`Configuration errors: ${validation.issues.join(', ')}`);\n            }\n            const updatedProject = {\n                ...project,\n                status: 'in_progress'\n            };\n            setCurrentProject(updatedProject);\n            for(let i = 0; i < updatedProject.steps.length; i++){\n                const step = updatedProject.steps[i];\n                step.status = 'running';\n                setSystemStatus(`EXECUTING: ${step.agent.toUpperCase()}`);\n                setCurrentProject({\n                    ...updatedProject\n                });\n                const executionTimes = {\n                    'Research Agent': 45000,\n                    'SEO Strategist': 30000,\n                    'Content Architect': 25000,\n                    'Content Creator': 120000,\n                    'Quality Editor': 40000\n                };\n                const stepTime = executionTimes[step.agent] || 30000;\n                const progressSteps = 20;\n                const intervalTime = stepTime / progressSteps;\n                for(let progress = 0; progress <= 100; progress += 5){\n                    step.progress = progress;\n                    setPowerLevel(Math.max(70, 100 - progress * 0.3));\n                    setCurrentProject({\n                        ...updatedProject\n                    });\n                    await new Promise((resolve)=>setTimeout(resolve, intervalTime / 20));\n                }\n                step.status = 'completed';\n                const costs = {\n                    'Research Agent': 0.08,\n                    'SEO Strategist': 0.05,\n                    'Content Architect': 0.04,\n                    'Content Creator': 0.25,\n                    'Quality Editor': 0.06\n                };\n                step.cost = costs[step.agent] || 0.05;\n                step.duration = stepTime / 1000;\n                if (step.agent === 'Content Creator') {\n                    step.output = generateSampleContent(updatedProject.topic);\n                    setPreviewContent(step.output);\n                }\n                setTotalCost((prev)=>prev + (step.cost || 0));\n                setPowerLevel(100);\n            }\n            updatedProject.status = 'completed';\n            setCurrentProject(updatedProject);\n            setProjects((prev)=>prev.map((p)=>p.id === updatedProject.id ? updatedProject : p));\n            setSystemStatus('COMPLETED');\n        } catch (error) {\n            console.error('Workflow execution failed:', error);\n            setWorkflowError(error instanceof Error ? error.message : 'Unknown error occurred');\n            setSystemStatus('ERROR');\n        } finally{\n            setIsRunning(false);\n        }\n    };\n    const generateSampleContent = (topic)=>{\n        return `# ${topic}\n\n## Introduction\n\nThis comprehensive guide explores ${topic} and provides valuable insights for readers seeking to understand this important subject.\n\n## Key Points\n\n- **Research-backed insights**: Our analysis is based on the latest industry research and expert opinions\n- **Practical applications**: Real-world examples and actionable strategies\n- **Future trends**: What to expect in the evolving landscape\n\n## Detailed Analysis\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.\n\n### Section 1: Understanding the Basics\n\nDetailed explanation of fundamental concepts and principles.\n\n### Section 2: Advanced Strategies  \n\nMore sophisticated approaches and methodologies.\n\n## Conclusion\n\nThis content has been optimized for search engines while maintaining high readability and engagement for human readers.\n\n*Generated by Invincible AI Content Team - Powered by KaibanJS & Kimi K2*`;\n    };\n    const exportContent = (format)=>{\n        if (!previewContent) {\n            alert('No content to export. Please generate content first.');\n            return;\n        }\n        let content = '';\n        let filename = '';\n        let mimeType = '';\n        switch(format){\n            case 'md':\n                content = previewContent;\n                filename = `${topic?.replace(/\\s+/g, '-').toLowerCase() || 'content'}.md`;\n                mimeType = 'text/markdown';\n                break;\n            case 'html':\n                content = `<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>${topic || 'Generated Content'}</title>\n    <style>\n        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; max-width: 800px; margin: 0 auto; padding: 20px; }\n        h1, h2, h3 { color: #333; }\n        p { margin-bottom: 16px; }\n        ul, ol { margin-bottom: 16px; padding-left: 20px; }\n    </style>\n</head>\n<body>\n    ${previewContent.replace(/\\n/g, '<br>')}\n</body>\n</html>`;\n                filename = `${topic?.replace(/\\s+/g, '-').toLowerCase() || 'content'}.html`;\n                mimeType = 'text/html';\n                break;\n            case 'txt':\n                content = previewContent.replace(/[#*`]/g, '').replace(/\\n+/g, '\\n');\n                filename = `${topic?.replace(/\\s+/g, '-').toLowerCase() || 'content'}.txt`;\n                mimeType = 'text/plain';\n                break;\n        }\n        const blob = new Blob([\n            content\n        ], {\n            type: mimeType\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = filename;\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n    };\n    const copyToClipboard = async ()=>{\n        if (!previewContent) return;\n        try {\n            await navigator.clipboard.writeText(previewContent);\n            alert('Content copied to clipboard!');\n        } catch (err) {\n            console.error('Failed to copy content:', err);\n            alert('Failed to copy content to clipboard');\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-blue-500/30 bg-slate-900/80 backdrop-blur-lg sticky top-0 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-6 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Shield, {\n                                                className: \"h-10 w-10 text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 439,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 437,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl font-bold bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent\",\n                                                children: \"INVINCIBLE AI WRITER\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-slate-400\",\n                                                children: \"Multi-Agent Content Generation System\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 445,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                lineNumber: 436,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium text-blue-400\",\n                                                children: \"SYSTEM STATUS\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 452,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-slate-300\",\n                                                children: systemStatus\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 453,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative w-16 h-16\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 rounded-full bg-gradient-to-r from-blue-500 to-cyan-500 animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-1 rounded-full bg-slate-900 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-bold text-cyan-400\",\n                                                    children: [\n                                                        powerLevel,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                lineNumber: 450,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                        lineNumber: 435,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                    lineNumber: 434,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                lineNumber: 433,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-6 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-1 bg-slate-800/50 p-2 rounded-xl mb-8 backdrop-blur-sm\",\n                        children: [\n                            {\n                                id: 'command-center',\n                                label: 'Command Center',\n                                icon: _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n                            },\n                            {\n                                id: 'mission-config',\n                                label: 'Mission Config',\n                                icon: _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n                            },\n                            {\n                                id: 'content-preview',\n                                label: 'Content Preview',\n                                icon: _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                            },\n                            {\n                                id: 'analytics',\n                                label: 'Analytics',\n                                icon: _barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                            }\n                        ].map(({ id, label, icon: Icon })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(id),\n                                className: `flex items-center space-x-2 px-4 py-3 rounded-lg font-medium transition-all duration-200 ${activeTab === id ? 'bg-gradient-to-r from-blue-500 to-cyan-500 text-white shadow-lg' : 'text-slate-400 hover:text-white hover:bg-slate-700/50'}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 484,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: label\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 485,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, id, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                lineNumber: 475,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                        lineNumber: 468,\n                        columnNumber: 9\n                    }, this),\n                    activeTab === 'command-center' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-slate-800/50 to-slate-900/50 rounded-2xl border border-blue-500/20 backdrop-blur-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 border-b border-slate-700/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Cpu, {\n                                                        className: \"h-6 w-6 text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 497,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl font-bold\",\n                                                        children: \"Mission Control\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 498,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 496,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: startContentGeneration,\n                                                        disabled: isRunning || !topic.trim(),\n                                                        className: \"flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 disabled:from-slate-600 disabled:to-slate-700 disabled:cursor-not-allowed rounded-lg font-medium transition-all duration-200 shadow-lg hover:shadow-xl\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 506,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: isRunning ? 'EXECUTING...' : 'INITIATE MISSION'\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 507,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 501,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: stopGeneration,\n                                                        disabled: !isRunning,\n                                                        className: \"flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-red-500 to-rose-500 hover:from-red-600 hover:to-rose-600 disabled:from-slate-600 disabled:to-slate-700 disabled:cursor-not-allowed rounded-lg font-medium transition-all duration-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 514,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"ABORT\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 515,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 509,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: resetWorkflow,\n                                                        className: \"flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 rounded-lg font-medium transition-all duration-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RefreshCw, {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 521,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"RESET\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 522,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 517,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 500,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg font-medium\",\n                                                        children: topic || 'No active mission'\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 530,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `px-3 py-1 rounded-full text-sm font-medium ${currentTask.status === 'completed' ? 'bg-green-500/20 text-green-400' : currentTask.status === 'in_progress' ? 'bg-blue-500/20 text-blue-400' : 'bg-slate-500/20 text-slate-400'}`,\n                                                        children: currentTask.status.toUpperCase()\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 533,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 529,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative h-3 bg-slate-700 rounded-full overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-y-0 left-0 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full transition-all duration-500\",\n                                                        style: {\n                                                            width: `${currentTask.progress}%`\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 543,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 547,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 542,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-sm text-slate-400 mt-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            Math.round(currentTask.progress),\n                                                            \"% Complete\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 551,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Mission Cost: $\",\n                                                            totalCost.toFixed(3)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 552,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 550,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 528,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                lineNumber: 494,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-6 w-6 text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-bold\",\n                                                children: \"Agent Squadron Status\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 561,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 559,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                        children: agents.map((agent)=>{\n                                            const Icon = agentIcons[agent.id];\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-br from-slate-800/50 to-slate-900/50 rounded-xl border border-slate-700/50 p-6 backdrop-blur-sm hover:border-blue-500/30 transition-all duration-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3 mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: `p-3 rounded-lg ${agent.status === 'running' ? 'bg-blue-500/20 text-blue-400' : agent.status === 'completed' ? 'bg-green-500/20 text-green-400' : agent.status === 'error' ? 'bg-red-500/20 text-red-400' : 'bg-slate-500/20 text-slate-400'}`,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                    className: \"h-6 w-6\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 576,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 570,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-bold text-sm\",\n                                                                        children: agent.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                        lineNumber: 579,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-slate-400\",\n                                                                        children: agent.specialAbility\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                        lineNumber: 580,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 578,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 569,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between text-xs text-slate-400 mb-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Power Level\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                        lineNumber: 587,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            agent.powerLevel,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                        lineNumber: 588,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 586,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-2 bg-slate-700 rounded-full overflow-hidden\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: `h-full rounded-full transition-all duration-500 ${agent.powerLevel >= 95 ? 'bg-gradient-to-r from-green-400 to-emerald-400' : agent.powerLevel >= 85 ? 'bg-gradient-to-r from-blue-400 to-cyan-400' : 'bg-gradient-to-r from-yellow-400 to-orange-400'}`,\n                                                                    style: {\n                                                                        width: `${agent.powerLevel}%`\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 591,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 590,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 585,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-1 bg-slate-700 rounded-full overflow-hidden mb-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `h-full rounded-full transition-all duration-300 ${agent.status === 'completed' ? 'bg-green-400' : agent.status === 'running' ? 'bg-blue-400 animate-pulse' : 'bg-slate-600'}`,\n                                                            style: {\n                                                                width: `${agent.progress}%`\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 604,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 603,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-xs\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: `px-2 py-1 rounded-full ${agent.status === 'running' ? 'bg-blue-500/20 text-blue-400' : agent.status === 'completed' ? 'bg-green-500/20 text-green-400' : agent.status === 'error' ? 'bg-red-500/20 text-red-400' : 'bg-slate-500/20 text-slate-400'}`,\n                                                                children: agent.status.toUpperCase()\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 615,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            agent.cost > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-slate-400\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    agent.cost.toFixed(3)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 623,\n                                                                columnNumber: 44\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 614,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, agent.id, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 568,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 564,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                lineNumber: 558,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                        lineNumber: 492,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'mission-config' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-br from-slate-800/50 to-slate-900/50 rounded-2xl border border-blue-500/20 backdrop-blur-sm p-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Brain, {\n                                            className: \"h-6 w-6 text-blue-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                            lineNumber: 638,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-bold\",\n                                            children: \"Mission Configuration\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                            lineNumber: 639,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                    lineNumber: 637,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-slate-300 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                    className: \"h-4 w-4 inline mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 646,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Primary Target *\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 645,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: topic,\n                                                            onChange: (e)=>setTopic(e.target.value),\n                                                            placeholder: \"Enter your content mission objective...\",\n                                                            className: \"w-full px-4 py-3 bg-slate-800/50 border border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 649,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                    lineNumber: 644,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-slate-300 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Globe, {\n                                                                    className: \"h-4 w-4 inline mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 660,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"SEO Keywords\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 659,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: keywords,\n                                                            onChange: (e)=>setKeywords(e.target.value),\n                                                            placeholder: \"keyword1, keyword2, keyword3...\",\n                                                            className: \"w-full px-4 py-3 bg-slate-800/50 border border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 663,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                    lineNumber: 658,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                            lineNumber: 643,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-slate-300 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    className: \"h-4 w-4 inline mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 676,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Mission Type\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 675,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: contentType,\n                                                            onChange: (e)=>setContentType(e.target.value),\n                                                            className: \"w-full px-4 py-3 bg-slate-800/50 border border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"blog-post\",\n                                                                    children: \"Blog Post\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 684,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"article\",\n                                                                    children: \"Article\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 685,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"landing-page\",\n                                                                    children: \"Landing Page\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 686,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"product-description\",\n                                                                    children: \"Product Description\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 687,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"social-media\",\n                                                                    children: \"Social Media Series\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 688,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 679,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                    lineNumber: 674,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-slate-300 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TrendingUp, {\n                                                                    className: \"h-4 w-4 inline mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 694,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Target Scope: \",\n                                                                targetLength,\n                                                                \" words\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 693,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"range\",\n                                                            min: \"500\",\n                                                            max: \"5000\",\n                                                            step: \"100\",\n                                                            value: targetLength,\n                                                            onChange: (e)=>setTargetLength(parseInt(e.target.value)),\n                                                            className: \"w-full h-2 bg-slate-700 rounded-lg appearance-none cursor-pointer slider\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 697,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-xs text-slate-400 mt-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 707,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"5000\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 708,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 706,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                    lineNumber: 692,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                            lineNumber: 673,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                    lineNumber: 642,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                            lineNumber: 636,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                        lineNumber: 635,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'content-preview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-br from-slate-800/50 to-slate-900/50 rounded-2xl border border-blue-500/20 backdrop-blur-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border-b border-slate-700/50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-6 w-6 text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 724,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl font-bold\",\n                                                        children: \"Generated Content\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 725,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 723,\n                                                columnNumber: 19\n                                            }, this),\n                                            previewContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>exportContent('md'),\n                                                        className: \"px-4 py-2 bg-slate-700 hover:bg-slate-600 border border-slate-600 rounded-lg text-sm font-medium transition-colors flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 733,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"MD\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 734,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 729,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>exportContent('html'),\n                                                        className: \"px-4 py-2 bg-slate-700 hover:bg-slate-600 border border-slate-600 rounded-lg text-sm font-medium transition-colors flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 740,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"HTML\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 741,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 736,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: copyToClipboard,\n                                                        className: \"px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg text-sm font-medium transition-colors\",\n                                                        children: \"Copy\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 743,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 728,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 722,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                    lineNumber: 721,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-slate-900/50 rounded-xl border border-slate-700/50 p-6 min-h-[400px]\",\n                                        children: previewContent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"prose prose-invert prose-blue max-w-none\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_16__.Markdown, {\n                                                children: previewContent\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 758,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                            lineNumber: 757,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center h-full text-slate-500\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-16 w-16 mx-auto mb-4 opacity-50\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 763,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg\",\n                                                        children: \"Content will materialize here after mission execution\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 764,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm mt-2\",\n                                                        children: \"Configure your mission and initiate the content generation process\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 765,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 762,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                            lineNumber: 761,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 755,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                    lineNumber: 754,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                            lineNumber: 720,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                        lineNumber: 719,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'analytics' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            workflowError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-500/10 border border-red-500/30 rounded-xl p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center text-red-400\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-5 w-5 mr-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                            lineNumber: 782,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: \"Mission Error: \"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                            lineNumber: 783,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2\",\n                                            children: workflowError\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                            lineNumber: 784,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                    lineNumber: 781,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                lineNumber: 780,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-6 w-6 text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 792,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-bold\",\n                                                children: \"Performance Metrics\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 793,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 791,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-br from-green-500/10 to-emerald-500/10 border border-green-500/20 rounded-xl p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"h-10 w-10 text-green-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 799,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-slate-400\",\n                                                                    children: \"Mission Cost\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 801,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-2xl font-bold text-green-400\",\n                                                                    children: [\n                                                                        \"$\",\n                                                                        totalCost.toFixed(3)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 802,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-green-500\",\n                                                                    children: \"85-95% savings\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 803,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 800,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                    lineNumber: 798,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 797,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-br from-blue-500/10 to-cyan-500/10 border border-blue-500/20 rounded-xl p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            className: \"h-10 w-10 text-blue-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 810,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-slate-400\",\n                                                                    children: \"Avg. Generation\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 812,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-2xl font-bold text-blue-400\",\n                                                                    children: \"4-6m\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 813,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-blue-500\",\n                                                                    children: \"Per mission\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 814,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 811,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                    lineNumber: 809,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 808,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-br from-purple-500/10 to-pink-500/10 border border-purple-500/20 rounded-xl p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-10 w-10 text-purple-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 821,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-slate-400\",\n                                                                    children: \"Quality Score\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 823,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-2xl font-bold text-purple-400\",\n                                                                    children: \"9.2/10\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 824,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-purple-500\",\n                                                                    children: \"EQ-Bench rating\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 825,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 822,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                    lineNumber: 820,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 819,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-br from-cyan-500/10 to-teal-500/10 border border-cyan-500/20 rounded-xl p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_CheckCircle_Clock_DollarSign_Download_Eye_FileText_PenTool_Play_Search_Settings_Sparkles_Square_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"h-10 w-10 text-cyan-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 832,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-slate-400\",\n                                                                    children: \"Success Rate\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 834,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-2xl font-bold text-cyan-400\",\n                                                                    children: \"98%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 835,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-cyan-500\",\n                                                                    children: \"Mission completion\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 836,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 833,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                    lineNumber: 831,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 830,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 796,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                lineNumber: 790,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-slate-800/50 to-slate-900/50 rounded-2xl border border-blue-500/20 backdrop-blur-sm p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold mb-6\",\n                                        children: \"Agent Squadron Analytics\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 845,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: teamAnalytics.agents.map((agent, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-6 bg-slate-800/30 border border-slate-700/50 rounded-xl\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-3 h-3 rounded-full bg-green-400 animate-pulse\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 850,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-bold text-white\",\n                                                                        children: agent.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                        lineNumber: 852,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-slate-400\",\n                                                                        children: agent.specialization\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                        lineNumber: 853,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-slate-500\",\n                                                                        children: [\n                                                                            \"Tools: \",\n                                                                            agent.tools.join(', ')\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                        lineNumber: 854,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 851,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 849,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium text-blue-400\",\n                                                                children: agent.performance\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 858,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-slate-400\",\n                                                                children: agent.avgExecutionTime\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 859,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mt-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-slate-500\",\n                                                                        children: \"Power:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                        lineNumber: 861,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs font-bold text-green-400\",\n                                                                        children: [\n                                                                            agent.powerLevel,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                        lineNumber: 862,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 860,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 857,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 848,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 846,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                lineNumber: 844,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                        lineNumber: 777,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                lineNumber: 466,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n        lineNumber: 431,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/content-writer/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/SessionProvider.tsx":
/*!********************************************!*\
  !*** ./src/components/SessionProvider.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SessionProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction SessionProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SessionProvider.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9TZXNzaW9uUHJvdmlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUU0RTtBQU83RCxTQUFTQSxnQkFBZ0IsRUFBRUUsUUFBUSxFQUF3QjtJQUN4RSxxQkFDRSw4REFBQ0QsNERBQXVCQTtrQkFDckJDOzs7Ozs7QUFHUCIsInNvdXJjZXMiOlsiL1VzZXJzL2FheXVzaG1pc2hyYS9EZXNrdG9wL29sZCBpbnZpbmNpYmxlIHdpdGggZGVlcHJlc2VhcmNoL3NyYy9jb21wb25lbnRzL1Nlc3Npb25Qcm92aWRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IFNlc3Npb25Qcm92aWRlciBhcyBOZXh0QXV0aFNlc3Npb25Qcm92aWRlciB9IGZyb20gJ25leHQtYXV0aC9yZWFjdCdcbmltcG9ydCB7IFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0J1xuXG5pbnRlcmZhY2UgU2Vzc2lvblByb3ZpZGVyUHJvcHMge1xuICBjaGlsZHJlbjogUmVhY3ROb2RlXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFNlc3Npb25Qcm92aWRlcih7IGNoaWxkcmVuIH06IFNlc3Npb25Qcm92aWRlclByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPE5leHRBdXRoU2Vzc2lvblByb3ZpZGVyPlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvTmV4dEF1dGhTZXNzaW9uUHJvdmlkZXI+XG4gIClcbn0gIl0sIm5hbWVzIjpbIlNlc3Npb25Qcm92aWRlciIsIk5leHRBdXRoU2Vzc2lvblByb3ZpZGVyIiwiY2hpbGRyZW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SessionProvider.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/lucide-react","vendor-chunks/@babel","vendor-chunks/mdast-util-to-hast","vendor-chunks/micromark-core-commonmark","vendor-chunks/property-information","vendor-chunks/micromark","vendor-chunks/micromark-util-symbol","vendor-chunks/@ungap","vendor-chunks/debug","vendor-chunks/vfile","vendor-chunks/unist-util-visit-parents","vendor-chunks/unified","vendor-chunks/micromark-util-subtokenize","vendor-chunks/style-to-js","vendor-chunks/vfile-message","vendor-chunks/unist-util-visit","vendor-chunks/unist-util-stringify-position","vendor-chunks/unist-util-position","vendor-chunks/unist-util-is","vendor-chunks/trough","vendor-chunks/trim-lines","vendor-chunks/space-separated-tokens","vendor-chunks/remark-rehype","vendor-chunks/remark-parse","vendor-chunks/react-markdown","vendor-chunks/micromark-util-sanitize-uri","vendor-chunks/micromark-util-resolve-all","vendor-chunks/micromark-util-normalize-identifier","vendor-chunks/micromark-util-html-tag-name","vendor-chunks/micromark-util-encode","vendor-chunks/micromark-util-decode-string","vendor-chunks/micromark-util-decode-numeric-character-reference","vendor-chunks/micromark-util-combine-extensions","vendor-chunks/micromark-util-classify-character","vendor-chunks/micromark-util-chunked","vendor-chunks/micromark-util-character","vendor-chunks/micromark-factory-whitespace","vendor-chunks/micromark-factory-title","vendor-chunks/micromark-factory-space","vendor-chunks/micromark-factory-label","vendor-chunks/micromark-factory-destination","vendor-chunks/mdast-util-to-string","vendor-chunks/mdast-util-from-markdown","vendor-chunks/is-plain-obj","vendor-chunks/html-url-attributes","vendor-chunks/hast-util-whitespace","vendor-chunks/hast-util-to-jsx-runtime","vendor-chunks/estree-util-is-identifier-name","vendor-chunks/devlop","vendor-chunks/dequal","vendor-chunks/decode-named-character-reference","vendor-chunks/comma-separated-tokens","vendor-chunks/character-entities","vendor-chunks/bail","vendor-chunks/supports-color","vendor-chunks/style-to-object","vendor-chunks/ms","vendor-chunks/inline-style-parser","vendor-chunks/has-flag","vendor-chunks/extend"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fcontent-writer%2Fpage&page=%2Fcontent-writer%2Fpage&appPaths=%2Fcontent-writer%2Fpage&pagePath=private-next-app-dir%2Fcontent-writer%2Fpage.tsx&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();