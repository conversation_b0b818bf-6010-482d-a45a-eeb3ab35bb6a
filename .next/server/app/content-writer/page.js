/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/content-writer/page";
exports.ids = ["app/content-writer/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fcontent-writer%2Fpage&page=%2Fcontent-writer%2Fpage&appPaths=%2Fcontent-writer%2Fpage&pagePath=private-next-app-dir%2Fcontent-writer%2Fpage.tsx&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fcontent-writer%2Fpage&page=%2Fcontent-writer%2Fpage&appPaths=%2Fcontent-writer%2Fpage&pagePath=private-next-app-dir%2Fcontent-writer%2Fpage.tsx&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/content-writer/page.tsx */ \"(rsc)/./src/app/content-writer/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'content-writer',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/content-writer/page\",\n        pathname: \"/content-writer\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fcontent-writer%2Fpage&page=%2Fcontent-writer%2Fpage&appPaths=%2Fcontent-writer%2Fpage&pagePath=private-next-app-dir%2Fcontent-writer%2Fpage.tsx&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcomponents%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcomponents%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/SessionProvider.tsx */ \"(rsc)/./src/components/SessionProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmZvbnQlMkZnb29nbGUlMkZ0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMnNyYyUyRmFwcCUyRmxheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGc3JjJTJGYXBwJTJGZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZhYXl1c2htaXNocmElMkZEZXNrdG9wJTJGb2xkJTIwaW52aW5jaWJsZSUyMHdpdGglMjBkZWVwcmVzZWFyY2glMkZzcmMlMkZjb21wb25lbnRzJTJGU2Vzc2lvblByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9MQUFpSyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIi9Vc2Vycy9hYXl1c2htaXNocmEvRGVza3RvcC9vbGQgaW52aW5jaWJsZSB3aXRoIGRlZXByZXNlYXJjaC9zcmMvY29tcG9uZW50cy9TZXNzaW9uUHJvdmlkZXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcomponents%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fcontent-writer%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fcontent-writer%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/content-writer/page.tsx */ \"(rsc)/./src/app/content-writer/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGc3JjJTJGYXBwJTJGY29udGVudC13cml0ZXIlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOEtBQWlJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvYWF5dXNobWlzaHJhL0Rlc2t0b3Avb2xkIGludmluY2libGUgd2l0aCBkZWVwcmVzZWFyY2gvc3JjL2FwcC9jb250ZW50LXdyaXRlci9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fcontent-writer%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/content-writer/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/content-writer/page.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"333781e81268\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMvYWF5dXNobWlzaHJhL0Rlc2t0b3Avb2xkIGludmluY2libGUgd2l0aCBkZWVwcmVzZWFyY2gvc3JjL2FwcC9nbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjMzMzc4MWU4MTI2OFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_SessionProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/SessionProvider */ \"(rsc)/./src/components/SessionProvider.tsx\");\n\n\n\n\nconst metadata = {\n    title: 'Invincible - AI Content Generation Platform',\n    description: 'The ultimate content writing SaaS platform powered by advanced AI technology'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"dark\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className)} bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 min-h-screen`,\n            suppressHydrationWarning: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SessionProvider__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBS01BO0FBSGdCO0FBQ29DO0FBSW5ELE1BQU1FLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7UUFBS0MsV0FBVTtrQkFDeEIsNEVBQUNDO1lBQ0NELFdBQVcsR0FBR1QsK0pBQWUsQ0FBQywwRUFBMEUsQ0FBQztZQUN6R1csMEJBQTBCO3NCQUUxQiw0RUFBQ1YsbUVBQWVBOzBCQUNkLDRFQUFDVztvQkFBSUgsV0FBVTs4QkFDWkg7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU1iIiwic291cmNlcyI6WyIvVXNlcnMvYWF5dXNobWlzaHJhL0Rlc2t0b3Avb2xkIGludmluY2libGUgd2l0aCBkZWVwcmVzZWFyY2gvc3JjL2FwcC9sYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0J1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tICduZXh0L2ZvbnQvZ29vZ2xlJ1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuaW1wb3J0IFNlc3Npb25Qcm92aWRlciBmcm9tICdAL2NvbXBvbmVudHMvU2Vzc2lvblByb3ZpZGVyJ1xuXG5jb25zdCBpbnRlciA9IEludGVyKHsgc3Vic2V0czogWydsYXRpbiddIH0pXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnSW52aW5jaWJsZSAtIEFJIENvbnRlbnQgR2VuZXJhdGlvbiBQbGF0Zm9ybScsXG4gIGRlc2NyaXB0aW9uOiAnVGhlIHVsdGltYXRlIGNvbnRlbnQgd3JpdGluZyBTYWFTIHBsYXRmb3JtIHBvd2VyZWQgYnkgYWR2YW5jZWQgQUkgdGVjaG5vbG9neScsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiIGNsYXNzTmFtZT1cImRhcmtcIj5cbiAgICAgIDxib2R5IFxuICAgICAgICBjbGFzc05hbWU9e2Ake2ludGVyLmNsYXNzTmFtZX0gYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1zbGF0ZS05MDAgdmlhLXB1cnBsZS05MDAgdG8tc2xhdGUtOTAwIG1pbi1oLXNjcmVlbmB9XG4gICAgICAgIHN1cHByZXNzSHlkcmF0aW9uV2FybmluZz17dHJ1ZX1cbiAgICAgID5cbiAgICAgICAgPFNlc3Npb25Qcm92aWRlcj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlblwiPlxuICAgICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L1Nlc3Npb25Qcm92aWRlcj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn0gIl0sIm5hbWVzIjpbImludGVyIiwiU2Vzc2lvblByb3ZpZGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJjbGFzc05hbWUiLCJib2R5Iiwic3VwcHJlc3NIeWRyYXRpb25XYXJuaW5nIiwiZGl2Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/SessionProvider.tsx":
/*!********************************************!*\
  !*** ./src/components/SessionProvider.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SessionProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SessionProvider.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcomponents%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcomponents%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/SessionProvider.tsx */ \"(ssr)/./src/components/SessionProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmZvbnQlMkZnb29nbGUlMkZ0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMnNyYyUyRmFwcCUyRmxheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGc3JjJTJGYXBwJTJGZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZhYXl1c2htaXNocmElMkZEZXNrdG9wJTJGb2xkJTIwaW52aW5jaWJsZSUyMHdpdGglMjBkZWVwcmVzZWFyY2glMkZzcmMlMkZjb21wb25lbnRzJTJGU2Vzc2lvblByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9MQUFpSyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIi9Vc2Vycy9hYXl1c2htaXNocmEvRGVza3RvcC9vbGQgaW52aW5jaWJsZSB3aXRoIGRlZXByZXNlYXJjaC9zcmMvY29tcG9uZW50cy9TZXNzaW9uUHJvdmlkZXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcomponents%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fcontent-writer%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fcontent-writer%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/content-writer/page.tsx */ \"(ssr)/./src/app/content-writer/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGc3JjJTJGYXBwJTJGY29udGVudC13cml0ZXIlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOEtBQWlJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvYWF5dXNobWlzaHJhL0Rlc2t0b3Avb2xkIGludmluY2libGUgd2l0aCBkZWVwcmVzZWFyY2gvc3JjL2FwcC9jb250ZW50LXdyaXRlci9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fcontent-writer%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/content-writer/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/content-writer/page.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ InvincibleContentWriter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/pen-tool.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Brain,CheckCircle,Clock,Cpu,DollarSign,Download,Eye,FileText,Globe,PenTool,Play,RefreshCw,Search,Settings,Shield,Sparkles,Square,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! react-markdown */ \"(ssr)/./node_modules/react-markdown/lib/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n// Enhanced team analytics with Invincible theme\nconst getTeamAnalytics = ()=>({\n        agents: [\n            {\n                name: 'Research Agent',\n                specialization: 'Web Research & Data Collection',\n                tools: [\n                    'Tavily Search',\n                    'SERP Analysis'\n                ],\n                performance: 'Excellent',\n                avgExecutionTime: '45s',\n                powerLevel: 95,\n                specialAbility: 'Deep Web Intelligence'\n            },\n            {\n                name: 'SEO Strategist',\n                specialization: 'Keyword Research & Strategy',\n                tools: [\n                    'Keyword Analysis',\n                    'Competition Research'\n                ],\n                performance: 'Very Good',\n                avgExecutionTime: '30s',\n                powerLevel: 88,\n                specialAbility: 'Search Domination'\n            },\n            {\n                name: 'Content Architect',\n                specialization: 'Content Structure & Planning',\n                tools: [\n                    'Outline Generator',\n                    'Content Planner'\n                ],\n                performance: 'Excellent',\n                avgExecutionTime: '25s',\n                powerLevel: 92,\n                specialAbility: 'Structure Mastery'\n            },\n            {\n                name: 'Content Creator',\n                specialization: 'AI-Powered Writing',\n                tools: [\n                    'Kimi K2 Model',\n                    'Content Generator'\n                ],\n                performance: 'Outstanding',\n                avgExecutionTime: '2m',\n                powerLevel: 98,\n                specialAbility: 'Creative Genesis'\n            },\n            {\n                name: 'Quality Editor',\n                specialization: 'Content Review & Optimization',\n                tools: [\n                    'Grammar Check',\n                    'SEO Optimization'\n                ],\n                performance: 'Excellent',\n                avgExecutionTime: '40s',\n                powerLevel: 90,\n                specialAbility: 'Perfection Protocol'\n            }\n        ],\n        capabilities: {\n            content_types: [\n                'Blog Posts',\n                'Articles',\n                'Landing Pages',\n                'Social Media',\n                'Email Campaigns',\n                'Product Descriptions'\n            ],\n            word_count_range: '500-5000',\n            seo_optimization: 'Real-time SEO scoring and recommendations',\n            fact_checking: 'Automated fact-checking with source verification',\n            brand_voice: 'Adaptive brand voice matching'\n        },\n        workflows: [\n            {\n                name: 'Standard Content Creation',\n                agents: 5,\n                tasks: 5,\n                estimatedTime: '4-6 minutes',\n                costEstimate: '$0.40-0.80',\n                success_rate: '98%'\n            },\n            {\n                name: 'SEO-Focused Content',\n                agents: 5,\n                tasks: 7,\n                estimatedTime: '6-8 minutes',\n                costEstimate: '$0.60-1.00',\n                success_rate: '96%'\n            },\n            {\n                name: 'Technical Content',\n                agents: 5,\n                tasks: 6,\n                estimatedTime: '8-12 minutes',\n                costEstimate: '$0.80-1.20',\n                success_rate: '94%'\n            }\n        ]\n    });\nconst validateTeamConfiguration = ()=>({\n        isValid: true,\n        issues: []\n    });\nfunction InvincibleContentWriter() {\n    const [projects, setProjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentProject, setCurrentProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [newProjectTopic, setNewProjectTopic] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [targetKeywords, setTargetKeywords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [contentType, setContentType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('blog-post');\n    const [wordCount, setWordCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(2000);\n    const [isRunning, setIsRunning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [totalCost, setTotalCost] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [previewContent, setPreviewContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [teamAnalytics, setTeamAnalytics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(getTeamAnalytics());\n    const [workflowError, setWorkflowError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('command-center');\n    const [topic, setTopic] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [keywords, setKeywords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [targetLength, setTargetLength] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(2000);\n    // Enhanced UI states\n    const [systemStatus, setSystemStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('READY');\n    const [powerLevel, setPowerLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(100);\n    // Mock current task data\n    const getOverallProgress = ()=>{\n        if (!currentProject || !currentProject.steps || currentProject.steps.length === 0) return 0;\n        const completedSteps = currentProject.steps.filter((s)=>s.status === 'completed').length;\n        return completedSteps / currentProject.steps.length * 100;\n    };\n    // Current task computed state\n    const currentTask = {\n        topic: currentProject?.topic || topic || '',\n        status: currentProject?.status || 'pending',\n        progress: getOverallProgress()\n    };\n    // Enhanced agents data with power levels\n    const [agents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 'Research Agent',\n            name: 'Research Agent',\n            status: 'idle',\n            progress: 0,\n            currentTask: '',\n            cost: 0,\n            powerLevel: 95,\n            specialAbility: 'Deep Web Intelligence'\n        },\n        {\n            id: 'SEO Strategist',\n            name: 'SEO Strategist',\n            status: 'idle',\n            progress: 0,\n            currentTask: '',\n            cost: 0,\n            powerLevel: 88,\n            specialAbility: 'Search Domination'\n        },\n        {\n            id: 'Content Architect',\n            name: 'Content Architect',\n            status: 'idle',\n            progress: 0,\n            currentTask: '',\n            cost: 0,\n            powerLevel: 92,\n            specialAbility: 'Structure Mastery'\n        },\n        {\n            id: 'Content Creator',\n            name: 'Content Creator',\n            status: 'idle',\n            progress: 0,\n            currentTask: '',\n            cost: 0,\n            powerLevel: 98,\n            specialAbility: 'Creative Genesis'\n        },\n        {\n            id: 'Quality Editor',\n            name: 'Quality Editor',\n            status: 'idle',\n            progress: 0,\n            currentTask: '',\n            cost: 0,\n            powerLevel: 90,\n            specialAbility: 'Perfection Protocol'\n        }\n    ]);\n    const agentIcons = {\n        'Research Agent': _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        'SEO Strategist': _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        'Content Architect': _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        'Content Creator': _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        'Quality Editor': _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    };\n    // Placeholder functions\n    const stopGeneration = ()=>{\n        setIsRunning(false);\n        setSystemStatus('STOPPED');\n    };\n    const resetWorkflow = ()=>{\n        setIsRunning(false);\n        setCurrentProject(null);\n        setTotalCost(0);\n        setPreviewContent('');\n        setWorkflowError(null);\n        setSystemStatus('READY');\n        setPowerLevel(100);\n    };\n    const startContentGeneration = async ()=>{\n        if (!topic.trim()) {\n            alert('Please enter a topic first');\n            return;\n        }\n        setSystemStatus('INITIALIZING');\n        setPowerLevel(95);\n        // Create a new project with steps\n        const project = {\n            id: Date.now().toString(),\n            topic: topic,\n            targetKeywords: keywords.split(',').map((k)=>k.trim()).filter(Boolean),\n            contentType,\n            wordCount: targetLength,\n            status: 'draft',\n            createdAt: new Date(),\n            steps: [\n                {\n                    id: '1',\n                    name: 'Research & Data Collection',\n                    status: 'pending',\n                    progress: 0,\n                    agent: 'Research Agent'\n                },\n                {\n                    id: '2',\n                    name: 'SEO Strategy & Keywords',\n                    status: 'pending',\n                    progress: 0,\n                    agent: 'SEO Strategist'\n                },\n                {\n                    id: '3',\n                    name: 'Content Architecture',\n                    status: 'pending',\n                    progress: 0,\n                    agent: 'Content Architect'\n                },\n                {\n                    id: '4',\n                    name: 'Content Creation',\n                    status: 'pending',\n                    progress: 0,\n                    agent: 'Content Creator'\n                },\n                {\n                    id: '5',\n                    name: 'Quality Review & Optimization',\n                    status: 'pending',\n                    progress: 0,\n                    agent: 'Quality Editor'\n                }\n            ]\n        };\n        setCurrentProject(project);\n        setProjects((prev)=>[\n                project,\n                ...prev\n            ]);\n        setIsRunning(true);\n        setSystemStatus('ACTIVE');\n        setWorkflowError(null);\n        try {\n            const validation = validateTeamConfiguration();\n            if (!validation.isValid) {\n                throw new Error(`Configuration errors: ${validation.issues.join(', ')}`);\n            }\n            const updatedProject = {\n                ...project,\n                status: 'in_progress'\n            };\n            setCurrentProject(updatedProject);\n            for(let i = 0; i < updatedProject.steps.length; i++){\n                const step = updatedProject.steps[i];\n                step.status = 'running';\n                setSystemStatus(`EXECUTING: ${step.agent.toUpperCase()}`);\n                setCurrentProject({\n                    ...updatedProject\n                });\n                const executionTimes = {\n                    'Research Agent': 45000,\n                    'SEO Strategist': 30000,\n                    'Content Architect': 25000,\n                    'Content Creator': 120000,\n                    'Quality Editor': 40000\n                };\n                const stepTime = executionTimes[step.agent] || 30000;\n                const progressSteps = 20;\n                const intervalTime = stepTime / progressSteps;\n                for(let progress = 0; progress <= 100; progress += 5){\n                    step.progress = progress;\n                    setPowerLevel(Math.max(70, 100 - progress * 0.3));\n                    setCurrentProject({\n                        ...updatedProject\n                    });\n                    await new Promise((resolve)=>setTimeout(resolve, intervalTime / 20));\n                }\n                step.status = 'completed';\n                const costs = {\n                    'Research Agent': 0.08,\n                    'SEO Strategist': 0.05,\n                    'Content Architect': 0.04,\n                    'Content Creator': 0.25,\n                    'Quality Editor': 0.06\n                };\n                step.cost = costs[step.agent] || 0.05;\n                step.duration = stepTime / 1000;\n                if (step.agent === 'Content Creator') {\n                    step.output = generateSampleContent(updatedProject.topic);\n                    setPreviewContent(step.output);\n                }\n                setTotalCost((prev)=>prev + (step.cost || 0));\n                setPowerLevel(100);\n            }\n            updatedProject.status = 'completed';\n            setCurrentProject(updatedProject);\n            setProjects((prev)=>prev.map((p)=>p.id === updatedProject.id ? updatedProject : p));\n            setSystemStatus('COMPLETED');\n        } catch (error) {\n            console.error('Workflow execution failed:', error);\n            setWorkflowError(error instanceof Error ? error.message : 'Unknown error occurred');\n            setSystemStatus('ERROR');\n        } finally{\n            setIsRunning(false);\n        }\n    };\n    const generateSampleContent = (topic)=>{\n        return `# ${topic}\n\n## Introduction\n\nThis comprehensive guide explores ${topic} and provides valuable insights for readers seeking to understand this important subject.\n\n## Key Points\n\n- **Research-backed insights**: Our analysis is based on the latest industry research and expert opinions\n- **Practical applications**: Real-world examples and actionable strategies\n- **Future trends**: What to expect in the evolving landscape\n\n## Detailed Analysis\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.\n\n### Section 1: Understanding the Basics\n\nDetailed explanation of fundamental concepts and principles.\n\n### Section 2: Advanced Strategies  \n\nMore sophisticated approaches and methodologies.\n\n## Conclusion\n\nThis content has been optimized for search engines while maintaining high readability and engagement for human readers.\n\n*Generated by Invincible AI Content Team - Powered by KaibanJS & Kimi K2*`;\n    };\n    const exportContent = (format)=>{\n        if (!previewContent) {\n            alert('No content to export. Please generate content first.');\n            return;\n        }\n        let content = '';\n        let filename = '';\n        let mimeType = '';\n        switch(format){\n            case 'md':\n                content = previewContent;\n                filename = `${topic?.replace(/\\s+/g, '-').toLowerCase() || 'content'}.md`;\n                mimeType = 'text/markdown';\n                break;\n            case 'html':\n                content = `<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>${topic || 'Generated Content'}</title>\n    <style>\n        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; max-width: 800px; margin: 0 auto; padding: 20px; }\n        h1, h2, h3 { color: #333; }\n        p { margin-bottom: 16px; }\n        ul, ol { margin-bottom: 16px; padding-left: 20px; }\n    </style>\n</head>\n<body>\n    ${previewContent.replace(/\\n/g, '<br>')}\n</body>\n</html>`;\n                filename = `${topic?.replace(/\\s+/g, '-').toLowerCase() || 'content'}.html`;\n                mimeType = 'text/html';\n                break;\n            case 'txt':\n                content = previewContent.replace(/[#*`]/g, '').replace(/\\n+/g, '\\n');\n                filename = `${topic?.replace(/\\s+/g, '-').toLowerCase() || 'content'}.txt`;\n                mimeType = 'text/plain';\n                break;\n        }\n        const blob = new Blob([\n            content\n        ], {\n            type: mimeType\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = filename;\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n    };\n    const copyToClipboard = async ()=>{\n        if (!previewContent) return;\n        try {\n            await navigator.clipboard.writeText(previewContent);\n            alert('Content copied to clipboard!');\n        } catch (err) {\n            console.error('Failed to copy content:', err);\n            alert('Failed to copy content to clipboard');\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-blue-500/30 bg-slate-900/80 backdrop-blur-lg sticky top-0 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-6 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-10 w-10 text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 444,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 442,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl font-bold bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent\",\n                                                children: \"INVINCIBLE AI WRITER\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 447,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-slate-400\",\n                                                children: \"Multi-Agent Content Generation System\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                lineNumber: 441,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium text-blue-400\",\n                                                children: \"SYSTEM STATUS\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-slate-300\",\n                                                children: systemStatus\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 458,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 456,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative w-16 h-16\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 rounded-full bg-gradient-to-r from-blue-500 to-cyan-500 animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 461,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-1 rounded-full bg-slate-900 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-bold text-cyan-400\",\n                                                    children: [\n                                                        powerLevel,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                    lineNumber: 463,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                lineNumber: 455,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                        lineNumber: 440,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                    lineNumber: 439,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                lineNumber: 438,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-6 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-1 bg-slate-800/50 p-2 rounded-xl mb-8 backdrop-blur-sm\",\n                        children: [\n                            {\n                                id: 'command-center',\n                                label: 'Command Center',\n                                icon: _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n                            },\n                            {\n                                id: 'mission-config',\n                                label: 'Mission Config',\n                                icon: _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                            },\n                            {\n                                id: 'content-preview',\n                                label: 'Content Preview',\n                                icon: _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                            },\n                            {\n                                id: 'analytics',\n                                label: 'Analytics',\n                                icon: _barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                            }\n                        ].map(({ id, label, icon: Icon })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(id),\n                                className: `flex items-center space-x-2 px-4 py-3 rounded-lg font-medium transition-all duration-200 ${activeTab === id ? 'bg-gradient-to-r from-blue-500 to-cyan-500 text-white shadow-lg' : 'text-slate-400 hover:text-white hover:bg-slate-700/50'}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: label\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 490,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, id, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                lineNumber: 480,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                        lineNumber: 473,\n                        columnNumber: 9\n                    }, this),\n                    activeTab === 'command-center' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-slate-800/50 to-slate-900/50 rounded-2xl border border-blue-500/20 backdrop-blur-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 border-b border-slate-700/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-6 w-6 text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 502,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl font-bold\",\n                                                        children: \"Mission Control\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 503,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: startContentGeneration,\n                                                        disabled: isRunning || !topic.trim(),\n                                                        className: \"flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 disabled:from-slate-600 disabled:to-slate-700 disabled:cursor-not-allowed rounded-lg font-medium transition-all duration-200 shadow-lg hover:shadow-xl\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 511,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: isRunning ? 'EXECUTING...' : 'INITIATE MISSION'\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 512,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 506,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: stopGeneration,\n                                                        disabled: !isRunning,\n                                                        className: \"flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-red-500 to-rose-500 hover:from-red-600 hover:to-rose-600 disabled:from-slate-600 disabled:to-slate-700 disabled:cursor-not-allowed rounded-lg font-medium transition-all duration-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 519,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"ABORT\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 520,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 514,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: resetWorkflow,\n                                                        className: \"flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 rounded-lg font-medium transition-all duration-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 526,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"RESET\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 527,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 522,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 505,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg font-medium\",\n                                                        children: topic || 'No active mission'\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 535,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `px-3 py-1 rounded-full text-sm font-medium ${currentTask.status === 'completed' ? 'bg-green-500/20 text-green-400' : currentTask.status === 'in_progress' ? 'bg-blue-500/20 text-blue-400' : 'bg-slate-500/20 text-slate-400'}`,\n                                                        children: currentTask.status.toUpperCase()\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 538,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 534,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative h-3 bg-slate-700 rounded-full overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-y-0 left-0 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full transition-all duration-500\",\n                                                        style: {\n                                                            width: `${currentTask.progress}%`\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 548,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 552,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 547,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-sm text-slate-400 mt-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            Math.round(currentTask.progress),\n                                                            \"% Complete\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 556,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Mission Cost: $\",\n                                                            totalCost.toFixed(3)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 557,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 555,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 533,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                lineNumber: 499,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-6 w-6 text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 565,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-bold\",\n                                                children: \"Agent Squadron Status\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 566,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 564,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                        children: agents.map((agent)=>{\n                                            const Icon = agentIcons[agent.id];\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-br from-slate-800/50 to-slate-900/50 rounded-xl border border-slate-700/50 p-6 backdrop-blur-sm hover:border-blue-500/30 transition-all duration-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3 mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: `p-3 rounded-lg ${agent.status === 'running' ? 'bg-blue-500/20 text-blue-400' : agent.status === 'completed' ? 'bg-green-500/20 text-green-400' : agent.status === 'error' ? 'bg-red-500/20 text-red-400' : 'bg-slate-500/20 text-slate-400'}`,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                    className: \"h-6 w-6\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 581,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 575,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-bold text-sm\",\n                                                                        children: agent.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                        lineNumber: 584,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-slate-400\",\n                                                                        children: agent.specialAbility\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                        lineNumber: 585,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 583,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 574,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between text-xs text-slate-400 mb-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Power Level\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                        lineNumber: 592,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            agent.powerLevel,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                        lineNumber: 593,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 591,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-2 bg-slate-700 rounded-full overflow-hidden\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: `h-full rounded-full transition-all duration-500 ${agent.powerLevel >= 95 ? 'bg-gradient-to-r from-green-400 to-emerald-400' : agent.powerLevel >= 85 ? 'bg-gradient-to-r from-blue-400 to-cyan-400' : 'bg-gradient-to-r from-yellow-400 to-orange-400'}`,\n                                                                    style: {\n                                                                        width: `${agent.powerLevel}%`\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 596,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 595,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 590,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-1 bg-slate-700 rounded-full overflow-hidden mb-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `h-full rounded-full transition-all duration-300 ${agent.status === 'completed' ? 'bg-green-400' : agent.status === 'running' ? 'bg-blue-400 animate-pulse' : 'bg-slate-600'}`,\n                                                            style: {\n                                                                width: `${agent.progress}%`\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 609,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 608,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-xs\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: `px-2 py-1 rounded-full ${agent.status === 'running' ? 'bg-blue-500/20 text-blue-400' : agent.status === 'completed' ? 'bg-green-500/20 text-green-400' : agent.status === 'error' ? 'bg-red-500/20 text-red-400' : 'bg-slate-500/20 text-slate-400'}`,\n                                                                children: agent.status.toUpperCase()\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 620,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            agent.cost > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-slate-400\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    agent.cost.toFixed(3)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 628,\n                                                                columnNumber: 44\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 619,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, agent.id, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 573,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 569,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                lineNumber: 563,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                        lineNumber: 497,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'mission-config' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-br from-slate-800/50 to-slate-900/50 rounded-2xl border border-blue-500/20 backdrop-blur-sm p-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-6 w-6 text-blue-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                            lineNumber: 643,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-bold\",\n                                            children: \"Mission Configuration\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                            lineNumber: 644,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                    lineNumber: 642,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-slate-300 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                    className: \"h-4 w-4 inline mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 651,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Primary Target *\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 650,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: topic,\n                                                            onChange: (e)=>setTopic(e.target.value),\n                                                            placeholder: \"Enter your content mission objective...\",\n                                                            className: \"w-full px-4 py-3 bg-slate-800/50 border border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 654,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                    lineNumber: 649,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-slate-300 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"h-4 w-4 inline mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 665,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"SEO Keywords\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 664,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: keywords,\n                                                            onChange: (e)=>setKeywords(e.target.value),\n                                                            placeholder: \"keyword1, keyword2, keyword3...\",\n                                                            className: \"w-full px-4 py-3 bg-slate-800/50 border border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 668,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                    lineNumber: 663,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                            lineNumber: 648,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-slate-300 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    className: \"h-4 w-4 inline mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 681,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Mission Type\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 680,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: contentType,\n                                                            onChange: (e)=>setContentType(e.target.value),\n                                                            className: \"w-full px-4 py-3 bg-slate-800/50 border border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"blog-post\",\n                                                                    children: \"Blog Post\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 689,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"article\",\n                                                                    children: \"Article\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 690,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"landing-page\",\n                                                                    children: \"Landing Page\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 691,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"product-description\",\n                                                                    children: \"Product Description\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 692,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"social-media\",\n                                                                    children: \"Social Media Series\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 693,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 684,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                    lineNumber: 679,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-slate-300 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-4 w-4 inline mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 699,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Target Scope: \",\n                                                                targetLength,\n                                                                \" words\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 698,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"range\",\n                                                            min: \"500\",\n                                                            max: \"5000\",\n                                                            step: \"100\",\n                                                            value: targetLength,\n                                                            onChange: (e)=>setTargetLength(parseInt(e.target.value)),\n                                                            className: \"w-full h-2 bg-slate-700 rounded-lg appearance-none cursor-pointer slider\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 702,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-xs text-slate-400 mt-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 712,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"5000\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 713,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 711,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                    lineNumber: 697,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                            lineNumber: 678,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                    lineNumber: 647,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                            lineNumber: 641,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                        lineNumber: 640,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'content-preview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-br from-slate-800/50 to-slate-900/50 rounded-2xl border border-blue-500/20 backdrop-blur-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border-b border-slate-700/50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-6 w-6 text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 729,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl font-bold\",\n                                                        children: \"Generated Content\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 730,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 728,\n                                                columnNumber: 19\n                                            }, this),\n                                            previewContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>exportContent('md'),\n                                                        className: \"px-4 py-2 bg-slate-700 hover:bg-slate-600 border border-slate-600 rounded-lg text-sm font-medium transition-colors flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 738,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"MD\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 739,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 734,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>exportContent('html'),\n                                                        className: \"px-4 py-2 bg-slate-700 hover:bg-slate-600 border border-slate-600 rounded-lg text-sm font-medium transition-colors flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 745,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"HTML\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 746,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 741,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: copyToClipboard,\n                                                        className: \"px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg text-sm font-medium transition-colors\",\n                                                        children: \"Copy\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 748,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 733,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 727,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                    lineNumber: 726,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-slate-900/50 rounded-xl border border-slate-700/50 p-6 min-h-[400px]\",\n                                        children: previewContent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"prose prose-invert prose-blue max-w-none\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_22__.Markdown, {\n                                                children: previewContent\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 763,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                            lineNumber: 762,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center h-full text-slate-500\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-16 w-16 mx-auto mb-4 opacity-50\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 768,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg\",\n                                                        children: \"Content will materialize here after mission execution\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 769,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm mt-2\",\n                                                        children: \"Configure your mission and initiate the content generation process\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 770,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 767,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                            lineNumber: 766,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 760,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                    lineNumber: 759,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                            lineNumber: 725,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                        lineNumber: 724,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'analytics' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            workflowError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-500/10 border border-red-500/30 rounded-xl p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center text-red-400\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"h-5 w-5 mr-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                            lineNumber: 787,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: \"Mission Error: \"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                            lineNumber: 788,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2\",\n                                            children: workflowError\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                            lineNumber: 789,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                    lineNumber: 786,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                lineNumber: 785,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                className: \"h-6 w-6 text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 797,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-bold\",\n                                                children: \"Performance Metrics\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 798,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 796,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-br from-green-500/10 to-emerald-500/10 border border-green-500/20 rounded-xl p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                            className: \"h-10 w-10 text-green-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 804,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-slate-400\",\n                                                                    children: \"Mission Cost\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 806,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-2xl font-bold text-green-400\",\n                                                                    children: [\n                                                                        \"$\",\n                                                                        totalCost.toFixed(3)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 807,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-green-500\",\n                                                                    children: \"85-95% savings\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 808,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 805,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                    lineNumber: 803,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 802,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-br from-blue-500/10 to-cyan-500/10 border border-blue-500/20 rounded-xl p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                            className: \"h-10 w-10 text-blue-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 815,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-slate-400\",\n                                                                    children: \"Avg. Generation\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 817,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-2xl font-bold text-blue-400\",\n                                                                    children: \"4-6m\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 818,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-blue-500\",\n                                                                    children: \"Per mission\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 819,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 816,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                    lineNumber: 814,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 813,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-br from-purple-500/10 to-pink-500/10 border border-purple-500/20 rounded-xl p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-10 w-10 text-purple-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 826,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-slate-400\",\n                                                                    children: \"Quality Score\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 828,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-2xl font-bold text-purple-400\",\n                                                                    children: \"9.2/10\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 829,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-purple-500\",\n                                                                    children: \"EQ-Bench rating\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 830,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 827,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                    lineNumber: 825,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 824,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-br from-cyan-500/10 to-teal-500/10 border border-cyan-500/20 rounded-xl p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Brain_CheckCircle_Clock_Cpu_DollarSign_Download_Eye_FileText_Globe_PenTool_Play_RefreshCw_Search_Settings_Shield_Sparkles_Square_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"h-10 w-10 text-cyan-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 837,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-slate-400\",\n                                                                    children: \"Success Rate\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 839,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-2xl font-bold text-cyan-400\",\n                                                                    children: \"98%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 840,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-cyan-500\",\n                                                                    children: \"Mission completion\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                    lineNumber: 841,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                            lineNumber: 838,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                    lineNumber: 836,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 835,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 801,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                lineNumber: 795,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-slate-800/50 to-slate-900/50 rounded-2xl border border-blue-500/20 backdrop-blur-sm p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold mb-6\",\n                                        children: \"Agent Squadron Analytics\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 850,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: teamAnalytics.agents.map((agent, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-6 bg-slate-800/30 border border-slate-700/50 rounded-xl\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-3 h-3 rounded-full bg-green-400 animate-pulse\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 855,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-bold text-white\",\n                                                                        children: agent.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                        lineNumber: 857,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-slate-400\",\n                                                                        children: agent.specialization\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                        lineNumber: 858,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-slate-500\",\n                                                                        children: [\n                                                                            \"Tools: \",\n                                                                            agent.tools.join(', ')\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                        lineNumber: 859,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 856,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 854,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium text-blue-400\",\n                                                                children: agent.performance\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 863,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-slate-400\",\n                                                                children: agent.avgExecutionTime\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 864,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mt-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-slate-500\",\n                                                                        children: \"Power:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                        lineNumber: 866,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs font-bold text-green-400\",\n                                                                        children: [\n                                                                            agent.powerLevel,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                        lineNumber: 867,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                                lineNumber: 865,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                        lineNumber: 862,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                                lineNumber: 853,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                        lineNumber: 851,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                                lineNumber: 849,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                        lineNumber: 782,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n                lineNumber: 471,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content-writer/page.tsx\",\n        lineNumber: 436,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/content-writer/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/SessionProvider.tsx":
/*!********************************************!*\
  !*** ./src/components/SessionProvider.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SessionProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction SessionProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SessionProvider.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9TZXNzaW9uUHJvdmlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUU0RTtBQU83RCxTQUFTQSxnQkFBZ0IsRUFBRUUsUUFBUSxFQUF3QjtJQUN4RSxxQkFDRSw4REFBQ0QsNERBQXVCQTtrQkFDckJDOzs7Ozs7QUFHUCIsInNvdXJjZXMiOlsiL1VzZXJzL2FheXVzaG1pc2hyYS9EZXNrdG9wL29sZCBpbnZpbmNpYmxlIHdpdGggZGVlcHJlc2VhcmNoL3NyYy9jb21wb25lbnRzL1Nlc3Npb25Qcm92aWRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IFNlc3Npb25Qcm92aWRlciBhcyBOZXh0QXV0aFNlc3Npb25Qcm92aWRlciB9IGZyb20gJ25leHQtYXV0aC9yZWFjdCdcbmltcG9ydCB7IFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0J1xuXG5pbnRlcmZhY2UgU2Vzc2lvblByb3ZpZGVyUHJvcHMge1xuICBjaGlsZHJlbjogUmVhY3ROb2RlXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFNlc3Npb25Qcm92aWRlcih7IGNoaWxkcmVuIH06IFNlc3Npb25Qcm92aWRlclByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPE5leHRBdXRoU2Vzc2lvblByb3ZpZGVyPlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvTmV4dEF1dGhTZXNzaW9uUHJvdmlkZXI+XG4gIClcbn0gIl0sIm5hbWVzIjpbIlNlc3Npb25Qcm92aWRlciIsIk5leHRBdXRoU2Vzc2lvblByb3ZpZGVyIiwiY2hpbGRyZW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SessionProvider.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/lucide-react","vendor-chunks/mdast-util-to-hast","vendor-chunks/micromark-core-commonmark","vendor-chunks/property-information","vendor-chunks/micromark","vendor-chunks/micromark-util-symbol","vendor-chunks/@ungap","vendor-chunks/debug","vendor-chunks/vfile","vendor-chunks/unist-util-visit-parents","vendor-chunks/unified","vendor-chunks/micromark-util-subtokenize","vendor-chunks/style-to-js","vendor-chunks/vfile-message","vendor-chunks/unist-util-visit","vendor-chunks/unist-util-stringify-position","vendor-chunks/unist-util-position","vendor-chunks/unist-util-is","vendor-chunks/trough","vendor-chunks/trim-lines","vendor-chunks/space-separated-tokens","vendor-chunks/remark-rehype","vendor-chunks/remark-parse","vendor-chunks/react-markdown","vendor-chunks/micromark-util-sanitize-uri","vendor-chunks/micromark-util-resolve-all","vendor-chunks/micromark-util-normalize-identifier","vendor-chunks/micromark-util-html-tag-name","vendor-chunks/micromark-util-encode","vendor-chunks/micromark-util-decode-string","vendor-chunks/micromark-util-decode-numeric-character-reference","vendor-chunks/micromark-util-combine-extensions","vendor-chunks/micromark-util-classify-character","vendor-chunks/micromark-util-chunked","vendor-chunks/micromark-util-character","vendor-chunks/micromark-factory-whitespace","vendor-chunks/micromark-factory-title","vendor-chunks/micromark-factory-space","vendor-chunks/micromark-factory-label","vendor-chunks/micromark-factory-destination","vendor-chunks/mdast-util-to-string","vendor-chunks/mdast-util-from-markdown","vendor-chunks/is-plain-obj","vendor-chunks/html-url-attributes","vendor-chunks/hast-util-whitespace","vendor-chunks/hast-util-to-jsx-runtime","vendor-chunks/estree-util-is-identifier-name","vendor-chunks/devlop","vendor-chunks/dequal","vendor-chunks/decode-named-character-reference","vendor-chunks/comma-separated-tokens","vendor-chunks/character-entities","vendor-chunks/bail","vendor-chunks/supports-color","vendor-chunks/style-to-object","vendor-chunks/ms","vendor-chunks/inline-style-parser","vendor-chunks/has-flag","vendor-chunks/extend"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fcontent-writer%2Fpage&page=%2Fcontent-writer%2Fpage&appPaths=%2Fcontent-writer%2Fpage&pagePath=private-next-app-dir%2Fcontent-writer%2Fpage.tsx&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();