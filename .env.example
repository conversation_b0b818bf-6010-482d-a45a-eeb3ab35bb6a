# KaibanJS Content Writing Team - Environment Variables

# OpenRouter API Key (Required)
# Get your API key from: https://openrouter.ai/keys
# This is used for Kimi K2 model access through OpenRouter
NEXT_PUBLIC_OPENROUTER_API_KEY=your_openrouter_api_key_here

# Tavily API Key (Optional but Recommended)
# Get your API key from: https://app.tavily.com/
# This enables advanced web research capabilities for the Research Agent
NEXT_PUBLIC_TAVILY_API_KEY=your_tavily_api_key_here

# Additional Configuration Options

# Default Content Settings
DEFAULT_CONTENT_TYPE=blog-post
DEFAULT_WORD_COUNT=2000
DEFAULT_BRAND_VOICE=professional

# Performance Settings
WORKFLOW_TIMEOUT=1800000  # 30 minutes in milliseconds
MAX_RETRIES=2
ENABLE_VERBOSE_LOGGING=true

# Cost Management
MAX_COST_PER_ARTICLE=2.00  # Maximum cost threshold per article
ENABLE_COST_ALERTS=true

# Note: After creating your .env.local file with your actual API keys,
# restart your development server for the changes to take effect.