# KaibanJS Content Writing Team - Environment Variables
# Copy this file to .env.local and fill in your API keys

# OpenRouter API Key for Kimi K2 model access
# Get your key from: https://openrouter.ai/keys
NEXT_PUBLIC_OPENROUTER_API_KEY=your_openrouter_api_key_here

# Tavily Search API Key for enhanced web research
# Get your key from: https://tavily.com/
NEXT_PUBLIC_TAVILY_API_KEY=your_tavily_api_key_here

# Optional: Additional configuration
# Uncomment and customize as needed

# Default model configuration
# NEXT_PUBLIC_DEFAULT_MODEL=deepseek/deepseek-chat
# NEXT_PUBLIC_FALLBACK_MODEL=anthropic/claude-3-haiku

# API Configuration
# NEXT_PUBLIC_OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
# NEXT_PUBLIC_REQUEST_TIMEOUT=120000

# Content Generation Settings
# NEXT_PUBLIC_DEFAULT_WORD_COUNT=2000
# NEXT_PUBLIC_MAX_WORD_COUNT=5000
# NEXT_PUBLIC_DEFAULT_CONTENT_TYPE=blog-post

# SEO Settings
# NEXT_PUBLIC_DEFAULT_KEYWORDS_COUNT=10
# NEXT_PUBLIC_MAX_KEYWORDS_COUNT=25

# Quality Settings
# NEXT_PUBLIC_FACT_CHECK_ENABLED=true
# NEXT_PUBLIC_PLAGIARISM_CHECK_ENABLED=true
# NEXT_PUBLIC_READABILITY_TARGET=8

# Performance Settings
# NEXT_PUBLIC_MAX_CONCURRENT_AGENTS=3
# NEXT_PUBLIC_AGENT_TIMEOUT=180000
# NEXT_PUBLIC_RETRY_ATTEMPTS=3

# Analytics and Monitoring
# NEXT_PUBLIC_ANALYTICS_ENABLED=true
# NEXT_PUBLIC_COST_TRACKING_ENABLED=true
# NEXT_PUBLIC_PERFORMANCE_MONITORING=true

# Development Settings
# NEXT_PUBLIC_DEBUG_MODE=false
# NEXT_PUBLIC_VERBOSE_LOGGING=false
# NEXT_PUBLIC_MOCK_MODE=false