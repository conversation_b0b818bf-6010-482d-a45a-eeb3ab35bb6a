# Google OAuth Configuration
GOOGLE_CLIENT_ID=647896132184-j5dvm8f8tkmi7gd1vimfhndnt75ofq03.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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

# NextAuth Configuration
NEXTAUTH_SECRET=akshRDLocPvLiyG3Hhfu/OfHNE6hDKkwOaXCSzO87PU=
NEXTAUTH_URL=http://localhost:3000

# Database
DATABASE_URL="file:./dev.db"

# API Keys (add your keys here)
OPENROUTER_API_KEY=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
GEMINI_API_KEY=AIzaSyCU1qb0b0XEM-B99XUDIRmCfKE3kunbKfY
TAVILY_API_KEY=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
TAVILY_API_KEY_2=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
TAVILY_API_KEY_3=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
GOOGLE_API_KEY=AIzaSyCU1qb0b0XEM-B99XUDIRmCfKE3kunbKfY

# KaibanJS Content Writer API Keys (NEXT_PUBLIC_ prefix for client-side access)
NEXT_PUBLIC_OPENROUTER_API_KEY=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
NEXT_PUBLIC_TAVILY_API_KEY=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

# For KaibanJS (uses OPENAI_API_KEY but points to OpenRouter)
NEXT_PUBLIC_OPENAI_API_KEY=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
